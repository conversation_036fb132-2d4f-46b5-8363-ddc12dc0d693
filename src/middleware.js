import { NextResponse } from 'next/server';

// List of public paths that don't require authentication
const publicPaths = [
  '/login',
  '/register',
  '/set-password',
  '/forgot-password',
  '/activate',
  '/payment/success',
  '/payment/cancel',
];

export function middleware(request) {
  const token = request.cookies.get('token')?.value;
  const path = request.nextUrl.pathname;

  // Check if the path requires authentication
  const isPublicPath = publicPaths.some(publicPath =>
    path === publicPath || path.startsWith(`${publicPath}/`)
  );

  // Redirect to login if accessing a protected route without a token
  if (!isPublicPath && !token) {
    const url = new URL('/login', request.url);
    return NextResponse.redirect(url);
  }

  // Redirect to dashboard if accessing a public path with a token
  if (isPublicPath && token && path !== '/activate') {
    const url = new URL('/dashboard', request.url);
    return NextResponse.redirect(url);
  }

  return NextResponse.next();
}

// Configure middleware to run on specific paths
export const config = {
  matcher: [
    // Match all request paths except for:
    // - _next/static (static files)
    // - _next/image (image optimization files)
    // - favicon.ico (favicon file)
    // - public files (images, etc.)
    '/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
};