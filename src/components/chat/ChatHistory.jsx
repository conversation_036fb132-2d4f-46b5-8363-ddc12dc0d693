"use client";

import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { 
  ClockIcon, 
  XMarkIcon, 
  ChatBubbleLeftIcon,
  TrashIcon 
} from "@heroicons/react/24/outline";
import { chatService } from "@/services/chatService";
import toast from "react-hot-toast";

export default function ChatHistory({ 
  isOpen, 
  onClose, 
  onSelectSession, 
  currentSessionId 
}) {
  const [chatSessions, setChatSessions] = useState([]);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (isOpen) {
      loadChatSessions();
    }
  }, [isOpen]);

  const loadChatSessions = async () => {
    setIsLoading(true);
    try {
      const sessions = await chatService.getAllChatSessions();
      setChatSessions(sessions);
    } catch (error) {
      console.error("Error loading chat sessions:", error);
      toast.error("Failed to load chat history");
    } finally {
      setIsLoading(false);
    }
  };

  const handleSelectSession = (session) => {
    onSelectSession(session);
    onClose();
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now - date);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) {
      return "Today";
    } else if (diffDays === 2) {
      return "Yesterday";
    } else if (diffDays <= 7) {
      return `${diffDays - 1} days ago`;
    } else {
      return date.toLocaleDateString();
    }
  };

  const truncateText = (text, maxLength = 60) => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + "...";
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black bg-opacity-50 flex justify-end z-50"
        onClick={onClose}
      >
        <motion.div
          initial={{ x: "100%" }}
          animate={{ x: 0 }}
          exit={{ x: "100%" }}
          transition={{ type: "spring", damping: 25, stiffness: 200 }}
          className="bg-white w-96 h-full shadow-xl overflow-hidden flex flex-col"
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-gray-50">
            <div className="flex items-center space-x-2">
              <ClockIcon className="w-5 h-5 text-gray-600" />
              <h2 className="text-lg font-semibold text-gray-800">Chat History</h2>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <XMarkIcon className="w-6 h-6" />
            </button>
          </div>

          {/* Content */}
          <div className="flex-1 overflow-y-auto">
            {isLoading ? (
              <div className="flex items-center justify-center h-32">
                <div className="animate-spin h-8 w-8 border-4 border-blue-500 rounded-full border-t-transparent"></div>
              </div>
            ) : chatSessions.length === 0 ? (
              <div className="flex flex-col items-center justify-center h-32 text-gray-500">
                <ChatBubbleLeftIcon className="w-12 h-12 mb-2 text-gray-300" />
                <p className="text-sm">No chat history yet</p>
                <p className="text-xs text-gray-400">Start a conversation to see it here</p>
              </div>
            ) : (
              <div className="p-2">
                {chatSessions.map((session) => (
                  <motion.div
                    key={session.chat_session_id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className={`p-3 mb-2 rounded-lg border cursor-pointer transition-all hover:shadow-md ${
                      currentSessionId === session.chat_session_id
                        ? "bg-blue-50 border-blue-200"
                        : "bg-white border-gray-200 hover:border-gray-300"
                    }`}
                    onClick={() => handleSelectSession(session)}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1 min-w-0">
                        <h3 className="text-sm font-medium text-gray-900 mb-1">
                          {truncateText(session.initial_question)}
                        </h3>
                        <div className="flex items-center space-x-2 text-xs text-gray-500">
                          <span>{formatDate(session.timestamp)}</span>
                          {session.category && (
                            <>
                              <span>•</span>
                              <span className="px-2 py-1 bg-gray-100 rounded-full">
                                {session.category}
                              </span>
                            </>
                          )}
                        </div>
                        <div className="mt-2 text-xs text-gray-400">
                          {session.messages.length} message{session.messages.length !== 1 ? 's' : ''}
                        </div>
                      </div>
                      
                      {currentSessionId === session.chat_session_id && (
                        <div className="ml-2 flex-shrink-0">
                          <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                        </div>
                      )}
                    </div>
                  </motion.div>
                ))}
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="p-4 border-t border-gray-200 bg-gray-50">
            <button
              onClick={loadChatSessions}
              disabled={isLoading}
              className="w-full px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors disabled:opacity-50"
            >
              {isLoading ? "Refreshing..." : "Refresh History"}
            </button>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
}
