"use client";

import { useMemo } from "react";

export default function FormattedMessage({ content }) {
  const formattedContent = useMemo(() => {
    if (!content) return null;

    // Split content into lines for processing
    const lines = content.split('\n');
    const elements = [];
    let currentParagraph = [];
    let inSourcesSection = false;

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();

      // Skip empty lines but track them for paragraph breaks
      if (!line) {
        if (currentParagraph.length > 0) {
          elements.push(createParagraph(currentParagraph.join(' '), elements.length));
          currentParagraph = [];
        }
        continue;
      }

      // Handle headings
      if (line.startsWith('### ')) {
        if (currentParagraph.length > 0) {
          elements.push(createParagraph(currentParagraph.join(' '), elements.length));
          currentParagraph = [];
        }
        const headingText = line.substring(4);
        if (headingText.toLowerCase() === 'sources') {
          inSourcesSection = true;
        }
        elements.push(
          <h3 key={elements.length} className="text-lg font-semibold text-gray-800 mt-4 mb-2 border-b border-gray-200 pb-1">
            {headingText}
          </h3>
        );
        continue;
      }

      if (line.startsWith('## ')) {
        if (currentParagraph.length > 0) {
          elements.push(createParagraph(currentParagraph.join(' '), elements.length));
          currentParagraph = [];
        }
        const headingText = line.substring(3);
        elements.push(
          <h2 key={elements.length} className="text-xl font-bold text-gray-900 mt-6 mb-3 border-b-2 border-blue-200 pb-2">
            {headingText}
          </h2>
        );
        continue;
      }

      // Handle bullet points in sources section
      if (inSourcesSection && line.startsWith('- ')) {
        if (currentParagraph.length > 0) {
          elements.push(createParagraph(currentParagraph.join(' '), elements.length));
          currentParagraph = [];
        }
        const linkText = line.substring(2);
        elements.push(createSourceLink(linkText, elements.length));
        continue;
      }

      // Handle regular bullet points
      if (line.startsWith('- ')) {
        if (currentParagraph.length > 0) {
          elements.push(createParagraph(currentParagraph.join(' '), elements.length));
          currentParagraph = [];
        }
        const bulletText = line.substring(2);
        elements.push(
          <div key={elements.length} className="flex items-start mb-2">
            <span className="text-blue-500 mr-2 mt-1">•</span>
            <span className="flex-1">{parseInlineContent(bulletText)}</span>
          </div>
        );
        continue;
      }

      // Handle numbered lists
      const numberedMatch = line.match(/^(\d+)\.\s+(.+)$/);
      if (numberedMatch) {
        if (currentParagraph.length > 0) {
          elements.push(createParagraph(currentParagraph.join(' '), elements.length));
          currentParagraph = [];
        }
        const [, number, text] = numberedMatch;
        elements.push(
          <div key={elements.length} className="flex items-start mb-2">
            <span className="text-blue-600 font-medium mr-2 mt-1">{number}.</span>
            <span className="flex-1">{parseInlineContent(text)}</span>
          </div>
        );
        continue;
      }

      // Regular text - add to current paragraph
      currentParagraph.push(line);
    }

    // Add any remaining paragraph content
    if (currentParagraph.length > 0) {
      elements.push(createParagraph(currentParagraph.join(' '), elements.length));
    }

    return elements;
  }, [content]);

  // Helper function to create a paragraph with inline content parsing
  function createParagraph(text, key) {
    return (
      <p key={key} className="mb-3 leading-relaxed text-gray-700">
        {parseInlineContent(text)}
      </p>
    );
  }

  // Helper function to create source links
  function createSourceLink(linkText, key) {
    const urlRegex = /(https?:\/\/[^\s]+)/g;
    const match = linkText.match(urlRegex);

    if (match && match[0]) {
      const url = match[0];
      // Extract domain name for display
      let displayText = url;
      try {
        const urlObj = new URL(url);
        displayText = urlObj.hostname.replace('www.', '');
      } catch (e) {
        // If URL parsing fails, use the full URL
        displayText = url;
      }

      return (
        <div key={key} className="mb-3 p-3 bg-blue-50 border-l-4 border-blue-400 rounded-r-lg">
          <a
            href={url}
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center text-blue-700 hover:text-blue-900 font-medium transition-colors duration-200"
          >
            <svg className="w-4 h-4 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
            </svg>
            <span className="break-all">{displayText}</span>
          </a>
          <div className="text-xs text-gray-600 mt-1 break-all">{url}</div>
        </div>
      );
    }

    // If no URL found, treat as regular bullet point
    return (
      <div key={key} className="flex items-start mb-2">
        <span className="text-blue-500 mr-2 mt-1">•</span>
        <span className="flex-1">{parseInlineContent(linkText)}</span>
      </div>
    );
  }

  // Helper function to parse inline content (URLs, bold, etc.)
  function parseInlineContent(text) {
    if (!text) return text;

    // Split text by URLs to handle them separately
    const urlRegex = /(https?:\/\/[^\s]+)/g;
    const parts = text.split(urlRegex);

    return parts.map((part, index) => {
      if (urlRegex.test(part)) {
        // Extract domain name for better display
        let displayText = part;
        try {
          const urlObj = new URL(part);
          displayText = urlObj.hostname.replace('www.', '');
        } catch (e) {
          displayText = part;
        }

        return (
          <a
            key={index}
            href={part}
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center text-blue-600 hover:text-blue-800 underline hover:no-underline transition-colors duration-200 mx-1"
            title={part}
          >
            <svg className="w-3 h-3 mr-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
            </svg>
            {displayText}
          </a>
        );
      }
      return part;
    });
  }

  return (
    <div className="formatted-message">
      {formattedContent}
    </div>
  );
}
