"use client";

import { useState, useEffect, useRef } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { ClockIcon } from "@heroicons/react/24/outline";
import ChatMessage from "./ChatMessage";
import ChatInput from "./ChatInput";
import MemoControls from "./MemoControls";
import EmailModal from "./EmailModal";
import ChatHistory from "./ChatHistory";
import { chatService } from "@/services/chatService";
import { useAuth } from "@/context/AuthContext";
import { useCredit } from "@/context/CreditContext";
import toast from "react-hot-toast";

export default function ChatInterface() {
  const { user } = useAuth();
  const { updateCreditBalance } = useCredit();
  const [messages, setMessages] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [chatSessionId, setChatSessionId] = useState(null);
  const [memoFormat, setMemoFormat] = useState(false);
  const [memoDetails, setMemoDetails] = useState({
    company: "",
    preparedBy: "",
    subject: "",
  });
  const [showEmailModal, setShowEmailModal] = useState(false);
  const [currentMemoUrl, setCurrentMemoUrl] = useState(null);
  const [showChatHistory, setShowChatHistory] = useState(false);
  const messagesEndRef = useRef(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    // Don't auto-load chat history on mount - let user start fresh or choose from history
  }, []);

  // Set up credit update callback for chat service
  useEffect(() => {
    chatService.setCreditUpdateCallback(updateCreditBalance);

    // Cleanup on unmount
    return () => {
      chatService.setCreditUpdateCallback(null);
    };
  }, [updateCreditBalance]);

  const loadChatSession = (session) => {
    setChatSessionId(session.chat_session_id);

    // Convert history to messages format
    const historyMessages = session.messages.flatMap(msg => [
      {
        id: `user-${Date.now()}-${Math.random()}`,
        type: "user",
        content: msg.question,
        timestamp: msg.timestamp,
      },
      {
        id: `assistant-${Date.now()}-${Math.random()}`,
        type: "assistant",
        content: msg.response,
        timestamp: msg.timestamp,
        memoUrl: msg.memo_url,
      },
    ]);

    setMessages(historyMessages);

    // Check if any message has a memo URL to enable the send email button
    const lastMemoUrl = session.messages
      .slice()
      .reverse()
      .find(msg => msg.memo_url)?.memo_url;

    if (lastMemoUrl) {
      setCurrentMemoUrl(lastMemoUrl);
    }
  };

  const handleSendMessage = async (message) => {
    if (!message.trim()) return;

    // Validate memo fields if memo format is enabled
    if (memoFormat) {
      const isFormValid = memoDetails.company.trim() &&
                         memoDetails.preparedBy.trim() &&
                         memoDetails.subject.trim();

      if (!isFormValid) {
        toast.error("Please fill in all required memo fields (Company Name, Prepared By, and Subject)");
        return;
      }
    }

    const userMessage = {
      id: `user-${Date.now()}`,
      type: "user",
      content: message,
      timestamp: new Date().toISOString(),
    };

    setMessages(prev => [...prev, userMessage]);
    setIsLoading(true);

    try {
      const response = await chatService.sendMessage({
        question: message,
        memo_format: memoFormat,
        memo_details: memoFormat ? memoDetails : null,
        chat_session_id: chatSessionId,
      });

      if (response.error) {
        toast.error(response.error);
        return;
      }

      // Update chat session ID if new
      if (response.chat_session_id && !chatSessionId) {
        setChatSessionId(response.chat_session_id);
      }

      const assistantMessage = {
        id: `assistant-${Date.now()}`,
        type: "assistant",
        content: response.answer || response.response || "I apologize, but I couldn't generate a response.",
        timestamp: new Date().toISOString(),
        memoUrl: response.memo_url,
        category: response.category,
      };

      setMessages(prev => [...prev, assistantMessage]);

      // If memo was generated, store the URL for potential email sending
      if (response.memo_url) {
        setCurrentMemoUrl(response.memo_url);
      }

    } catch (error) {
      console.error("Error sending message:", error);

      // Check if it's a credit-related error
      if (error.message && error.message.includes('credit')) {
        toast.error(error.message);
      } else {
        toast.error("Failed to send message. Please try again.");
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleNewChat = () => {
    setMessages([]);
    setChatSessionId(null);
    setCurrentMemoUrl(null);
    setMemoFormat(false);
    setMemoDetails({
      company: "",
      preparedBy: "",
      subject: "",
    });
    toast.success("Started new chat session");
  };

  const handleSendEmail = (memoUrl) => {
    if (!memoUrl) {
      toast.error("No memo available to send");
      return;
    }
    setCurrentMemoUrl(memoUrl);
    setShowEmailModal(true);
  };

  const handleEmailSent = () => {
    setShowEmailModal(false);
    toast.success("Memo sent successfully!");
  };

  return (
    <div className="flex flex-col h-full max-h-[calc(100vh-200px)]">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <div>
          <h1 className="text-2xl font-semibold text-gray-800">
            Tax & Accounting Assistant
          </h1>
          <p className="text-sm text-gray-600">
            Ask questions about Canadian tax and accounting standards
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <button
            onClick={() => setShowChatHistory(true)}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors flex items-center space-x-2"
          >
            <ClockIcon className="w-4 h-4" />
            <span>History</span>
          </button>
          <button
            onClick={handleNewChat}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
          >
            New Chat
          </button>
        </div>
      </div>

      {/* Memo Controls */}
      <MemoControls
        memoFormat={memoFormat}
        setMemoFormat={setMemoFormat}
        memoDetails={memoDetails}
        setMemoDetails={setMemoDetails}
      />


      {/* Messages Container */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        <AnimatePresence>
          {messages.length === 0 ? (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="text-center py-12"
            >
              <div className="text-gray-400 text-lg mb-2"> 🤖 MTA Bot</div>
              <h3 className="text-lg font-medium text-teal-700 mb-2">
                Welcome to Mizuflow Tax & Accounting Assistant
              </h3>
              <p className="text-gray-500 max-w-md mx-auto">
                Ask me anything about Canadian tax regulations, accounting standards (IFRS, ASPE),
                or financial reporting requirements.
              </p>
            </motion.div>
          ) : (
            messages.map((message) => (
              <ChatMessage
                key={message.id}
                message={message}
                onSendEmail={handleSendEmail}
              />
            ))
          )}
        </AnimatePresence>

        {isLoading && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="flex items-center space-x-2 text-gray-500"
          >
            <div className="flex space-x-1">
              <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
              <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: "0.1s" }}></div>
              <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: "0.2s" }}></div>
            </div>
            <span>Assistant is thinking...</span>
          </motion.div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Chat Input */}
      <div className="border-t border-gray-200 p-4">
        <ChatInput onSendMessage={handleSendMessage} disabled={isLoading} />
      </div>

      {/* Email Modal */}
      <EmailModal
        isOpen={showEmailModal}
        onClose={() => setShowEmailModal(false)}
        onEmailSent={handleEmailSent}
        memoUrl={currentMemoUrl}
        memoDetails={memoDetails}
      />

      {/* Chat History Sidebar */}
      <ChatHistory
        isOpen={showChatHistory}
        onClose={() => setShowChatHistory(false)}
        onSelectSession={loadChatSession}
        currentSessionId={chatSessionId}
      />
    </div>
  );
}
