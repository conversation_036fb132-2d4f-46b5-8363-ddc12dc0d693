"use client";

import { motion } from "framer-motion";
import { useState } from "react";
import { ClipboardIcon, CheckIcon, DocumentArrowDownIcon, EnvelopeIcon } from "@heroicons/react/24/outline";
import { chatService } from "@/services/chatService";
import { useAuth } from "@/context/AuthContext";
import toast from "react-hot-toast";
import FormattedMessage from "./FormattedMessage";

export default function ChatMessage({ message, onSendEmail }) {
  const { user } = useAuth();
  const [copied, setCopied] = useState(false);
  const [isDownloading, setIsDownloading] = useState(false);

  // Get user's first initial for avatar
  const getUserInitial = () => {
    if (user?.first_name && user.first_name.trim()) {
      return user.first_name.trim().charAt(0).toUpperCase();
    }
    return 'U'; // Fallback to 'U' if no first name
  };

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(message.content);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error("Failed to copy text:", error);
    }
  };

  const handleDownloadMemo = async () => {
    if (!message.memoUrl) {
      toast.error("No memo URL available");
      return;
    }

    setIsDownloading(true);
    try {
      await chatService.downloadMemo(message.memoUrl);
      toast.success("Memo downloaded successfully");
    } catch (error) {
      console.error("Error downloading memo:", error);
      toast.error("Failed to download memo. Please try again.");
    } finally {
      setIsDownloading(false);
    }
  };

  const handleSendEmail = () => {
    if (message.memoUrl && onSendEmail) {
      onSendEmail(message.memoUrl);
    }
  };

  const formatTimestamp = (timestamp) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'} mb-4`}
    >
      <div className={`max-w-[80%] ${message.type === 'user' ? 'order-2' : 'order-1'}`}>
        {/* Avatar */}
        <div className={`flex items-start space-x-3 ${message.type === 'user' ? 'flex-row-reverse space-x-reverse' : ''}`}>
          <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
            message.type === 'user'
              ? 'bg-blue-500 text-white'
              : 'bg-gray-100 text-gray-600'
          }`}>
            {message.type === 'user' ? getUserInitial() : 'A'}
          </div>

          {/* Message Content */}
          <div className={`flex-1 ${message.type === 'user' ? 'text-right' : 'text-left'}`}>
            <div className={`inline-block p-4 rounded-2xl ${
              message.type === 'user'
                ? 'bg-blue-500 text-white rounded-br-md'
                : 'bg-gray-100 text-gray-800 rounded-bl-md'
            }`}>
              {/* Message Text */}
              {message.type === 'assistant' ? (
                <FormattedMessage content={message.content} />
              ) : (
                <div className="whitespace-pre-wrap break-words">
                  {message.content}
                </div>
              )}

              {/* Category Badge */}
              {message.category && message.type === 'assistant' && (
                <div className="mt-2">
                  <span className="inline-block px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">
                    {message.category}
                  </span>
                </div>
              )}

              {/* Memo Download and Email Buttons */}
              {message.memoUrl && message.type === 'assistant' && (
                <div className="mt-3 pt-3 border-t border-gray-200 space-y-2">
                  <button
                    onClick={handleDownloadMemo}
                    disabled={isDownloading}
                    className={`inline-flex items-center space-x-2 px-3 py-2 text-sm font-medium rounded-lg transition-colors ${
                      isDownloading
                        ? "text-gray-400 bg-gray-100 cursor-not-allowed"
                        : "text-blue-600 bg-blue-50 hover:bg-blue-100"
                    }`}
                  >
                    {isDownloading ? (
                      <>
                        <div className="w-4 h-4 border-2 border-gray-300 border-t-gray-600 rounded-full animate-spin"></div>
                        <span>Downloading...</span>
                      </>
                    ) : (
                      <>
                        <DocumentArrowDownIcon className="w-4 h-4" />
                        <span>Download Memo</span>
                      </>
                    )}
                  </button>
                  <button
                    onClick={handleSendEmail}
                    className="inline-flex items-center space-x-2 px-3 py-2 text-sm font-medium text-green-600 bg-green-50 rounded-lg hover:bg-green-100 transition-colors ml-2"
                  >
                    <EnvelopeIcon className="w-4 h-4" />
                    <span>Send Email</span>
                  </button>
                </div>
              )}
            </div>

            {/* Message Actions */}
            <div className={`flex items-center mt-2 space-x-2 text-xs text-gray-500 ${
              message.type === 'user' ? 'justify-end' : 'justify-start'
            }`}>
              <span>{formatTimestamp(message.timestamp)}</span>

              {message.type === 'assistant' && (
                <button
                  onClick={handleCopy}
                  className="inline-flex items-center space-x-1 px-2 py-1 rounded hover:bg-gray-100 transition-colors"
                  title="Copy message"
                >
                  {copied ? (
                    <>
                      <CheckIcon className="w-3 h-3 text-green-500" />
                      <span className="text-green-500">Copied</span>
                    </>
                  ) : (
                    <>
                      <ClipboardIcon className="w-3 h-3" />
                      <span>Copy</span>
                    </>
                  )}
                </button>
              )}
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  );
}
