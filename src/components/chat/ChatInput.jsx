"use client";

import { useState, useRef, useEffect } from "react";
import { PaperAirplaneIcon, CurrencyDollarIcon } from "@heroicons/react/24/solid";
import { useAuth } from "@/context/AuthContext";

export default function ChatInput({ onSendMessage, disabled = false }) {
  const [message, setMessage] = useState("");
  const textareaRef = useRef(null);
  const { user } = useAuth();

  // Get credit cost from user data (from backend API)
  const creditCost = user?.credits_per_chat_request || 10;

  // Auto-resize textarea
  useEffect(() => {
    const textarea = textareaRef.current;
    if (textarea) {
      // Reset height to auto to get the correct scrollHeight
      textarea.style.height = "auto";

      // Set height based on scrollHeight, with min and max constraints
      const newHeight = Math.min(Math.max(textarea.scrollHeight, 44), 200);
      textarea.style.height = `${newHeight}px`;
    }
  }, [message]);

  const handleSubmit = (e) => {
    e.preventDefault();
    if (message.trim() && !disabled) {
      onSendMessage(message.trim());
      setMessage("");
    }
  };

  const handleKeyDown = (e) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  const handleChange = (e) => {
    setMessage(e.target.value);
  };

  return (
    <form onSubmit={handleSubmit} className="relative">
      <div className="flex items-end space-x-3 p-3 bg-gray-50 rounded-2xl border border-gray-200 focus-within:border-blue-500 focus-within:ring-1 focus-within:ring-blue-500 transition-all">
        {/* Textarea */}
        <div className="flex-1">
          <textarea
            ref={textareaRef}
            value={message}
            onChange={handleChange}
            onKeyDown={handleKeyDown}
            placeholder="Ask me about Canadian tax or accounting standards..."
            disabled={disabled}
            className="w-full resize-none border-0 bg-transparent text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-0 text-sm leading-6"
            style={{
              minHeight: "44px",
              maxHeight: "200px",
            }}
            rows={1}
          />
        </div>

        {/* Send Button */}
        <button
          type="submit"
          disabled={!message.trim() || disabled}
          className={`flex-shrink-0 p-2 rounded-xl transition-all duration-200 ${
            message.trim() && !disabled
              ? "bg-blue-500 text-white hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              : "bg-gray-300 text-gray-500 cursor-not-allowed"
          }`}
        >
          <PaperAirplaneIcon className="w-5 h-5" />
        </button>
      </div>

      {/* Credit cost and tips */}
      <div className="flex items-center justify-between mt-2 px-3">
        <div className="flex items-center space-x-4">
          {/* Credit cost indicator */}
          <div className="flex items-center space-x-1 text-xs text-blue-600 bg-blue-50 px-2 py-1 rounded-md">
            <CurrencyDollarIcon className="w-3 h-3" />
            <span className="font-medium">Cost: {creditCost} credits per request</span>
          </div>

          {/* Character count */}
          <div className="text-xs text-gray-500">
            {message.length > 0 && (
              <span className={message.length > 1000 ? "text-orange-500" : ""}>
                {message.length}/2000
              </span>
            )}
          </div>
        </div>

        <div className="text-xs text-gray-400">
          Press Enter to send, Shift+Enter for new line
        </div>
      </div>
    </form>
  );
}
