"use client";

import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { DocumentTextIcon, ChevronDownIcon, ChevronUpIcon } from "@heroicons/react/24/outline";

export default function MemoControls({
  memoFormat,
  setMemoFormat,
  memoDetails,
  setMemoDetails
}) {
  const [isExpanded, setIsExpanded] = useState(false);

  const handleMemoToggle = () => {
    setMemoFormat(!memoFormat);
    // If turning on memo format, expand the section automatically
    // If turning off memo format, collapse the section
    if (!memoFormat) {
      setIsExpanded(true);
    } else {
      setIsExpanded(false);
    }
  };

  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
  };

  const handleInputChange = (field, value) => {
    setMemoDetails(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const isFormValid = () => {
    return memoDetails.company.trim() &&
           memoDetails.preparedBy.trim() &&
           memoDetails.subject.trim();
  };

  return (
    <div className="border-b border-gray-200 bg-gray-50">
      {/* Memo Format Toggle */}
      <div className="p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <label className="flex items-center space-x-2 cursor-pointer">
              <input
                type="checkbox"
                checked={memoFormat}
                onChange={handleMemoToggle}
                className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
              />
              <div className="flex items-center space-x-2">
                <DocumentTextIcon className="w-5 h-5 text-gray-600" />
                <span className="text-sm font-medium text-teal-700">
                  Generate response in memo format
                </span>
              </div>
            </label>
          </div>

          {/* Expand/Collapse Button - only show when memo format is enabled */}
          {memoFormat && (
            <button
              onClick={toggleExpanded}
              className="flex items-center space-x-1 px-3 py-1 text-sm text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-md transition-colors"
            >
              <span>{isExpanded ? 'Hide Details' : 'Show Details'}</span>
              {isExpanded ? (
                <ChevronUpIcon className="w-4 h-4" />
              ) : (
                <ChevronDownIcon className="w-4 h-4" />
              )}
            </button>
          )}
        </div>

        {/* Status indicator when memo format is enabled but collapsed */}
        {memoFormat && !isExpanded && (
          <div className="px-4 pb-3">
            <div className={`text-xs px-2 py-1 rounded-md ${
              isFormValid()
                ? 'bg-green-50 text-green-700 border border-green-200'
                : 'bg-yellow-50 text-yellow-700 border border-yellow-200'
            }`}>
              {isFormValid()
                ? '✓ Memo details completed'
                : '⚠ Memo details required - click "Show Details" to configure'
              }
            </div>
          </div>
        )}
      </div>

      {/* Memo Details Form */}
      <AnimatePresence>
        {memoFormat && isExpanded && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: "auto", opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="overflow-hidden"
          >
            <div className="px-4 pb-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {/* Company Name */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Company Name <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    value={memoDetails.company}
                    onChange={(e) => handleInputChange("company", e.target.value)}
                    placeholder="Enter company name"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    required
                  />
                </div>

                {/* Prepared By */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Prepared By <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    value={memoDetails.preparedBy}
                    onChange={(e) => handleInputChange("preparedBy", e.target.value)}
                    placeholder="Enter preparer name"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    required
                  />
                </div>

                {/* Subject */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Subject <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    value={memoDetails.subject}
                    onChange={(e) => handleInputChange("subject", e.target.value)}
                    placeholder="Enter memo subject"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    required
                  />
                </div>
              </div>



              {/* Info Text */}
              <div className="mt-3 p-3 bg-teal-50 border border-teal-200 rounded-lg">
                <p className="text-sm text-gray-700">
                  <strong>Note:</strong> When memo format is enabled, the response will be formatted as a professional
                  technical memo document that can be downloaded and shared. All fields marked with <span className="text-red-500">*</span> are required.
                </p>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
