"use client";

import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { XMarkIcon, PlusIcon, TrashIcon, EnvelopeIcon } from "@heroicons/react/24/outline";
import { chatService } from "@/services/chatService";
import toast from "react-hot-toast";

export default function EmailModal({ isOpen, onClose, onEmailSent, memoUrl, memoDetails }) {
  const [emailAddresses, setEmailAddresses] = useState([""]);
  const [subject, setSubject] = useState("");
  const [message, setMessage] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  // Initialize subject when modal opens
  useEffect(() => {
    if (isOpen && memoDetails?.subject) {
      setSubject(`Technical Memo: ${memoDetails.subject}`);
    } else if (isOpen) {
      setSubject("Technical Memo");
    }
  }, [isOpen, memoDetails]);

  const addEmailField = () => {
    setEmailAddresses([...emailAddresses, ""]);
  };

  const removeEmailField = (index) => {
    if (emailAddresses.length > 1) {
      setEmailAddresses(emailAddresses.filter((_, i) => i !== index));
    }
  };

  const updateEmailAddress = (index, value) => {
    const newAddresses = [...emailAddresses];
    newAddresses[index] = value;
    setEmailAddresses(newAddresses);
  };

  const validateEmails = () => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const validEmails = emailAddresses.filter(email => email.trim() && emailRegex.test(email.trim()));
    return validEmails;
  };

  const handleSendEmail = async () => {
    const validEmails = validateEmails();

    if (validEmails.length === 0) {
      toast.error("Please enter at least one valid email address");
      return;
    }

    if (!subject.trim()) {
      toast.error("Please enter a subject");
      return;
    }

    if (!memoUrl) {
      toast.error("No memo available to send");
      return;
    }

    setIsLoading(true);

    try {
      // Extract filename from memo URL
      // The memo URL format is: /tax-acc-assistant/memo-download/{user_folder}/{chat_session_id}/{memo_file}
      // need to extract everything after "memo-download/"
      const memoDownloadIndex = memoUrl.indexOf('memo-download/');
      if (memoDownloadIndex === -1) {
        toast.error("Invalid memo URL format");
        return;
      }

      const filename = memoUrl.substring(memoDownloadIndex + 'memo-download/'.length);

      const response = await chatService.sendMemoEmail({
        memo_filename: filename,
        email_addresses: validEmails,
        subject: subject.trim(),
        message: message.trim() || "Please find the attached technical memo.",
      });

      if (response.success) {
        toast.success(`Email sent to ${response.successful_emails.length} recipient(s)`);
        if (response.failed_emails.length > 0) {
          toast.error(`Failed to send to: ${response.failed_emails.join(", ")}`);
        }
        onEmailSent();
        handleClose();
      } else {
        toast.error("Failed to send email");
      }
    } catch (error) {
      console.error("Error sending email:", error);
      toast.error("Failed to send email. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    setEmailAddresses([""]);
    setSubject("");
    setMessage("");
    setIsLoading(false);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
        onClick={handleClose}
      >
        <motion.div
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.9, opacity: 0 }}
          className="bg-white rounded-xl shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto"
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <div className="flex items-center space-x-2">
              <EnvelopeIcon className="w-6 h-6 text-blue-600" />
              <h2 className="text-lg font-semibold text-gray-900">Send Memo via Email</h2>
            </div>
            <button
              onClick={handleClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <XMarkIcon className="w-6 h-6" />
            </button>
          </div>

          {/* Content */}
          <div className="p-6 space-y-4">
            {/* Email Addresses */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Email Addresses
              </label>
              <div className="space-y-2">
                {emailAddresses.map((email, index) => (
                  <div key={index} className="flex items-center space-x-2">
                    <input
                      type="email"
                      value={email}
                      onChange={(e) => updateEmailAddress(index, e.target.value)}
                      placeholder="Enter email address"
                      className="flex-1 px-3 py-2 border text-gray-700 border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                    {emailAddresses.length > 1 && (
                      <button
                        onClick={() => removeEmailField(index)}
                        className="p-2 text-red-500 hover:text-red-700 transition-colors"
                      >
                        <TrashIcon className="w-4 h-4" />
                      </button>
                    )}
                  </div>
                ))}
              </div>
              <button
                onClick={addEmailField}
                className="mt-2 inline-flex items-center space-x-1 text-sm text-blue-600 hover:text-blue-700 transition-colors"
              >
                <PlusIcon className="w-4 h-4" />
                <span>Add another email</span>
              </button>
            </div>

            {/* Subject */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Subject
              </label>
              <input
                type="text"
                value={subject}
                onChange={(e) => setSubject(e.target.value)}
                placeholder="Enter email subject"
                className="w-full px-3 py-2 border border-gray-300 text-gray-700 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            {/* Message */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Message (Optional)
              </label>
              <textarea
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                placeholder="Enter additional message..."
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 text-gray-700 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
              />
            </div>
          </div>

          {/* Footer */}
          <div className="flex items-center justify-end space-x-3 p-6 border-t border-gray-200">
            <button
              onClick={handleClose}
              disabled={isLoading}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors disabled:opacity-50"
            >
              Cancel
            </button>
            <button
              onClick={handleSendEmail}
              disabled={isLoading || validateEmails().length === 0}
              className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? "Sending..." : "Send Email"}
            </button>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
}
