'use client';

import { useAuth } from '@/context/AuthContext';
import { yupResolver } from '@hookform/resolvers/yup';
import Link from 'next/link';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import * as yup from 'yup';
import Button from '../ui/Button';
import FormInput from '../ui/FormInput';

// Form validation schema
const schema = yup.object().shape({
  email: yup
    .string()
    .email('Please enter a valid email')
    .required('Email is required'),
  password: yup
    .string()
    .required('Password is required')
    .min(8, 'Password must be at least 8 characters'),
  password2: yup
    .string()
    .oneOf([yup.ref('password'), null], 'Passwords must match')
    .required('Confirm password is required'),
  first_name: yup.string().required('First name is required'),
  last_name: yup.string().required('Last name is required'),
});

export default function SetPasswordForm({ initialEmail = '' }) {
  const { setPassword, loading, error, clearError } = useAuth();
  const [serverErrors, setServerErrors] = useState({});
  
  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    setError: setFormError,
  } = useForm({
    resolver: yupResolver(schema),
    defaultValues: {
      email: initialEmail,
    }
  });

  // Clear errors when component mounts and unmounts
  useEffect(() => {
    clearError();
    return () => clearError();
  }, [clearError]);

  // Set email value when initialEmail changes (e.g., from URL params)
  useEffect(() => {
    if (initialEmail) {
      setValue('email', initialEmail);
    }
  }, [initialEmail, setValue]);

  // Process server errors and set them in the form
  useEffect(() => {
    if (error && typeof error === 'object') {
      setServerErrors(error);
      
      // Set field-specific errors in the form
      Object.keys(error).forEach(fieldName => {
        // Skip 'general' error as it's displayed separately
        if (fieldName !== 'general' && fieldName in schema.fields) {
          setFormError(fieldName, {
            type: 'server',
            message: error[fieldName]
          });
        }
      });
    } else {
      setServerErrors({});
    }
  }, [error, setFormError]);

  const onSubmit = async (data) => {
    try {
      setServerErrors({});
      await setPassword(data);
    } catch (error) {
      // Error handling is done in AuthContext and the above useEffect
    }
  };

  return (
    <div className="bg-white p-8 rounded-lg shadow-md w-full max-w-md">
      <h2 className="text-2xl font-bold text-center mb-6 text-gray-800">
        Set Your Password
      </h2>
      
      {serverErrors.general && (
        <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
          {serverErrors.general}
        </div>
      )}
      
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        <div className="mb-4">
          <label 
            htmlFor="email" 
            className="block text-sm font-medium text-gray-700 mb-1"
          >
            Email Address
          </label>
          <input
            id="email"
            name="email"
            type="email"
            className="w-full px-4 py-2 bg-gray-100 border border-gray-300 rounded-md text-gray-700"
            value={initialEmail}
            readOnly
            disabled
            {...register('email')}
          />
          {errors.email && (
            <p className="mt-1 text-sm text-red-600">{errors.email.message}</p>
          )}
          {serverErrors.email && !errors.email && (
            <p className="mt-1 text-sm text-red-600">{serverErrors.email}</p>
          )}
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormInput
            name="first_name"
            label="First Name"
            autoComplete="given-name"
            {...register('first_name')}
            error={errors.first_name || (serverErrors.first_name && { message: serverErrors.first_name })}
          />
          
          <FormInput
            name="last_name"
            label="Last Name"
            autoComplete="family-name"
            {...register('last_name')}
            error={errors.last_name || (serverErrors.last_name && { message: serverErrors.last_name })}
          />
        </div>
        
        <FormInput
          name="password"
          label="Password"
          type="password"
          autoComplete="new-password"
          {...register('password')}
          error={errors.password || (serverErrors.password && { message: serverErrors.password })}
        />
        
        <FormInput
          name="password2"
          label="Confirm Password"
          type="password"
          autoComplete="new-password"
          {...register('password2')}
          error={errors.password2 || (serverErrors.password2 && { message: serverErrors.password2 })}
        />
        
        <Button
          type="submit"
          variant="primary"
          size="md"
          className="w-full"
          isLoading={loading}
        >
          Set Password
        </Button>
        
        <div className="text-center mt-4">
          <p className="text-sm text-gray-600">
            Already have an account?{' '}
            <Link
              href="/login"
              className="text-blue-600 hover:text-blue-500"
              onClick={clearError}
            >
              Log in
            </Link>
          </p>
        </div>
      </form>
    </div>
  );
} 