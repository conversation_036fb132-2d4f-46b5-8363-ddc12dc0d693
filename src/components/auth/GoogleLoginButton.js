'use client';

import { useAuth } from '@/context/AuthContext';
import { GoogleLogin } from '@react-oauth/google';
import { toast } from 'react-hot-toast';

export default function GoogleLoginButton({ text = "Sign in with Google" }) {
  const { googleLogin, loading } = useAuth();

  const handleGoogleSuccess = async (credentialResponse) => {
    try {
      await googleLogin(credentialResponse.credential);
    } catch (error) {
      console.error('Google login failed:', error);
    }
  };

  const handleGoogleError = () => {
    toast.error('Google login failed. Please try again.');
  };

  return (
    <div className="w-full">
      <GoogleLogin
        onSuccess={handleGoogleSuccess}
        onError={handleGoogleError}
        text={text}
        theme="outline"
        size="large"
        width="100%"
        disabled={loading}
      />
    </div>
  );
}
