"use client";

import { useState, useEffect } from 'react';
import { useAuth } from '@/context/AuthContext';
import { useCredit } from '@/context/CreditContext';
import { useSearchParams, useRouter } from 'next/navigation';
import { FaUser, FaCoins, FaHistory, FaEnvelope, FaCalendarAlt, FaShoppingCart } from 'react-icons/fa';
import CreditBalance from './CreditBalance';
import TransactionHistory from './TransactionHistory';
import PurchaseHistory from './PurchaseHistory';

export default function ProfilePage() {
  const { user } = useAuth();
  const { creditBalance } = useCredit();
  const searchParams = useSearchParams();
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('overview');
  const [loading, setLoading] = useState(true);

  // Handle URL parameters for tab selection
  useEffect(() => {
    const tab = searchParams.get('tab');
    if (tab && ['overview', 'credits', 'transactions', 'purchases'].includes(tab)) {
      setActiveTab(tab);
    }
  }, [searchParams]);

  // Debug: Log user data to help troubleshoot date issues
  useEffect(() => {
    if (user) {
      console.log('ProfilePage - User data received:', user);
      console.log('ProfilePage - date_joined value:', user.date_joined);
      console.log('ProfilePage - date_joined type:', typeof user.date_joined);
    }
  }, [user]);

  // Set loading to false when user is loaded
  useEffect(() => {
    if (user) {
      setLoading(false);
    }
  }, [user]);

  // Function to handle tab changes and update URL
  const handleTabChange = (tabId) => {
    setActiveTab(tabId);
    const url = new URL(window.location);
    url.searchParams.set('tab', tabId);
    router.replace(url.pathname + url.search);
  };

  const tabs = [
    { id: 'overview', label: 'Overview', icon: FaUser },
    { id: 'credits', label: 'Credits', icon: FaCoins },
    { id: 'transactions', label: 'Transaction History', icon: FaHistory },
    { id: 'purchases', label: 'Purchase History', icon: FaShoppingCart },
  ];

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-teal-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-teal-50 to-cyan-50 rounded-xl p-6 border border-teal-100">
        <div className="flex items-center gap-4">
          <div className="w-16 h-16 bg-teal-600 rounded-full flex items-center justify-center">
            <FaUser className="text-white text-2xl" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-800">
              {user?.first_name} {user?.last_name}
            </h1>
            <div className="flex items-center gap-4 text-gray-600 mt-1">
              <div className="flex items-center gap-1">
                <FaEnvelope className="text-sm" />
                <span>{user?.email}</span>
              </div>
              <div className="flex items-center gap-1">
                <FaCalendarAlt className="text-sm" />
                <span>Joined {(() => {
                  if (!user?.date_joined) {
                    console.warn('User date_joined is missing:', user);
                    return 'Date not available';
                  }
                  try {
                    const date = new Date(user.date_joined);
                    if (isNaN(date.getTime())) {
                      console.warn('Invalid date_joined format:', user.date_joined);
                      return 'Invalid date format';
                    }
                    return date.toLocaleDateString('en-US', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    });
                  } catch (error) {
                    console.error('Error parsing date_joined:', error, user.date_joined);
                    return 'Date parsing error';
                  }
                })()}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="flex space-x-8">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => handleTabChange(tab.id)}
                className={`flex items-center gap-2 py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-teal-500 text-teal-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Icon className="text-lg" />
                {tab.label}
              </button>
            );
          })}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="mt-6">
        {activeTab === 'overview' && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* User Info Card */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-800 mb-4">Account Information</h3>
              <div className="space-y-3">
                <div>
                  <label className="text-sm font-medium text-gray-500">Full Name</label>
                  <p className="text-gray-800">{user?.first_name} {user?.last_name}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Email Address</label>
                  <p className="text-gray-800">{user?.email}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Account Status</label>
                  <p className={`text-sm font-medium ${user?.is_active ? 'text-green-600' : 'text-red-600'}`}>
                    {user?.is_active ? '✓ Active' : '✗ Inactive'}
                  </p>
                </div>

              </div>
            </div>

            {/* Credit Balance Card */}
            <div className="bg-gradient-to-br from-yellow-50 to-orange-50 rounded-xl shadow-sm border border-yellow-200 p-6">
              <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2">
                <FaCoins className="text-yellow-600" />
                Credit Balance
              </h3>
              <div className="text-center">
                <div className="text-4xl font-bold text-yellow-700 mb-2">
                  {creditBalance !== null ? creditBalance : '---'}
                </div>
                <p className="text-yellow-600 font-medium">Available Credits</p>
                <p className="text-sm text-gray-600 mt-2">
                  Credits are used for AI-powered features across MizuFlow
                </p>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'credits' && (
          <CreditBalance />
        )}

        {activeTab === 'transactions' && (
          <TransactionHistory />
        )}

        {activeTab === 'purchases' && (
          <PurchaseHistory />
        )}
      </div>
    </div>
  );
}
