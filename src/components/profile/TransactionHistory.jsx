"use client";

import { useState, useEffect } from 'react';
import { FaHistory, FaPlus, FaMinus, FaSync, FaFilter } from 'react-icons/fa';
import toast from 'react-hot-toast';

export default function TransactionHistory() {
  const [transactions, setTransactions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState('all'); // all, credit, debit

  useEffect(() => {
    fetchTransactions();
  }, []);

  const fetchTransactions = async () => {
    setLoading(true);
    try {
      const token = document.cookie
        .split('; ')
        .find(row => row.startsWith('token='))
        ?.split('=')[1];

      if (token) {
        const response = await fetch(`${process.env.NEXT_PUBLIC_CURRENT_BACKEND_SERVER}/credit-balance/transactions/`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        });

        if (response.ok) {
          const data = await response.json();
          setTransactions(data.results || data);
        } else {
          toast.error('Failed to fetch transaction history');
        }
      }
    } catch (error) {
      console.error('Error fetching transactions:', error);
      toast.error('Error fetching transaction history');
    } finally {
      setLoading(false);
    }
  };

  const filteredTransactions = transactions.filter(transaction => {
    if (filter === 'all') return true;
    if (filter === 'credit') return transaction.amount > 0;
    if (filter === 'debit') return transaction.amount < 0;
    return true;
  });

  const getTransactionIcon = (amount) => {
    return amount > 0 ? (
      <FaPlus className="text-green-600" />
    ) : (
      <FaMinus className="text-red-600" />
    );
  };

  const getTransactionColor = (amount) => {
    return amount > 0 ? 'text-green-600' : 'text-red-600';
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-teal-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Filter */}
      <div className="flex items-center justify-between">
        <h3 className="text-xl font-semibold text-gray-800 flex items-center gap-2">
          <FaHistory className="text-teal-600" />
          Transaction History
        </h3>

        <div className="flex items-center gap-3">
          {/* Filter */}
          <div className="flex items-center gap-2">
            <FaFilter className="text-gray-500" />
            <select
              value={filter}
              onChange={(e) => setFilter(e.target.value)}
              className="border border-gray-300 rounded-lg text-gray-700 px-3 py-1.5 text-sm focus:outline-none focus:ring-2 focus:ring-teal-500"
            >
              <option value="all">All Transactions</option>
              <option value="credit">Credits Added</option>
              <option value="debit">Credits Used</option>
            </select>
          </div>

          {/* Refresh Button */}
          <button
            onClick={fetchTransactions}
            disabled={loading}
            className="flex items-center gap-2 px-3 py-1.5 text-sm bg-teal-600 text-white rounded-lg hover:bg-teal-700 transition-colors disabled:opacity-50"
          >
            <FaSync className={`${loading ? 'animate-spin' : ''}`} />
            Refresh
          </button>
        </div>
      </div>

      {/* Transactions List */}
      {filteredTransactions.length === 0 ? (
        <div className="text-center py-12">
          <FaHistory className="text-gray-400 text-4xl mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-600 mb-2">No transactions found</h3>
          <p className="text-gray-500">
            {filter === 'all'
              ? 'Your transaction history will appear here once you start using credits.'
              : `No ${filter === 'credit' ? 'credit additions' : 'credit usage'} found.`
            }
          </p>
        </div>
      ) : (
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50 border-b border-gray-200">
                <tr>
                  <th className="text-left py-3 px-4 font-medium text-gray-700">Type</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-700">Amount</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-700">Description</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-700">Balance After</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-700">Date</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {filteredTransactions.map((transaction) => (
                  <tr key={transaction.id} className="hover:bg-gray-50">
                    <td className="py-3 px-4">
                      <div className="flex items-center gap-2">
                        {getTransactionIcon(transaction.amount)}
                        <span className={`font-medium ${getTransactionColor(transaction.amount)}`}>
                          {transaction.amount > 0 ? 'Credit' : 'Debit'}
                        </span>
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      <span className={`font-semibold ${getTransactionColor(transaction.amount)}`}>
                        {transaction.amount > 0 ? '+' : ''}{transaction.amount}
                      </span>
                    </td>
                    <td className="py-3 px-4">
                      <span className="text-gray-700 text-sm">
                        {transaction.description || 'No description'}
                      </span>
                    </td>
                    <td className="py-3 px-4">
                      <span className="font-medium text-gray-800">
                        {transaction.balance_after}
                      </span>
                    </td>
                    <td className="py-3 px-4">
                      <span className="text-gray-600 text-sm">
                        {formatDate(transaction.created_at)}
                      </span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* Summary Stats */}
      {filteredTransactions.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-green-50 rounded-lg p-4 border border-green-200">
            <div className="flex items-center gap-2 mb-2">
              <FaPlus className="text-green-600" />
              <span className="font-medium text-green-800">Total Credits Added</span>
            </div>
            <div className="text-2xl font-bold text-green-700">
              +{transactions.filter(t => t.amount > 0).reduce((sum, t) => sum + t.amount, 0)}
            </div>
          </div>

          <div className="bg-red-50 rounded-lg p-4 border border-red-200">
            <div className="flex items-center gap-2 mb-2">
              <FaMinus className="text-red-600" />
              <span className="font-medium text-red-800">Total Credits Used</span>
            </div>
            <div className="text-2xl font-bold text-red-700">
              {transactions.filter(t => t.amount < 0).reduce((sum, t) => sum + t.amount, 0)}
            </div>
          </div>

          <div className="bg-blue-50 rounded-lg p-4 border border-blue-200">
            <div className="flex items-center gap-2 mb-2">
              <FaHistory className="text-blue-600" />
              <span className="font-medium text-blue-800">Total Transactions</span>
            </div>
            <div className="text-2xl font-bold text-blue-700">
              {transactions.length}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
