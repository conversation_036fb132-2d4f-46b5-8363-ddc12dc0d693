"use client";

import { useEffect, useState } from 'react';
import { FaHistory, FaCoins, FaCalendarAlt, FaCheckCircle, FaClock, FaTimesCircle } from 'react-icons/fa';
import { paymentService } from '@/services/paymentService';
import toast from 'react-hot-toast';

export default function PurchaseHistory() {
  const [purchases, setPurchases] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchPurchaseHistory();
  }, []);

  const fetchPurchaseHistory = async () => {
    try {
      setLoading(true);
      const data = await paymentService.getPurchaseHistory();
      setPurchases(data.results || data || []);
    } catch (error) {
      console.error('Error fetching purchase history:', error);
      setError(error.message);
      toast.error('Failed to load purchase history');
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'completed':
        return <FaCheckCircle className="text-green-500" />;
      case 'pending':
        return <FaClock className="text-yellow-500" />;
      case 'failed':
      case 'canceled':
      case 'expired':
        return <FaTimesCircle className="text-red-500" />;
      default:
        return <FaClock className="text-gray-500" />;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed':
        return 'text-green-700 bg-green-100';
      case 'pending':
        return 'text-yellow-700 bg-yellow-100';
      case 'failed':
      case 'canceled':
      case 'expired':
        return 'text-red-700 bg-red-100';
      default:
        return 'text-gray-700 bg-gray-100';
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <h3 className="text-xl font-semibold text-gray-800 mb-4 flex items-center gap-2">
          <FaHistory className="text-teal-600" />
          Purchase History
        </h3>
        <div className="flex items-center justify-center py-8">
          <div className="w-8 h-8 border-2 border-teal-600 border-t-transparent rounded-full animate-spin"></div>
          <span className="ml-3 text-gray-600">Loading purchase history...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <h3 className="text-xl font-semibold text-gray-800 mb-4 flex items-center gap-2">
          <FaHistory className="text-teal-600" />
          Purchase History
        </h3>
        <div className="text-center py-8">
          <FaTimesCircle className="text-4xl text-red-500 mx-auto mb-4" />
          <p className="text-gray-600 mb-4">Failed to load purchase history</p>
          <button
            onClick={fetchPurchaseHistory}
            className="bg-teal-600 text-white px-4 py-2 rounded-lg hover:bg-teal-700 transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
      <h3 className="text-xl font-semibold text-gray-800 mb-4 flex items-center gap-2">
        <FaHistory className="text-teal-600" />
        Purchase History
      </h3>

      {purchases.length === 0 ? (
        <div className="text-center py-8">
          <FaCoins className="text-4xl text-gray-400 mx-auto mb-4" />
          <p className="text-gray-600 mb-2">No purchases yet</p>
          <p className="text-sm text-gray-500">Your credit purchase history will appear here</p>
        </div>
      ) : (
        <div className="space-y-4">
          {purchases.map((purchase) => (
            <div
              key={purchase.id}
              className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
            >
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center gap-2">
                  {getStatusIcon(purchase.status)}
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(purchase.status)}`}>
                    {purchase.status.charAt(0).toUpperCase() + purchase.status.slice(1)}
                  </span>
                </div>
                <div className="text-sm text-gray-500 flex items-center gap-1">
                  <FaCalendarAlt />
                  {formatDate(purchase.payment_date || purchase.created_at)}
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <p className="text-sm text-gray-600">Credits</p>
                  <p className="font-semibold text-gray-800 flex items-center gap-1">
                    <FaCoins className="text-yellow-600" />
                    {purchase.credits_amount?.toLocaleString() || 'N/A'}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Amount</p>
                  <p className="font-semibold text-gray-800">
                    ${purchase.amount_usd} {purchase.currency?.toUpperCase()}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Transaction ID</p>
                  <p className="font-mono text-xs text-gray-600">
                    #{purchase.id}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
