'use client';

import axios from 'axios';
import Cookies from 'js-cookie';
import api from '@/services/api';
import { toast } from 'react-hot-toast';
import { useRouter } from 'next/navigation';
import { API_ENDPOINTS } from '@/config/api';
import { createContext, useContext, useEffect, useState } from 'react';

// Ensure HTTPS for production environments
const enforceHTTPS = (url) => {
  if (process.env.NODE_ENV === 'production' && url.startsWith('http:')) {
    return url.replace('http:', 'https:');
  }
  return url;
};

// Create Auth Context
const AuthContext = createContext();

// Auth Provider Component
export function AuthProvider({ children }) {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const router = useRouter();

  // Function to clear errors
  const clearError = () => {
    setError(null);
  };

  // Helper function to redirect with error clearing
  const redirectWithClearError = (path) => {
    clearError();
    router.push(path);
  };

  // Check if user is logged in on initial load
  useEffect(() => {
    const checkUserLoggedIn = async () => {
      try {
        const token = Cookies.get('token');
        if (token) {
          // Verify token is valid
          const response = await api.get('/users/me/');
          setUser(response.data);
        }
      } catch (error) {
        console.error('Authentication error:', error);
        // Clear invalid token
        Cookies.remove('token');
        Cookies.remove('refreshToken');
        setUser(null);
      } finally {
        setLoading(false);
      }
    };

    checkUserLoggedIn();
  }, []);

  // Register a new user
  const register = async (email) => {
    setLoading(true);
    setError(null);
    try {
      const response = await axios.post(enforceHTTPS(API_ENDPOINTS.REGISTER), { email });
      toast.success('Registration successful! Please check your email for activation instructions.');
      return response.data;
    } catch (error) {
      // Handle detailed errors from the API
      if (error.response?.data) {
        const errorData = error.response.data;

        // Check if we have field-specific errors
        if (typeof errorData === 'object' && !errorData.detail) {
          // Format field-specific errors
          const fieldErrors = {};
          Object.keys(errorData).forEach(key => {
            fieldErrors[key] = Array.isArray(errorData[key])
              ? errorData[key][0]
              : errorData[key];
          });

          setError(fieldErrors);

          // Show the first error as a toast
          const firstErrorKey = Object.keys(fieldErrors)[0];
          if (firstErrorKey) {
            toast.error(`${firstErrorKey}: ${fieldErrors[firstErrorKey]}`);
          } else {
            toast.error('Registration failed. Please try again.');
          }
        } else {
          // Handle general error message
          const errorMessage = errorData.detail || 'Registration failed. Please try again.';
          setError({ general: errorMessage });
          toast.error(errorMessage);
        }
      } else {
        // Fallback for unexpected error format
        setError({ general: 'Registration failed. Please try again.' });
        toast.error('Registration failed. Please try again.');
      }
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Set password for a newly registered user
  const setPassword = async (data) => {
    setLoading(true);
    setError(null);
    try {
      const response = await axios.post(enforceHTTPS(API_ENDPOINTS.SET_PASSWORD), data);
      toast.success('Password set successfully! You can now log in.');
      redirectWithClearError('/login');
      return response.data;
    } catch (error) {
      // Handle detailed errors from the API
      if (error.response?.data) {
        const errorData = error.response.data;

        // Check if we have field-specific errors
        if (typeof errorData === 'object' && !errorData.detail) {
          // Format field-specific errors
          const fieldErrors = {};
          Object.keys(errorData).forEach(key => {
            fieldErrors[key] = Array.isArray(errorData[key])
              ? errorData[key][0]
              : errorData[key];
          });

          setError(fieldErrors);

          // Show the first error as a toast
          const firstErrorKey = Object.keys(fieldErrors)[0];
          if (firstErrorKey) {
            toast.error(`${firstErrorKey}: ${fieldErrors[firstErrorKey]}`);
          } else {
            toast.error('Failed to set password. Please try again.');
          }
        } else {
          // Handle general error message
          const errorMessage = errorData.detail || 'Failed to set password. Please try again.';
          setError({ general: errorMessage });
          toast.error(errorMessage);
        }
      } else {
        // Fallback for unexpected error format
        setError({ general: 'Failed to set password. Please try again.' });
        toast.error('Failed to set password. Please try again.');
      }
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Login user
  const login = async (email, password) => {
    setLoading(true);
    setError(null);
    try {
      const response = await axios.post(enforceHTTPS(API_ENDPOINTS.LOGIN), { email, password });
      const { access, refresh } = response.data;

      // Store tokens
      Cookies.set('token', access, { expires: 1 }); // 1 day expiry
      Cookies.set('refreshToken', refresh, { expires: 7 }); // 7 days expiry

      // Get user data
      const userResponse = await api.get('/users/me/');

      setUser(userResponse.data);
      toast.success('Logged in successfully!');

      // Redirect to dashboard
      redirectWithClearError('/dashboard');
      return userResponse.data;
    } catch (error) {
      // Handle detailed errors from the API
      if (error.response?.data) {
        const errorData = error.response.data;

        // Check if we have field-specific errors
        if (typeof errorData === 'object' && !errorData.detail) {
          // Format field-specific errors
          const fieldErrors = {};
          Object.keys(errorData).forEach(key => {
            fieldErrors[key] = Array.isArray(errorData[key])
              ? errorData[key][0]
              : errorData[key];
          });

          setError(fieldErrors);

          // Show the first error as a toast
          const firstErrorKey = Object.keys(fieldErrors)[0];
          if (firstErrorKey) {
            toast.error(`${firstErrorKey}: ${fieldErrors[firstErrorKey]}`);
          } else {
            toast.error('Login failed. Please check your credentials.');
          }
        } else {
          // Handle general error message
          const errorMessage = errorData.detail || 'Login failed. Please check your credentials.';
          setError({ general: errorMessage });
          toast.error(errorMessage);
        }
      } else {
        // Fallback for unexpected error format
        setError({ general: 'Login failed. Please check your credentials.' });
        toast.error('Login failed. Please check your credentials.');
      }
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Logout user
  const logout = () => {
    Cookies.remove('token');
    Cookies.remove('refreshToken');
    setUser(null);
    toast.success('Logged out successfully');
    redirectWithClearError('/login');
  };

  // Google OAuth login
  const googleLogin = async (accessToken) => {
    setLoading(true);
    setError(null);
    try {
      const response = await axios.post(enforceHTTPS(API_ENDPOINTS.GOOGLE_OAUTH), {
        access_token: accessToken,
      });

      const { access, refresh, user } = response.data;

      // Store tokens
      Cookies.set('token', access, { expires: 1 }); // 1 day expiry
      Cookies.set('refreshToken', refresh, { expires: 7 }); // 7 days expiry

      setUser(user);
      toast.success('Logged in with Google successfully!');
      router.push('/dashboard');

      return user;
    } catch (error) {
      console.error('Google login error:', error);
      if (error.response?.data) {
        const errorData = error.response.data;
        if (typeof errorData === 'object') {
          setError(errorData);
          // Show first error message
          const firstError = Object.values(errorData)[0];
          if (Array.isArray(firstError)) {
            toast.error(firstError[0]);
          } else {
            toast.error(firstError);
          }
        } else {
          setError({ general: errorData });
          toast.error(errorData);
        }
      } else {
        setError({ general: 'Google login failed. Please try again.' });
        toast.error('Google login failed. Please try again.');
      }
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Refresh token
  const refreshToken = async () => {
    try {
      const refresh = Cookies.get('refreshToken');
      if (!refresh) {
        throw new Error('No refresh token available');
      }

      const response = await axios.post(enforceHTTPS(API_ENDPOINTS.REFRESH_TOKEN), {
        refresh,
      });

      const { access } = response.data;
      Cookies.set('token', access, { expires: 1 });

      return access;
    } catch (error) {
      console.error('Token refresh failed:', error);
      logout();
      throw error;
    }
  };

  const value = {
    user,
    loading,
    error,
    register,
    setPassword,
    login,
    googleLogin,
    logout,
    refreshToken,
    clearError,
    isAuthenticated: !!user,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

// Custom hook to use the auth context
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};