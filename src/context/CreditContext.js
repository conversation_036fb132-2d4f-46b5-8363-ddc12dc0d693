'use client';

import Cookies from 'js-cookie';
import { useAuth } from './AuthContext';
import { toast } from 'react-hot-toast';
import { createContext, useContext, useEffect, useState, useCallback } from 'react';

// Create Credit Context
const CreditContext = createContext();

// Credit Provider Component
export function CreditProvider({ children }) {
  const [creditBalance, setCreditBalance] = useState(null);
  const [loading, setLoading] = useState(false);
  const [lastUpdated, setLastUpdated] = useState(null);
  const { user, isAuthenticated } = useAuth();

  // Function to fetch credit balance from API
  const fetchCreditBalance = useCallback(async (showToast = false) => {
    if (!isAuthenticated || !user) {
      setCreditBalance(null);
      return null;
    }

    setLoading(true);
    try {
      const token = Cookies.get('token');
      if (!token) {
        setCreditBalance(null);
        return null;
      }

      const response = await fetch(
        `${process.env.NEXT_PUBLIC_CURRENT_BACKEND_SERVER}/credit-balance/balance/me/`,
        {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        }
      );

      if (response.ok) {
        const data = await response.json();
        setCreditBalance(data.balance);
        setLastUpdated(new Date());

        if (showToast) {
          toast.success('Credit balance refreshed');
        }

        return data.balance;
      } else {
        console.error('Failed to fetch credit balance:', response.status);
        if (showToast) {
          toast.error('Failed to refresh credit balance');
        }
        return null;
      }
    } catch (error) {
      console.error('Error fetching credit balance:', error);
      if (showToast) {
        toast.error('Error refreshing credit balance');
      }
      return null;
    } finally {
      setLoading(false);
    }
  }, [isAuthenticated, user]);

  // Function to manually refresh credit balance (with toast)
  const refreshCreditBalance = useCallback(() => {
    return fetchCreditBalance(true);
  }, [fetchCreditBalance]);

  // Function to update credit balance after consumption
  const updateCreditBalance = useCallback((newBalance) => {
    if (typeof newBalance === 'number') {
      setCreditBalance(newBalance);
      setLastUpdated(new Date());
    } else {
      // If no specific balance provided, fetch from server
      fetchCreditBalance(false);
    }
  }, [fetchCreditBalance]);

  // Function to deduct credits locally (optimistic update)
  const deductCredits = useCallback((amount) => {
    setCreditBalance(prevBalance => {
      if (prevBalance !== null && prevBalance >= amount) {
        return prevBalance - amount;
      }
      return prevBalance;
    });
    setLastUpdated(new Date());
  }, []);

  // Function to add credits locally (optimistic update)
  const addCredits = useCallback((amount) => {
    setCreditBalance(prevBalance => {
      if (prevBalance !== null) {
        return prevBalance + amount;
      }
      return prevBalance;
    });
    setLastUpdated(new Date());
  }, []);

  // Initial fetch when user changes
  useEffect(() => {
    if (isAuthenticated && user) {
      fetchCreditBalance(false);
    } else {
      setCreditBalance(null);
      setLastUpdated(null);
    }
  }, [isAuthenticated, user, fetchCreditBalance]);



  const value = {
    creditBalance,
    loading,
    lastUpdated,
    fetchCreditBalance,
    refreshCreditBalance,
    updateCreditBalance,
    deductCredits,
    addCredits,
  };

  return (
    <CreditContext.Provider value={value}>
      {children}
    </CreditContext.Provider>
  );
}

// Custom hook to use the credit context
export const useCredit = () => {
  const context = useContext(CreditContext);
  if (context === undefined) {
    throw new Error('useCredit must be used within a CreditProvider');
  }
  return context;
};
