'use client';

import { API_ENDPOINTS } from '@/config/api';
import axios from 'axios';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

export default function ActivatePage({ params }) {
  const [status, setStatus] = useState('loading'); // loading, success, error
  const [message, setMessage] = useState('');
  const [email, setEmail] = useState('');
  const router = useRouter();
  const { uid } = params;

  useEffect(() => {
    const activateAccount = async () => {
      try {
        // Make a request to activate the account
        const response = await axios.get(
          `${API_ENDPOINTS.REGISTER.replace('register/', '')}activate/${uid}/`
        );
        
        setStatus('success');
        setMessage(response.data.detail || 'Account activated successfully!');
        
        // Get the email from the response if available
        const userEmail = response.data.email;
        if (userEmail) {
          setEmail(userEmail);
          
          // Redirect to set-password after 2 seconds
          setTimeout(() => {
            router.push(`/set-password?email=${encodeURIComponent(userEmail)}`);
          }, 2000);
        } else {
          // If no email is available, just redirect to set-password
          setTimeout(() => {
            router.push('/set-password');
          }, 2000);
        }
      } catch (error) {
        setStatus('error');
        setMessage(
          error.response?.data?.detail || 'Activation failed. Please try again or contact support.'
        );
      }
    };

    activateAccount();
  }, [uid, router]); // Remove email from the dependency array

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gray-100 px-4 py-12">
      <div className="w-full max-w-md bg-white p-8 rounded-lg shadow-md">
        <div className="mb-8 text-center">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">MizuFlow</h1>
          <p className="text-gray-600">Account Activation</p>
        </div>
        
        <div className="text-center mb-6">
          {status === 'loading' && (
            <>
              <div className="flex justify-center mb-4">
                <div className="animate-spin h-12 w-12 border-4 border-blue-500 rounded-full border-t-transparent"></div>
              </div>
              <p className="text-gray-700">Activating your account...</p>
            </>
          )}
          
          {status === 'success' && (
            <>
              <div className="flex justify-center mb-4">
                <div className="h-16 w-16 bg-green-100 rounded-full flex items-center justify-center">
                  <svg
                    className="h-10 w-10 text-green-500"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M5 13l4 4L19 7"
                    />
                  </svg>
                </div>
              </div>
              <p className="text-green-700 font-medium mb-2">Success!</p>
              <p className="text-gray-600">{message}</p>
              <p className="text-gray-600 mt-2">Redirecting to set password...</p>
            </>
          )}
          
          {status === 'error' && (
            <>
              <div className="flex justify-center mb-4">
                <div className="h-16 w-16 bg-red-100 rounded-full flex items-center justify-center">
                  <svg
                    className="h-10 w-10 text-red-500"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M6 18L18 6M6 6l12 12"
                    />
                  </svg>
                </div>
              </div>
              <p className="text-red-700 font-medium mb-2">Activation Failed</p>
              <p className="text-gray-600">{message}</p>
              <div className="mt-4">
                <Link href="/register" className="text-blue-600 hover:text-blue-500">
                  Go back to registration
                </Link>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
} 