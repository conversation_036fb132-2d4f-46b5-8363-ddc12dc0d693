@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: #f8fafc;
  --foreground: #1e293b;
  --primary: #3b82f6;
  --primary-light: #93c5fd;
  --secondary: #6366f1;
  --secondary-light: #a5b4fc;
  --success: #10b981;
  --danger: #ef4444;
  --warning: #f59e0b;
  --info: #0ea5e9;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #f0f5f9;
    --foreground: #f1f5f9;
    --primary: #3b82f6;
    --primary-light: #60a5fa;
    --secondary: #6366f1;
    --secondary-light: #818cf8;
    --success: #10b981;
    --danger: #ef4444;
    --warning: #f59e0b;
    --info: #0ea5e9;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  overflow-x: hidden;
}

* {
  box-sizing: border-box;
}

/* Formatted Message Styles */
.formatted-message {
  line-height: 1.6;
}

.formatted-message h2 {
  font-family: 'Inter', system-ui, sans-serif;
  letter-spacing: -0.025em;
}

.formatted-message h3 {
  font-family: 'Inter', system-ui, sans-serif;
  letter-spacing: -0.015em;
}

.formatted-message p {
  text-align: justify;
  hyphens: auto;
}

.formatted-message a {
  word-break: break-word;
  transition: all 0.2s ease-in-out;
}

.formatted-message a:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
}
