"use client";

import { useRouter } from 'next/navigation';
import { FaTimesCircle, FaArrowLeft, FaShoppingCart } from 'react-icons/fa';

export default function PaymentCancelPage() {
  const router = useRouter();

  const handleReturnToProfile = () => {
    router.push('/profile');
  };

  const handleTryAgain = () => {
    router.push('/profile'); // User can try purchasing again from the profile page
  };

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-xl shadow-lg p-8 max-w-md w-full text-center">
        <FaTimesCircle className="text-4xl text-orange-500 mx-auto mb-4" />
        <h1 className="text-2xl font-bold text-gray-800 mb-2">Payment Cancelled</h1>
        <p className="text-gray-600 mb-6">
          Your payment was cancelled. No charges have been made to your account.
        </p>
        
        <div className="space-y-3">
          <button
            onClick={handleTryAgain}
            className="w-full bg-teal-600 text-white py-2 px-4 rounded-lg hover:bg-teal-700 transition-colors flex items-center justify-center gap-2"
          >
            <FaShoppingCart />
            Try Again
          </button>
          
          <button
            onClick={handleReturnToProfile}
            className="w-full bg-gray-100 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-200 transition-colors flex items-center justify-center gap-2"
          >
            <FaArrowLeft />
            Return to Profile
          </button>
        </div>
        
        <p className="text-sm text-gray-500 mt-4">
          You can purchase credits anytime from your profile page.
        </p>
      </div>
    </div>
  );
}
