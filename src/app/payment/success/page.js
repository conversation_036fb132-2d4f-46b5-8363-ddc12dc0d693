"use client";

import { useEffect, useState, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { FaCheckCircle, <PERSON>aCoi<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, FaExclamationTriangle } from 'react-icons/fa';
import { paymentService } from '@/services/paymentService';
import { useCredit } from '@/context/CreditContext';
import toast from 'react-hot-toast';

function PaymentSuccessContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { refreshCreditBalance } = useCredit();

  const [verificationStatus, setVerificationStatus] = useState('verifying'); // 'verifying', 'success', 'error'
  const [paymentData, setPaymentData] = useState(null);
  const [error, setError] = useState(null);

  useEffect(() => {
    const sessionId = searchParams.get('session_id');

    if (!sessionId) {
      setVerificationStatus('error');
      setError('No payment session found');
      return;
    }

    // Verify payment with backend
    const verifyPayment = async () => {
      try {
        const data = await paymentService.verifyPaymentSuccess(sessionId);
        setPaymentData(data);
        setVerificationStatus('success');

        // Refresh credit balance to show updated amount
        await refreshCreditBalance();

        toast.success('Payment successful! Your credits have been added.');
      } catch (error) {
        console.error('Payment verification failed:', error);
        setError(error.message || 'Payment verification failed');
        setVerificationStatus('error');
        toast.error('Payment verification failed. Please contact support if your payment was processed.');
      }
    };

    verifyPayment();
  }, [searchParams, refreshCreditBalance]);

  const handleContinue = () => {
    router.push('/profile');
  };

  if (verificationStatus === 'verifying') {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <div className="bg-white rounded-xl shadow-lg p-8 max-w-md w-full text-center">
          <FaSpinner className="text-4xl text-teal-600 mx-auto mb-4 animate-spin" />
          <h1 className="text-2xl font-bold text-gray-800 mb-2">Verifying Payment</h1>
          <p className="text-gray-600">Please wait while we confirm your payment...</p>
        </div>
      </div>
    );
  }

  if (verificationStatus === 'error') {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <div className="bg-white rounded-xl shadow-lg p-8 max-w-md w-full text-center">
          <FaExclamationTriangle className="text-4xl text-red-500 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-gray-800 mb-2">Payment Verification Failed</h1>
          <p className="text-gray-600 mb-6">{error}</p>
          <div className="space-y-3">
            <button
              onClick={handleContinue}
              className="w-full bg-teal-600 text-white py-2 px-4 rounded-lg hover:bg-teal-700 transition-colors"
            >
              Go to Profile
            </button>
            <p className="text-sm text-gray-500">
              If you believe this is an error, please contact our support team.
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-xl shadow-lg p-8 max-w-md w-full text-center">
        <FaCheckCircle className="text-4xl text-green-500 mx-auto mb-4" />
        <h1 className="text-2xl font-bold text-gray-800 mb-2">Payment Successful!</h1>
        <p className="text-gray-600 mb-6">Your credit purchase has been completed successfully.</p>

        {paymentData && (
          <div className="bg-green-50 rounded-lg p-4 mb-6">
            <div className="flex items-center justify-center gap-2 mb-2">
              <FaCoins className="text-green-600" />
              <span className="font-semibold text-green-800">
                {paymentData.credits_amount || 'Credits'} Credits Added
              </span>
            </div>
            <p className="text-sm text-green-700">
              Amount: ${paymentData.amount_usd || 'N/A'} {paymentData.currency?.toUpperCase() || ''}
            </p>
          </div>
        )}

        <button
          onClick={handleContinue}
          className="w-full bg-teal-600 text-white py-2 px-4 rounded-lg hover:bg-teal-700 transition-colors"
        >
          Continue to Profile
        </button>
      </div>
    </div>
  );
}

function LoadingFallback() {
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-xl shadow-lg p-8 max-w-md w-full text-center">
        <FaSpinner className="text-4xl text-teal-600 mx-auto mb-4 animate-spin" />
        <h1 className="text-2xl font-bold text-gray-800 mb-2">Loading</h1>
        <p className="text-gray-600">Please wait...</p>
      </div>
    </div>
  );
}

export default function PaymentSuccessPage() {
  return (
    <Suspense fallback={<LoadingFallback />}>
      <PaymentSuccessContent />
    </Suspense>
  );
}
