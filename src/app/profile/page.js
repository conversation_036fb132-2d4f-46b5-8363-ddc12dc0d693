'use client';

import { Suspense } from 'react';
import AppLayout from '@/components/AppLayout';
import PrivateRoute from '@/components/auth/PrivateRoute';
import ProfilePage from '@/components/profile/ProfilePage';

function ProfilePageContent() {
  return <ProfilePage />;
}

export default function Profile() {
  return (
    <PrivateRoute>
      <AppLayout>
        <Suspense fallback={
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-teal-600"></div>
          </div>
        }>
          <ProfilePageContent />
        </Suspense>
      </AppLayout>
    </PrivateRoute>
  );
}
