import { motion } from "framer-motion";
import Image from 'next/image';

const InvoicePreview = ({ variants, invoiceData, onSendEmails }) => {
  const { invoices, zipDownloadUrl } = invoiceData;

  return (
    <motion.div
      className="flex flex-col gap-8 w-full"
      variants={variants}
    >
      <h2 className="text-xl font-medium text-gray-700 text-center mb-2">
        Generated Invoices
      </h2>
      
      <p className="text-gray-600 text-center max-w-3xl mx-auto">
        Your invoices have been successfully generated. You can preview, download individual invoices, 
        or download all invoices as a ZIP file.
      </p>

      <div className="flex justify-center space-x-4 mb-6">
        <a 
          href={zipDownloadUrl}
          className="flex items-center gap-2 px-6 py-3 bg-blue-600 text-white font-medium rounded-lg shadow-md hover:bg-blue-700 transition-colors"
          download
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clipRule="evenodd" />
          </svg>
          Download All Invoices (ZIP)
        </a>
        
        <button
          onClick={onSendEmails}
          className="flex items-center gap-2 px-6 py-3 bg-green-600 text-white font-medium rounded-lg shadow-md hover:bg-green-700 transition-colors"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
            <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
          </svg>
          Send Invoices via Email
        </button>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        {invoices.map((invoice, index) => (
          <div key={index} className="border rounded-lg overflow-hidden shadow-md bg-white">
            <div className="p-4 bg-gray-50 border-b flex justify-between items-center">
              <h3 className="font-medium text-gray-800">Invoice #{invoice.id}</h3>
              <span className="text-gray-600 text-sm">{invoice.date}</span>
            </div>
            
            <div className="p-6">
              <div className="flex justify-between mb-4">
                <div>
                  <p className="text-sm text-gray-600">Customer</p>
                  <p className="font-medium text-gray-800">{invoice.customerName}</p>
                </div>
                <div className="text-right">
                  <p className="text-sm text-gray-600">Amount</p>
                  <p className="font-medium text-gray-800">{invoice.amount}</p>
                </div>
              </div>
              
              <div className="mt-4 h-64 bg-gray-100 rounded-lg overflow-hidden relative">
                <Image 
                  src={invoice.previewUrl} 
                  alt={`Invoice ${invoice.id} Preview`} 
                  fill
                  sizes="(max-width: 768px) 100vw, 50vw"
                  style={{
                    objectFit: 'contain'
                  }}
                />
              </div>
              
              <div className="mt-6">
                <a 
                  href={invoice.downloadUrl}
                  className="flex items-center justify-center gap-2 w-full py-2 bg-gray-100 text-gray-800 font-medium rounded-lg hover:bg-gray-200 transition-colors"
                  download
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                  Download Invoice
                </a>
              </div>
            </div>
          </div>
        ))}
      </div>
    </motion.div>
  );
};

export default InvoicePreview; 