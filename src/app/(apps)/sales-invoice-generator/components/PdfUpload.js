import { motion } from "framer-motion";
import { useRef, useState } from "react";

const PdfUpload = ({ onUpload, variants }) => {
  const [isDragging, setIsDragging] = useState(false);
  const [file, setFile] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const fileInputRef = useRef(null);

  const handleDragOver = (e) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = () => {
    setIsDragging(false);
  };

  const handleDrop = (e) => {
    e.preventDefault();
    setIsDragging(false);
    
    const droppedFile = e.dataTransfer.files[0];
    handleFileSelection(droppedFile);
  };

  const handleFileChange = (e) => {
    const selectedFile = e.target.files[0];
    handleFileSelection(selectedFile);
  };

  const handleFileSelection = (selectedFile) => {
    setError("");
    
    if (!selectedFile) return;
    
    if (selectedFile.type !== 'application/pdf') {
      setError("Please upload a PDF file");
      return;
    }
    
    setFile(selectedFile);
  };

  const handleSubmit = async () => {
    if (!file) {
      setError("Please select a file first");
      return;
    }

    setLoading(true);
    try {
      await onUpload(file);
    } catch (err) {
      setError("Failed to process the PDF. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <motion.div 
      className="flex flex-col items-center justify-center gap-6"
      variants={variants}
    >
      <h2 className="text-xl font-medium text-gray-700 text-center mb-2">
        Upload an invoice PDF to create your template
      </h2>
      
      <motion.div
        className={`border-2 border-dashed rounded-lg p-8 w-full 
                   flex flex-col items-center justify-center gap-4 
                   transition-colors ${isDragging 
                     ? 'border-blue-500 bg-blue-50' 
                     : file 
                     ? 'border-green-500 bg-green-50' 
                     : 'border-gray-300 hover:border-blue-400'}`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        whileHover={{ scale: 1.01 }}
        onClick={() => fileInputRef.current?.click()}
        variants={variants}
      >
        <input
          type="file"
          ref={fileInputRef}
          onChange={handleFileChange}
          accept=".pdf"
          className="hidden"
        />
        
        {file ? (
          <div className="text-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-green-500 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <p className="text-lg font-medium text-gray-800">{file.name}</p>
            <p className="text-sm text-gray-600">{(file.size / 1024 / 1024).toFixed(2)} MB</p>
          </div>
        ) : (
          <>
            <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-gray-600 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 13h6m-3-3v6m5 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            <p className="text-lg font-medium text-gray-700">Drag & drop your invoice PDF here</p>
            <p className="text-sm text-gray-600">or click to browse files</p>
          </>
        )}
      </motion.div>
      
      {error && (
        <p className="text-red-600 text-sm mt-2">{error}</p>
      )}
      
      <motion.button
        className={`px-6 py-3 rounded-lg shadow-md font-medium w-full max-w-xs
                    ${file 
                      ? 'bg-blue-600 hover:bg-blue-700 text-white' 
                      : 'bg-gray-300 text-gray-600 cursor-not-allowed'}`}
        whileHover={file ? { scale: 1.03 } : {}}
        whileTap={file ? { scale: 0.98 } : {}}
        disabled={!file || loading}
        onClick={handleSubmit}
        variants={variants}
      >
        {loading ? (
          <div className="flex items-center justify-center">
            <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Processing PDF...
          </div>
        ) : (
          "Process PDF"
        )}
      </motion.button>
    </motion.div>
  );
};

export default PdfUpload; 