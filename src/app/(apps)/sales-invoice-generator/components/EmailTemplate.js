import { motion } from "framer-motion";
import { useState } from "react";

const EmailTemplate = ({ variants, invoiceData, onNext }) => {
  const [emailSubject, setEmailSubject] = useState("Your Invoice from {{company}}");
  const [emailBody, setEmailBody] = useState(
`Dear {{customer_name}},

We have created an invoice #{{invoice_number}} for your recent purchase.

The total amount due is {{total}}.

Please find the attached invoice for your records.

Thank you for your business!

Best regards,
{{signature}}
{{company}}`
  );
  const [senderEmail, setSenderEmail] = useState("");
  const [error, setError] = useState("");
  
  const handleNext = () => {
    if (!emailSubject.trim() || !emailBody.trim()) {
      setError("Email subject and body cannot be empty");
      return;
    }
    
    if (!senderEmail.trim()) {
      setError("Sender email is required");
      return;
    }
    
    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(senderEmail)) {
      setError("Please enter a valid email address");
      return;
    }
    
    onNext({ emailSubject, emailBody, senderEmail });
  };
  
  // Helper to show a preview with variables replaced
  const getPreviewText = (text) => {
    return text
      .replace(/\{\{customer_name\}\}/g, "Sample Customer")
      .replace(/\{\{customer\}\}/g, "Sample Customer")
      .replace(/\{\{invoice_number\}\}/g, "INV-2023-001")
      .replace(/\{\{total\}\}/g, "$1,320.00")
      .replace(/\{\{company\}\}/g, "Your Company Name")
      .replace(/\{\{signature\}\}/g, "John Doe");
  };

  return (
    <motion.div
      className="flex flex-col gap-8 w-full"
      variants={variants}
    >
      <h2 className="text-xl font-medium text-gray-700 text-center mb-2">
        Customize Email Template
      </h2>
      
      <p className="text-gray-600 text-center max-w-3xl mx-auto">
        Customize the email message that will be sent to your customers with their invoices. 
        Use the available variables to personalize the message.
      </p>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <div className="space-y-6">
          <div>
            <label htmlFor="senderEmail" className="block text-sm font-medium text-gray-700 mb-1">
              Send From Email <span className="text-red-500">*</span>
            </label>
            <input
              id="senderEmail"
              type="email"
              value={senderEmail}
              onChange={(e) => setSenderEmail(e.target.value)}
              className="w-full p-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 text-gray-900"
              placeholder="<EMAIL>"
              required
            />
          </div>
        
          <div>
            <label htmlFor="emailSubject" className="block text-sm font-medium text-gray-700 mb-1">
              Email Subject <span className="text-red-500">*</span>
            </label>
            <input
              id="emailSubject"
              type="text"
              value={emailSubject}
              onChange={(e) => setEmailSubject(e.target.value)}
              className="w-full p-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 text-gray-900"
              placeholder="Enter email subject"
            />
          </div>
          
          <div>
            <label htmlFor="emailBody" className="block text-sm font-medium text-gray-700 mb-1">
              Email Body <span className="text-red-500">*</span>
            </label>
            <textarea
              id="emailBody"
              value={emailBody}
              onChange={(e) => setEmailBody(e.target.value)}
              className="w-full p-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 min-h-[300px] font-mono text-gray-900"
              placeholder="Enter email body"
            />
          </div>
          
          <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
            <h3 className="text-sm font-medium text-blue-800 mb-2">Available Variables:</h3>
            <ul className="space-y-1 text-sm text-blue-700">
              <li><code>{'{{customer_name}}'}</code> - Customer&apos;s name</li>
              <li><code>{'{{customer}}'}</code> - Customer&apos;s name (alternative)</li>
              <li><code>{'{{invoice_number}}'}</code> - Invoice number</li>
              <li><code>{'{{total}}'}</code> - Total amount</li>
              <li><code>{'{{company}}'}</code> - Your company name</li>
              <li><code>{'{{signature}}'}</code> - Your signature</li>
            </ul>
          </div>
        </div>
        
        <div className="space-y-6">
          <h3 className="text-lg font-medium text-gray-700">Email Preview</h3>
          
          <div className="border rounded-lg overflow-hidden bg-white shadow-sm">
            <div className="bg-gray-50 p-4 border-b">
              <p className="font-medium text-gray-800">From: {senderEmail || "<EMAIL>"}</p>
              <p className="font-medium text-gray-800 mt-2">Subject: {getPreviewText(emailSubject)}</p>
            </div>
            <div className="p-6 whitespace-pre-wrap text-gray-900">{getPreviewText(emailBody)}</div>
          </div>
          
          <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
            <p className="text-sm text-yellow-800">
              <strong>Note:</strong> This is just a preview. The actual email will include 
              the specific customer information when sent.
            </p>
          </div>
        </div>
      </div>
      
      {error && (
        <p className="text-red-600 text-sm text-center">{error}</p>
      )}
      
      <div className="flex justify-center mt-6">
        <button
          className="px-10 py-3 bg-blue-600 text-white font-medium rounded-lg shadow-md hover:bg-blue-700 transition-colors"
          onClick={handleNext}
        >
          Next: Review & Send Emails
        </button>
      </div>
    </motion.div>
  );
};

export default EmailTemplate; 