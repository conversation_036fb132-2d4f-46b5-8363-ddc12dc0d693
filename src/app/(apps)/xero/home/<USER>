'use client';

import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { toast } from 'react-hot-toast';
import { FaSpinner } from 'react-icons/fa';

export default function XeroHomePage() {
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    // Show a success toast
    toast.success('Successfully connected to Xero!');
    
    // Redirect to the main Xero page after a short delay
    const timer = setTimeout(() => {
      router.push('/xero');
    }, 1500);
    
    return () => clearTimeout(timer);
  }, [router]);
  
  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-gray-50">
      <div className="p-8 bg-white rounded-lg shadow-md text-center">
        <h1 className="text-2xl font-bold text-gray-800 mb-4">Connected to Xero</h1>
        <div className="flex justify-center mb-4">
          <FaSpinner className="h-8 w-8 animate-spin text-teal-600" />
        </div>
        <p className="text-gray-600">Redirecting you to the Xero dashboard...</p>
      </div>
    </div>
  );
} 