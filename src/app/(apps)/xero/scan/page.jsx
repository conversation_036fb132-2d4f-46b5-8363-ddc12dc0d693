'use client';

import { xeroApi } from '@/services/api';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { toast } from 'react-hot-toast';
import { FaFileUpload, FaSpinner } from 'react-icons/fa';

export default function XeroScanPage() {
  const router = useRouter();
  const [selectedFiles, setSelectedFiles] = useState([]);
  const [isUploading, setIsUploading] = useState(false);
  const [accounts, setAccounts] = useState([]);
  const [taxRates, setTaxRates] = useState([]);
  const [invoiceTypes, setInvoiceTypes] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedAccount, setSelectedAccount] = useState('');
  const [selectedTaxRate, setSelectedTaxRate] = useState('');
  const [selectedInvoiceType, setSelectedInvoiceType] = useState('');

  useEffect(() => {
    // Fetch required data from Xero
    const fetchXeroData = async () => {
      try {
        setIsLoading(true);
        
        // Fetch accounts, tax rates, and invoice types
        const [accountsRes, taxRatesRes, invoiceTypesRes] = await Promise.all([
          xeroApi.getAccounts(),
          xeroApi.getTaxRates(),
          xeroApi.getInvoiceTypes()
        ]);
        
        setAccounts(accountsRes.data.accounts || []);
        setTaxRates(taxRatesRes.data.tax_rates || []);
        setInvoiceTypes(invoiceTypesRes.data.invoice_types || []);
        
        // Set default values if available
        if (accountsRes.data.accounts?.length > 0) {
          setSelectedAccount(accountsRes.data.accounts[0].Code);
        }
        
        if (taxRatesRes.data.tax_rates?.length > 0) {
          setSelectedTaxRate(taxRatesRes.data.tax_rates[0].TaxType);
        }
        
        if (invoiceTypesRes.data.invoice_types?.length > 0) {
          setSelectedInvoiceType(invoiceTypesRes.data.invoice_types[0].Type);
        }
      } catch (error) {
        console.error('Error fetching Xero data:', error);
        toast.error('Failed to load Xero data. Please check your connection.');
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchXeroData();
  }, []);

  // Handle file selection
  const handleFileSelect = (e) => {
    const files = Array.from(e.target.files);
    setSelectedFiles(files);
  };

  // Handle form submission
  const handleScanInvoices = async (e) => {
    e.preventDefault();
    
    if (selectedFiles.length === 0) {
      toast.error('Please select at least one PDF invoice to scan');
      return;
    }
    
    if (!selectedAccount) {
      toast.error('Please select an account');
      return;
    }
    
    if (!selectedTaxRate) {
      toast.error('Please select a tax rate');
      return;
    }
    
    if (!selectedInvoiceType) {
      toast.error('Please select an invoice type');
      return;
    }
    
    try {
      setIsUploading(true);
      
      const formData = new FormData();
      selectedFiles.forEach((file, index) => {
        formData.append(`invoice_${index}`, file);
      });
      
      // Add the selected account, tax rate, and invoice type to the form data
      formData.append('account_code', selectedAccount);
      formData.append('tax_type', selectedTaxRate);
      formData.append('invoice_type', selectedInvoiceType);
      
      const response = await xeroApi.scanInvoices(formData);
      
      if (response.status === 200) {
        toast.success('Invoices scanned successfully');
        // Store scan results in localStorage for next step
        localStorage.setItem('scanResults', JSON.stringify(response.data));
        // Navigate to the next step (will be implemented later)
        router.push('/xero/scan/review');
      } else {
        throw new Error('Failed to scan invoices');
      }
    } catch (error) {
      console.error('Error scanning invoices:', error);
      toast.error('Failed to scan invoices');
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <div className="p-6">
      <div className="bg-white rounded-lg shadow-md p-6">
        {/* Breadcrumbs */}
        <nav className="flex mb-6" aria-label="Breadcrumb">
          <ol className="inline-flex items-center space-x-1 md:space-x-3">
            <li className="inline-flex items-center">
              <Link href="/xero" className="text-gray-700 hover:text-teal-600">
                Xero Automation
              </Link>
            </li>
            <li>
              <div className="flex items-center">
                <svg className="w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                  <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="m1 9 4-4-4-4"/>
                </svg>
                <span className="text-teal-600">Upload Invoices</span>
              </div>
            </li>
          </ol>
        </nav>

        <div>
          <h2 className="text-2xl font-bold text-gray-800 mb-4">Upload Invoices</h2>
          <p className="mb-6 text-gray-600">
            Upload your PDF invoices to scan and analyze. The service will extract key information 
            that can be reviewed before importing into Xero.
          </p>
          
          <form onSubmit={handleScanInvoices} className="space-y-6">
            {/* Xero Settings Section */}
            <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
              <h3 className="text-lg font-medium text-gray-700 mb-3">Xero Settings</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Account
                  </label>
                  <select
                    value={selectedAccount}
                    onChange={(e) => setSelectedAccount(e.target.value)}
                    className="w-full text-gray-700 rounded-md border border-gray-300 p-2 focus:ring-teal-500 focus:border-teal-500"
                    disabled={isLoading}
                    required
                  >
                    {isLoading ? (
                      <option>Loading accounts...</option>
                    ) : accounts.length === 0 ? (
                      <option value="">No accounts available</option>
                    ) : (
                      accounts.map((account) => (
                        <option key={account.AccountID} value={account.Code}>
                          {account.Name} ({account.Code})
                        </option>
                      ))
                    )}
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Tax Rate
                  </label>
                  <select
                    value={selectedTaxRate}
                    onChange={(e) => setSelectedTaxRate(e.target.value)}
                    className="w-full text-gray-700 rounded-md border border-gray-300 p-2 focus:ring-teal-500 focus:border-teal-500"
                    disabled={isLoading}
                    required
                  >
                    {isLoading ? (
                      <option>Loading tax rates...</option>
                    ) : taxRates.length === 0 ? (
                      <option value="">No tax rates available</option>
                    ) : (
                      taxRates.map((taxRate) => (
                        <option key={taxRate.TaxType} value={taxRate.TaxType}>
                          {taxRate.Name} ({taxRate.TaxType})
                        </option>
                      ))
                    )}
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Invoice Type
                  </label>
                  <select
                    value={selectedInvoiceType}
                    onChange={(e) => setSelectedInvoiceType(e.target.value)}
                    className="w-full text-gray-700 rounded-md border border-gray-300 p-2 focus:ring-teal-500 focus:border-teal-500"
                    disabled={isLoading}
                    required
                  >
                    {isLoading ? (
                      <option>Loading invoice types...</option>
                    ) : invoiceTypes.length === 0 ? (
                      <option value="">No invoice types available</option>
                    ) : (
                      invoiceTypes.map((type) => (
                        <option key={type.Type} value={type.Type}>
                          {type.Description} ({type.Type})
                        </option>
                      ))
                    )}
                  </select>
                </div>
              </div>
            </div>
            
            {/* File Upload Section */}
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
              <FaFileUpload className="mx-auto h-12 w-12 text-gray-400" />
              <p className="mt-4 text-sm text-gray-600">
                Drag and drop your PDF invoices here, or click to select files
              </p>
              <input
                type="file"
                multiple
                accept=".pdf"
                onChange={handleFileSelect}
                className="mt-4 block w-full text-gray-700 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-teal-500 focus:border-teal-500"
              />
              <div className="mt-4">
                {selectedFiles.length > 0 && (
                  <div className="text-left">
                    <p className="font-medium text-gray-700">Selected files:</p>
                    <ul className="mt-2 list-disc pl-5">
                      {selectedFiles.map((file, index) => (
                        <li key={index} className="text-sm text-gray-600">{file.name}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            </div>
            
            <div className="flex justify-end">
              <Link 
                href="/xero"
                className="mr-4 px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
              >
                Cancel
              </Link>
              <button
                type="submit"
                disabled={isUploading || selectedFiles.length === 0 || isLoading}
                className={`
                  px-6 py-3 bg-teal-600 text-white rounded-lg
                  hover:bg-teal-700 transition-colors
                  disabled:bg-teal-400 disabled:cursor-not-allowed
                  flex items-center justify-center min-w-[120px]
                `}
              >
                {isUploading ? (
                  <>
                    <FaSpinner className="animate-spin mr-2" />
                    Processing...
                  </>
                ) : 'Scan Invoices'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
} 