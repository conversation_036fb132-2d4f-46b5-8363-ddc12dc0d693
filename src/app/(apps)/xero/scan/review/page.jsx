'use client';

import { xeroApi } from '@/services/api';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { toast } from 'react-hot-toast';
import { FaCheck, FaEdit, FaExclamationTriangle, FaSave, FaSpinner, FaTimes } from 'react-icons/fa';

export default function XeroReviewPage() {
  const router = useRouter();
  const [scanResults, setScanResults] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isCreating, setIsCreating] = useState(false);
  const [editingIndex, setEditingIndex] = useState(null);
  const [editedInvoice, setEditedInvoice] = useState(null);
  const [errorMessages, setErrorMessages] = useState([]);
  
  // Store metadata for dropdowns
  const [contacts, setContacts] = useState([]);
  const [accounts, setAccounts] = useState([]);
  const [taxRates, setTaxRates] = useState([]);
  const [invoiceTypes, setInvoiceTypes] = useState([]);
  const [isLoadingMetadata, setIsLoadingMetadata] = useState(false);

  useEffect(() => {
    // Load scan results from localStorage
    const loadResults = () => {
      try {
        const resultsString = localStorage.getItem('scanResults');
        if (!resultsString) {
          toast.error('No scan results found. Please scan your invoices first.');
          router.push('/xero/scan');
          return;
        }

        const results = JSON.parse(resultsString);
        setScanResults(results);
      } catch (error) {
        console.error('Error loading scan results:', error);
        toast.error('Failed to load scan results');
        router.push('/xero/scan');
      }
    };

    loadResults();
  }, [router]);
  
  // Load metadata for edit mode
  const loadMetadata = async () => {
    try {
      setIsLoadingMetadata(true);
      const [contactsRes, accountsRes, taxRatesRes, invoiceTypesRes] = await Promise.all([
        xeroApi.getContacts(),
        xeroApi.getAccounts(),
        xeroApi.getTaxRates(),
        xeroApi.getInvoiceTypes()
      ]);
      
      setContacts(contactsRes.data.contacts || []);
      setAccounts(accountsRes.data.accounts || []);
      setTaxRates(taxRatesRes.data.tax_rates || []);
      setInvoiceTypes(invoiceTypesRes.data.invoice_types || []);
    } catch (error) {
      console.error('Error loading metadata:', error);
      toast.error('Failed to load data needed for editing');
    } finally {
      setIsLoadingMetadata(false);
    }
  };

  // Function to create invoices in Xero
  const handleCreateInvoices = async () => {
    if (!scanResults?.invoice_data || scanResults.invoice_data.length === 0) {
      toast.error('No valid invoice data to create');
      return;
    }

    try {
      setIsCreating(true);
      setErrorMessages([]);
      
      // Create each invoice in Xero
      const results = [];
      const errors = [];

      for (const [index, invoiceData] of scanResults.invoice_data.entries()) {
        try {
          const response = await xeroApi.createInvoice(invoiceData);
          results.push({
            success: true,
            data: response.data,
            error: null
          });
        } catch (error) {
          console.error('Error creating invoice:', error);
          const errorDetail = error.response?.data?.error || 'Failed to create invoice';
          results.push({
            success: false,
            data: null,
            error: errorDetail
          });
          
          // Add to error messages with invoice details
          errors.push({
            index,
            invoiceNumber: invoiceData.invoice_number || `Invoice #${index + 1}`,
            customerName: invoiceData.contact?.name || 'Unknown Customer',
            error: errorDetail,
            acknowledged: false
          });
        }
      }
      
      // Update error messages state
      if (errors.length > 0) {
        setErrorMessages(errors);
      }
      
      // Check if any invoices were created successfully
      const successCount = results.filter(r => r.success).length;
      if (successCount > 0) {
        toast.success(`Created ${successCount} out of ${results.length} invoices in Xero`);
        
        // Only navigate away if all were successful
        if (successCount === results.length) {
          // Store results for reference
          localStorage.setItem('createResults', JSON.stringify(results));
          
          // Navigate to home page
          router.push('/xero/home');
        }
      } else {
        toast.error('Failed to create any invoices in Xero');
      }
    } catch (error) {
      console.error('Error creating invoices:', error);
      toast.error('Failed to create invoices in Xero');
    } finally {
      setIsCreating(false);
    }
  };

  // Function to acknowledge an error
  const handleAcknowledgeError = (index) => {
    setErrorMessages(prevErrors => {
      const newErrors = [...prevErrors];
      newErrors[index].acknowledged = true;
      
      // Check if all errors are acknowledged
      const allAcknowledged = newErrors.every(err => err.acknowledged);
      if (allAcknowledged) {
        // If all are acknowledged, navigate home
        router.push('/xero/home');
      }
      
      return newErrors;
    });
  };

  // Function to start editing an invoice
  const handleEditInvoice = async (index) => {
    if (isLoadingMetadata) return;
    
    // Load metadata if not already loaded
    if (contacts.length === 0 || accounts.length === 0 || taxRates.length === 0 || invoiceTypes.length === 0) {
      await loadMetadata();
    }
    
    setEditingIndex(index);
    setEditedInvoice(JSON.parse(JSON.stringify(scanResults.invoice_data[index])));
  };
  
  // Function to save edited invoice
  const handleSaveInvoice = () => {
    if (editingIndex === null || !editedInvoice) return;
    
    // Validate the invoice data
    let isValid = true;
    let errorMessage = '';
    
    // Check required fields
    if (!editedInvoice.contact?.name) {
      isValid = false;
      errorMessage = 'Customer name is required';
    } else if (!editedInvoice.date) {
      isValid = false;
      errorMessage = 'Invoice date is required';
    } else if (!editedInvoice.due_date) {
      isValid = false;
      errorMessage = 'Due date is required';
    } else if (!editedInvoice.type) {
      isValid = false;
      errorMessage = 'Invoice type is required';
    } else if (!editedInvoice.status) {
      isValid = false;
      errorMessage = 'Status is required';
    } else if (!editedInvoice.line_items || editedInvoice.line_items.length === 0) {
      isValid = false;
      errorMessage = 'At least one line item is required';
    } else {
      // Validate each line item
      for (let i = 0; i < editedInvoice.line_items.length; i++) {
        const item = editedInvoice.line_items[i];
        if (!item.description) {
          isValid = false;
          errorMessage = `Line item ${i + 1} description is required`;
          break;
        }
        if (!item.account_code) {
          isValid = false;
          errorMessage = `Line item ${i + 1} account is required`;
          break;
        }
        if (!item.tax_type) {
          isValid = false;
          errorMessage = `Line item ${i + 1} tax rate is required`;
          break;
        }
        if (item.quantity <= 0) {
          isValid = false;
          errorMessage = `Line item ${i + 1} quantity must be greater than 0`;
          break;
        }
      }
    }
    
    if (!isValid) {
      toast.error(errorMessage);
      return;
    }
    
    // Create a copy of the scan results
    const updatedScanResults = { ...scanResults };
    
    // Update the invoice data at the editing index
    updatedScanResults.invoice_data[editingIndex] = editedInvoice;
    
    // Update the state
    setScanResults(updatedScanResults);
    
    // Update localStorage
    localStorage.setItem('scanResults', JSON.stringify(updatedScanResults));
    
    // Exit edit mode
    setEditingIndex(null);
    setEditedInvoice(null);
    
    toast.success('Invoice updated successfully');
  };
  
  // Function to cancel editing
  const handleCancelEdit = () => {
    setEditingIndex(null);
    setEditedInvoice(null);
  };
  
  // Function to handle changes to the edited invoice
  const handleInvoiceChange = (key, value) => {
    if (!editedInvoice) return;
    
    setEditedInvoice({
      ...editedInvoice,
      [key]: value
    });
  };
  
  // Function to handle changes to contact
  const handleContactChange = (contactName) => {
    if (!editedInvoice) return;
    
    setEditedInvoice({
      ...editedInvoice,
      contact: {
        ...editedInvoice.contact,
        name: contactName
      }
    });
  };
  
  // Function to handle changes to line items
  const handleLineItemChange = (index, key, value) => {
    if (!editedInvoice) return;
    
    const updatedLineItems = [...editedInvoice.line_items];
    updatedLineItems[index] = {
      ...updatedLineItems[index],
      [key]: key === 'quantity' || key === 'unit_amount' ? parseFloat(value) : value
    };
    
    setEditedInvoice({
      ...editedInvoice,
      line_items: updatedLineItems
    });
  };
  
  // Function to add a new line item
  const handleAddLineItem = () => {
    if (!editedInvoice) return;
    
    // Get default values from first line item or from dropdowns
    const defaultAccountCode = editedInvoice.line_items[0]?.account_code || (accounts[0]?.Code || '');
    const defaultTaxType = editedInvoice.line_items[0]?.tax_type || (taxRates[0]?.TaxType || '');
    
    const newLineItem = {
      description: '',
      quantity: 1,
      unit_amount: 0,
      account_code: defaultAccountCode,
      tax_type: defaultTaxType,
      item_code: ''
    };
    
    setEditedInvoice({
      ...editedInvoice,
      line_items: [...editedInvoice.line_items, newLineItem]
    });
  };
  
  // Function to remove a line item
  const handleRemoveLineItem = (index) => {
    if (!editedInvoice || editedInvoice.line_items.length <= 1) return;
    
    const updatedLineItems = [...editedInvoice.line_items];
    updatedLineItems.splice(index, 1);
    
    setEditedInvoice({
      ...editedInvoice,
      line_items: updatedLineItems
    });
  };

  // Format currency for display
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  // Calculate invoice total
  const calculateTotal = (invoice) => {
    if (!invoice?.line_items) return 0;
    return invoice.line_items.reduce((sum, item) => {
      return sum + (item.quantity * item.unit_amount);
    }, 0);
  };

  if (!scanResults) {
    return (
      <div className="p-6">
        <div className="bg-white rounded-lg shadow-md p-6 flex justify-center items-center h-64">
          <FaSpinner className="animate-spin text-teal-600 text-2xl mr-2" />
          <span className="text-gray-600">Loading scan results...</span>
        </div>
      </div>
    );
  }

  // If there are error messages, show them first
  if (errorMessages.length > 0) {
    return (
      <div className="p-6">
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-2xl font-bold text-gray-800 mb-4">Invoice Creation Results</h2>
          <p className="mb-6 text-gray-600">
            Some invoices could not be created in Xero. Please review the errors below.
          </p>
          
          <div className="space-y-4">
            {errorMessages.map((error, idx) => (
              <div 
                key={idx} 
                className={`border ${error.acknowledged ? 'border-gray-200 bg-gray-50' : 'border-red-200 bg-red-50'} rounded-lg p-4`}
              >
                <div className="flex items-start">
                  <div className="flex-shrink-0 mt-0.5">
                    <FaExclamationTriangle className={error.acknowledged ? "text-gray-400" : "text-red-500"} />
                  </div>
                  <div className="ml-3 flex-1">
                    <h3 className={`text-lg font-medium ${error.acknowledged ? 'text-gray-500' : 'text-red-800'}`}>
                      {error.invoiceNumber}
                      {error.customerName && (
                        <span className="text-gray-600"> - {error.customerName}</span>
                      )}
                    </h3>
                    <div className={`mt-2 text-sm ${error.acknowledged ? 'text-gray-500' : 'text-red-700'}`}>
                      <p>{error.error}</p>
                    </div>
                  </div>
                </div>
                {!error.acknowledged && (
                  <div className="mt-3 flex justify-end">
                    <button
                      onClick={() => handleAcknowledgeError(idx)}
                      className="px-4 py-2 bg-white text-red-600 font-medium rounded-md border border-red-300 hover:bg-red-50 transition-colors"
                    >
                      Acknowledge
                    </button>
                  </div>
                )}
              </div>
            ))}
          </div>
          
          <div className="mt-6 flex justify-end">
            <Link
              href="/xero/scan"
              className="mr-4 px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
            >
              Back to Scan
            </Link>
            <Link
              href="/xero/home"
              className="px-6 py-3 bg-teal-600 text-white rounded-lg hover:bg-teal-700 transition-colors"
            >
              Go to Dashboard
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="bg-white rounded-lg shadow-md p-6">
        {/* Breadcrumbs */}
        <nav className="flex mb-6" aria-label="Breadcrumb">
          <ol className="inline-flex items-center space-x-1 md:space-x-3">
            <li className="inline-flex items-center">
              <Link href="/xero" className="text-gray-700 hover:text-teal-600">
                Xero Automation
              </Link>
            </li>
            <li>
              <div className="flex items-center">
                <svg className="w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                  <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="m1 9 4-4-4-4"/>
                </svg>
                <Link href="/xero/scan" className="text-gray-700 hover:text-teal-600">
                  Upload Invoices
                </Link>
              </div>
            </li>
            <li>
              <div className="flex items-center">
                <svg className="w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                  <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="m1 9 4-4-4-4"/>
                </svg>
                <span className="text-teal-600">Review</span>
              </div>
            </li>
          </ol>
        </nav>

        <div>
          <h2 className="text-2xl font-bold text-gray-800 mb-4">Review Invoice Data</h2>
          <p className="mb-6 text-gray-600">
            Review the extracted invoice data before creating the invoices in Xero.
          </p>
          
          {scanResults.invoice_data?.length > 0 ? (
            <div className="space-y-8">
              {scanResults.invoice_data.map((invoice, index) => (
                <div key={index} className="border border-gray-200 rounded-lg overflow-hidden">
                  {/* Invoice header */}
                  <div className="bg-gray-50 p-4 border-b border-gray-200">
                    <div className="flex justify-between items-center">
                      <h3 className="text-lg font-medium text-gray-800">
                        {editingIndex === index ? (
                          <input
                            type="text"
                            className="border border-gray-300 rounded-md p-1 text-gray-700 w-48"
                            value={editedInvoice.invoice_number}
                            onChange={(e) => handleInvoiceChange('invoice_number', e.target.value)}
                          />
                        ) : (
                          <>Invoice # {invoice.invoice_number || `INV-${index + 1}`}</>
                        )}
                      </h3>
                      {editingIndex === index ? (
                        <div className="flex space-x-2">
                          <button 
                            onClick={handleSaveInvoice}
                            className="text-green-600 hover:text-green-800 flex items-center text-sm"
                          >
                            <FaSave className="mr-1" /> Save
                          </button>
                          <button 
                            onClick={handleCancelEdit}
                            className="text-red-600 hover:text-red-800 flex items-center text-sm"
                          >
                            <FaTimes className="mr-1" /> Cancel
                          </button>
                        </div>
                      ) : (
                        <button 
                          onClick={() => handleEditInvoice(index)}
                          className="text-teal-600 hover:text-teal-800 flex items-center text-sm"
                          disabled={isLoadingMetadata || editingIndex !== null}
                        >
                          {isLoadingMetadata ? (
                            <>
                              <FaSpinner className="animate-spin mr-1" /> Loading...
                            </>
                          ) : (
                            <>
                              <FaEdit className="mr-1" /> Edit
                            </>
                          )}
                        </button>
                      )}
                    </div>
                    <div className="mt-2 grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                      <div>
                        <span className="font-medium text-gray-700">Customer: </span>
                        {editingIndex === index ? (
                          <select
                            className="border border-gray-300 rounded-md p-1 text-gray-700 w-full"
                            value={editedInvoice.contact.name}
                            onChange={(e) => handleContactChange(e.target.value)}
                          >
                            {contacts.map((contact) => (
                              <option key={contact.ContactID} value={contact.Name}>
                                {contact.Name}
                              </option>
                            ))}
                          </select>
                        ) : (
                          <span className="text-gray-700">{invoice.contact?.name || 'Unknown'}</span>
                        )}
                      </div>
                      <div>
                        <span className="font-medium text-gray-700">Date: </span>
                        {editingIndex === index ? (
                          <input
                            type="date"
                            className="border border-gray-300 rounded-md p-1 text-gray-700"
                            value={editedInvoice.date}
                            onChange={(e) => handleInvoiceChange('date', e.target.value)}
                          />
                        ) : (
                          <span className="text-gray-700">{invoice.date}</span>
                        )}
                      </div>
                      <div>
                        <span className="font-medium text-gray-700">Due Date: </span>
                        {editingIndex === index ? (
                          <input
                            type="date"
                            className="border border-gray-300 rounded-md p-1 text-gray-700"
                            value={editedInvoice.due_date}
                            onChange={(e) => handleInvoiceChange('due_date', e.target.value)}
                          />
                        ) : (
                          <span className="text-gray-700">{invoice.due_date}</span>
                        )}
                      </div>
                      <div>
                        <span className="font-medium text-gray-700">Invoice Type: </span>
                        {editingIndex === index ? (
                          <select
                            className="border border-gray-300 rounded-md p-1 text-gray-700 w-full"
                            value={editedInvoice.type}
                            onChange={(e) => handleInvoiceChange('type', e.target.value)}
                          >
                            {invoiceTypes.map((type) => (
                              <option key={type.Type} value={type.Type}>
                                {type.Description} ({type.Type})
                              </option>
                            ))}
                          </select>
                        ) : (
                          <span className="text-gray-700">{invoice.type}</span>
                        )}
                      </div>
                      <div>
                        <span className="font-medium text-gray-700">Status: </span>
                        {editingIndex === index ? (
                          <select
                            className="border border-gray-300 rounded-md p-1 text-gray-700"
                            value={editedInvoice.status}
                            onChange={(e) => handleInvoiceChange('status', e.target.value)}
                          >
                            <option value="DRAFT">DRAFT</option>
                            <option value="SUBMITTED">SUBMITTED</option>
                            <option value="AUTHORISED">AUTHORISED</option>
                          </select>
                        ) : (
                          <span className="px-2 py-1 text-xs font-medium rounded-full bg-yellow-100 text-yellow-800">
                            {invoice.status}
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                  
                  {/* Line items */}
                  <div className="p-4">
                    <div className="flex justify-between mb-2">
                      <h4 className="font-medium text-gray-700">Line Items</h4>
                      {editingIndex === index && (
                        <button
                          onClick={handleAddLineItem}
                          className="text-sm text-teal-600 hover:text-teal-800"
                        >
                          + Add Line Item
                        </button>
                      )}
                    </div>
                    <div className="overflow-x-auto">
                      <table className="min-w-full divide-y divide-gray-200">
                        <thead>
                          <tr>
                            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
                            <th className="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
                            <th className="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Unit Price</th>
                            <th className="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                            {editingIndex === index && (
                              <>
                                <th className="px-4 py-2 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Account</th>
                                <th className="px-4 py-2 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Tax</th>
                                <th className="px-4 py-2 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                              </>
                            )}
                          </tr>
                        </thead>
                        <tbody className="divide-y divide-gray-200">
                          {(editingIndex === index ? editedInvoice.line_items : invoice.line_items).map((item, itemIndex) => (
                            <tr key={itemIndex}>
                              <td className="px-4 py-2 text-sm">
                                {editingIndex === index ? (
                                  <textarea
                                    className="border border-gray-300 rounded-md p-1 text-gray-700 w-full min-h-[60px] resize-vertical"
                                    value={item.description}
                                    onChange={(e) => handleLineItemChange(itemIndex, 'description', e.target.value)}
                                  />
                                ) : (
                                  <span className="text-gray-700 whitespace-pre-wrap">{item.description}</span>
                                )}
                              </td>
                              <td className="px-4 py-2 text-sm">
                                {editingIndex === index ? (
                                  <textarea
                                    className="border border-gray-300 rounded-md p-1 text-gray-700 w-full min-h-[60px] resize-vertical"
                                    value={item.item_code}
                                    onChange={(e) => handleLineItemChange(itemIndex, 'item_code', e.target.value)}
                                  />
                                ) : (
                                  <span className="text-gray-700 whitespace-pre-wrap">{item.item_code}</span>
                                )}
                              </td>
                              <td className="px-4 py-2 text-sm text-right">
                                {editingIndex === index ? (
                                  <input
                                    type="number"
                                    className="border border-gray-300 rounded-md p-1 text-gray-700 w-20 text-right"
                                    value={item.quantity}
                                    min="0" 
                                    step="0.01"
                                    onChange={(e) => handleLineItemChange(itemIndex, 'quantity', e.target.value)}
                                  />
                                ) : (
                                  <span className="text-gray-700">{item.quantity}</span>
                                )}
                              </td>
                              <td className="px-4 py-2 text-sm text-right">
                                {editingIndex === index ? (
                                  <input
                                    type="number"
                                    className="border border-gray-300 rounded-md p-1 text-gray-700 w-28 text-right"
                                    value={item.unit_amount}
                                    min="0" 
                                    step="0.01"
                                    onChange={(e) => handleLineItemChange(itemIndex, 'unit_amount', e.target.value)}
                                  />
                                ) : (
                                  <span className="text-gray-700">{formatCurrency(item.unit_amount)}</span>
                                )}
                              </td>
                              <td className="px-4 py-2 text-sm text-right text-gray-700">
                                {formatCurrency(item.quantity * item.unit_amount)}
                              </td>
                              {editingIndex === index && (
                                <>
                                  <td className="px-4 py-2 text-sm text-center">
                                    <select
                                      className="border border-gray-300 rounded-md p-1 text-gray-700 w-full"
                                      value={item.account_code}
                                      onChange={(e) => handleLineItemChange(itemIndex, 'account_code', e.target.value)}
                                    >
                                      {accounts.map((account) => (
                                        <option key={account.AccountID} value={account.Code}>
                                          {account.Name} ({account.Code})
                                        </option>
                                      ))}
                                    </select>
                                  </td>
                                  <td className="px-4 py-2 text-sm text-center">
                                    <select
                                      className="border border-gray-300 rounded-md p-1 text-gray-700 w-full"
                                      value={item.tax_type}
                                      onChange={(e) => handleLineItemChange(itemIndex, 'tax_type', e.target.value)}
                                    >
                                      {taxRates.map((tax) => (
                                        <option key={tax.TaxType} value={tax.TaxType}>
                                          {tax.Name} ({tax.TaxType})
                                        </option>
                                      ))}
                                    </select>
                                  </td>
                                  <td className="px-4 py-2 text-sm text-center">
                                    {editedInvoice.line_items.length > 1 && (
                                      <button
                                        onClick={() => handleRemoveLineItem(itemIndex)}
                                        className="text-red-600 hover:text-red-800"
                                        title="Remove line item"
                                      >
                                        <FaTimes />
                                      </button>
                                    )}
                                  </td>
                                </>
                              )}
                            </tr>
                          ))}
                        </tbody>
                        <tfoot>
                          <tr>
                            <td colSpan={editingIndex === index ? "7" : "4"} className="px-4 py-2 text-right font-medium text-gray-700">Subtotal:</td>
                            <td className="px-4 py-2 text-right font-medium text-gray-700">
                              {formatCurrency(calculateTotal(editingIndex === index ? editedInvoice : invoice))}
                            </td>
                          </tr>
                        </tfoot>
                      </table>
                    </div>
                  </div>
                </div>
              ))}
              
              <div className="flex justify-end mt-8">
                <Link 
                  href="/xero/scan"
                  className="mr-4 px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  Back
                </Link>
                <button
                  onClick={handleCreateInvoices}
                  disabled={isCreating || editingIndex !== null}
                  className={`
                    px-6 py-3 bg-teal-600 text-white rounded-lg
                    hover:bg-teal-700 transition-colors
                    disabled:bg-teal-400 disabled:cursor-not-allowed
                    flex items-center justify-center min-w-[160px]
                  `}
                >
                  {isCreating ? (
                    <>
                      <FaSpinner className="animate-spin mr-2" />
                      Creating...
                    </>
                  ) : (
                    <>
                      <FaCheck className="mr-2" />
                      Create in Xero
                    </>
                  )}
                </button>
              </div>
            </div>
          ) : (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 text-yellow-800">
              <p>No valid invoice data was extracted from the scanned documents. Please try again or use different files.</p>
              <div className="mt-4">
                <Link
                  href="/xero/scan"
                  className="px-4 py-2 bg-yellow-200 text-yellow-800 rounded-md hover:bg-yellow-300 transition-colors inline-block"
                >
                  Try Again
                </Link>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
} 