'use client';

import { xeroApi } from '@/services/api';
import Link from 'next/link';
import { useEffect, useState } from 'react';
import { toast } from 'react-hot-toast';
import { FaChartBar, FaCheckCircle, FaCog, FaFileUpload, FaMoneyBillWave, FaS<PERSON>ner, FaUsers } from 'react-icons/fa';

export default function XeroPage() {
  const [isLoading, setIsLoading] = useState(true);
  const [isConnected, setIsConnected] = useState(false);
  const [error, setError] = useState(null);
  const [orgData, setOrgData] = useState(null);
  const [invoices, setInvoices] = useState([]);
  const [contacts, setContacts] = useState([]);
  const [accounts, setAccounts] = useState([]);
  const [activeTab, setActiveTab] = useState('dashboard');
  const [selectedFiles, setSelectedFiles] = useState([]);
  const [isUploading, setIsUploading] = useState(false);
  const [scanResults, setScanResults] = useState(null);

  // Function to verify Xero connection
  const verifyXeroConnection = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      const response = await xeroApi.verifyConnection();
      
      if (response.status === 200) {
        const data = response.data;
        setIsConnected(data.is_connected);
        
        // If a specific reason was provided but it's just that no token was found,
        // this is a normal state rather than an error
        if (data.reason === "No token found") {
          console.log("No Xero token found - showing connect option");
        } else if (!data.is_connected && data.reason) {
          // For other connection issues, set as an error
          setError(data.reason);
        }
        
        if (data.organisation) {
          setOrgData(data.organisation);
        }
        
        // If connected, fetch initial data
        if (data.is_connected) {
          fetchXeroData();
        }
      } else {
        setIsConnected(false);
      }
    } catch (error) {
      console.error('Error verifying Xero connection:', error);
      setError(error.response?.data?.reason || 'Failed to verify Xero connection');
      setIsConnected(false);
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch data from Xero
  const fetchXeroData = async () => {
    try {
      // Fetch invoices
      const invoicesResponse = await xeroApi.getInvoices();
      if (invoicesResponse.status === 200) {
        setInvoices(invoicesResponse.data.invoices || []);
      }
      
      // Fetch contacts
      const contactsResponse = await xeroApi.getContacts();
      if (contactsResponse.status === 200) {
        setContacts(contactsResponse.data.contacts || []);
      }
      
      // Fetch accounts
      const accountsResponse = await xeroApi.getAccounts();
      if (accountsResponse.status === 200) {
        setAccounts(accountsResponse.data.accounts || []);
      }
    } catch (error) {
      console.error('Error fetching Xero data:', error);
      toast.error('Failed to fetch some Xero data');
    }
  };

  // Check connection status on component mount
  useEffect(() => {
    verifyXeroConnection();
  }, []);

  const handleConnectToXero = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      const response = await xeroApi.getAuthorizationUrl();
      
      if (response.status === 200) {
        window.location.href = response.data.authorization_url;
      } else {
        throw new Error('Failed to get authorization URL');
      }
    } catch (error) {
      console.error('Error connecting to Xero:', error);
      setError('Failed to connect to Xero');
      toast.error('Failed to connect to Xero');
    } finally {
      setIsLoading(false);
    }
  };
  
  const handleDisconnect = async () => {
    try {
      await xeroApi.disconnect();
      setIsConnected(false);
      setOrgData(null);
      toast.success('Disconnected from Xero successfully');
    } catch (error) {
      console.error('Error disconnecting from Xero:', error);
      toast.error('Failed to disconnect from Xero');
    }
  };
  
  // Handle file selection
  const handleFileSelect = (e) => {
    const files = Array.from(e.target.files);
    setSelectedFiles(files);
  };

  // Handle form submission
  const handleScanInvoices = async (e) => {
    e.preventDefault();
    
    if (selectedFiles.length === 0) {
      toast.error('Please select at least one PDF invoice to scan');
      return;
    }
    
    try {
      setIsUploading(true);
      
      const formData = new FormData();
      selectedFiles.forEach((file, index) => {
        formData.append(`invoice_${index}`, file);
      });
      
      const response = await xeroApi.scanInvoices(formData);
      
      if (response.status === 200) {
        setScanResults(response.data);
        toast.success('Invoices scanned successfully');
        setActiveTab('scan_results');
      } else {
        throw new Error('Failed to scan invoices');
      }
    } catch (error) {
      console.error('Error scanning invoices:', error);
      toast.error('Failed to scan invoices');
    } finally {
      setIsUploading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen bg-gray-50">
        <div className="p-8">
          <FaSpinner className="h-12 w-12 animate-spin text-teal-600" />
          <p className="mt-4 text-gray-600">Verifying Xero connection...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen bg-gray-50">
        <div className="p-8 bg-white rounded-lg shadow-md">
          <h1 className="text-2xl font-bold mb-4 text-red-600">Error</h1>
          <p className="text-gray-700 mb-6">{error}</p>
          <button
            onClick={verifyXeroConnection}
            className="px-6 py-3 bg-teal-600 text-white rounded-lg hover:bg-teal-700 transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  if (!isConnected) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen bg-gray-50">
        <div className="p-8 bg-white rounded-lg shadow-md max-w-lg w-full">
          <h1 className="text-2xl font-bold mb-6 text-gray-800">Connect to Xero</h1>
          <p className="text-gray-600 mb-6">
            Connect your Xero account to automatically sync invoices, contacts, and financial data.
          </p>
          <div className="mb-6 p-4 bg-teal-50 rounded-lg border border-teal-100">
            <h2 className="text-sm font-medium text-teal-800 mb-2">Benefits of connecting:</h2>
            <ul className="text-sm text-teal-700 space-y-1">
              <li>• Sync invoices automatically</li>
              <li>• Import contacts and customer data</li>
              <li>• Access financial reports</li>
              <li>• Streamline your accounting workflow</li>
            </ul>
          </div>
          <button
            onClick={handleConnectToXero}
            disabled={isLoading}
            className={`
              w-full px-6 py-3 bg-teal-600 text-white rounded-lg
              hover:bg-teal-700 transition-colors
              disabled:bg-teal-400 disabled:cursor-not-allowed
              flex items-center justify-center
            `}
          >
            {isLoading ? 'Connecting...' : 'Connect to Xero'}
          </button>
        </div>
      </div>
    );
  }

  // If connected, show the Xero dashboard with tabs
  return (
    <div className="p-6">
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-2xl font-bold text-gray-800">Xero Automation</h1>
          <div className="flex items-center space-x-2">
            <FaCheckCircle className="text-green-500" />
            <span className="text-sm text-gray-600">Connected</span>
            <button 
              onClick={handleDisconnect}
              className="ml-4 text-sm text-red-600 hover:text-red-800"
            >
              Disconnect
            </button>
          </div>
        </div>

        {/* Organization info */}
        {orgData && (
          <div className="mb-6 p-4 bg-gray-50 rounded-lg">
            <h2 className="text-lg text-black font-semibold mb-2">Organization Details</h2>
            <p className="text-gray-700">Name: {orgData.Name}</p>
            {orgData.LegalName && <p className="text-gray-700">Legal Name: {orgData.LegalName}</p>}
            {orgData.OrganisationType && <p className="text-gray-700">Type: {orgData.OrganisationType}</p>}
          </div>
        )}

        {/* Invoice Scanner Button */}
        <div className="mb-6">
          <Link 
            href="/xero/scan"
            className="inline-flex items-center px-6 py-3 bg-teal-600 text-white rounded-lg hover:bg-teal-700 transition-colors"
          >
            <FaFileUpload className="mr-2" />
            Scan Invoices
          </Link>
          <p className="mt-2 text-sm text-gray-600">
            Upload and scan PDF invoices to automatically extract data and import into Xero.
          </p>
        </div>
        
        {/* Tabs */}
        <div className="border-b border-gray-200 mb-6">
          <nav className="flex space-x-8">
            <button
              onClick={() => setActiveTab('dashboard')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'dashboard'
                  ? 'border-teal-500 text-teal-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Dashboard
            </button>
            <button
              onClick={() => setActiveTab('invoices')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'invoices'
                  ? 'border-teal-500 text-teal-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Invoices
            </button>
            <button
              onClick={() => setActiveTab('contacts')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'contacts'
                  ? 'border-teal-500 text-teal-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Contacts
            </button>
            <button
              onClick={() => setActiveTab('accounts')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'accounts'
                  ? 'border-teal-500 text-teal-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Accounts
            </button>
            {scanResults && (
              <button
                onClick={() => setActiveTab('scan_results')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'scan_results'
                    ? 'border-teal-500 text-teal-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Scan Results
              </button>
            )}
          </nav>
        </div>
        
        {/* Tab content */}
        <div>
          {/* Dashboard tab */}
          {activeTab === 'dashboard' && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div className="p-4 border rounded-lg hover:shadow-md transition-shadow">
                <div className="flex items-center justify-between mb-2">
                  <h3 className="text-black font-semibold">Invoices</h3>
                  <FaMoneyBillWave className="text-teal-500" />
                </div>
                <p className="text-gray-600 mb-2">Total invoices: {invoices.length}</p>
                <button 
                  onClick={() => setActiveTab('invoices')} 
                  className="text-sm text-teal-600 hover:text-teal-800"
                >
                  View all
                </button>
              </div>
              
              <div className="p-4 border rounded-lg hover:shadow-md transition-shadow">
                <div className="flex items-center justify-between mb-2">
                  <h3 className="text-black font-semibold">Contacts</h3>
                  <FaUsers className="text-teal-500" />
                </div>
                <p className="text-gray-600 mb-2">Total contacts: {contacts.length}</p>
                <button 
                  onClick={() => setActiveTab('contacts')} 
                  className="text-sm text-teal-600 hover:text-teal-800"
                >
                  View all
                </button>
              </div>
              
              <div className="p-4 border rounded-lg hover:shadow-md transition-shadow">
                <div className="flex items-center justify-between mb-2">
                  <h3 className="text-black font-semibold">Financial Accounts</h3>
                  <FaChartBar className="text-teal-500" />
                </div>
                <p className="text-gray-600 mb-2">Total accounts: {accounts.length}</p>
                <button 
                  onClick={() => setActiveTab('accounts')} 
                  className="text-sm text-teal-600 hover:text-teal-800"
                >
                  View all
                </button>
              </div>
            </div>
          )}
          
          {/* Invoices tab */}
          {activeTab === 'invoices' && (
            <div>
              <h2 className="text-lg text-black font-semibold mb-4">Invoices</h2>
              {invoices.length > 0 ? (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Invoice #</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {invoices.map((invoice) => (
                        <tr key={invoice.InvoiceID}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{invoice.InvoiceNumber}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{invoice.Date}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{invoice.Contact?.Name}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{invoice.Total}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            <span className={`px-2 py-1 text-xs rounded-full ${
                              invoice.Status === 'PAID' ? 'bg-green-100 text-green-800' :
                              invoice.Status === 'DRAFT' ? 'bg-gray-100 text-gray-800' : 
                              'bg-yellow-100 text-yellow-800'
                            }`}>
                              {invoice.Status}
                            </span>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="text-center py-10 bg-gray-50 rounded-lg">
                  <p className="text-gray-500">No invoices found.</p>
                  <button 
                    onClick={fetchXeroData} 
                    className="mt-2 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-teal-700 bg-teal-100 hover:bg-teal-200"
                  >
                    <FaCog className="mr-2" /> Refresh Data
                  </button>
                </div>
              )}
            </div>
          )}
          
          {/* Contacts tab */}
          {activeTab === 'contacts' && (
            <div>
              <h2 className="text-lg text-black font-semibold mb-4">Contacts</h2>
              {contacts.length > 0 ? (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Phone</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {contacts.map((contact) => (
                        <tr key={contact.ContactID}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{contact.Name}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{contact.EmailAddress || '-'}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{contact.Phones?.[0]?.PhoneNumber || '-'}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="text-center py-10 bg-gray-50 rounded-lg">
                  <p className="text-gray-500">No contacts found.</p>
                  <button 
                    onClick={fetchXeroData} 
                    className="mt-2 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-teal-700 bg-teal-100 hover:bg-teal-200"
                  >
                    <FaCog className="mr-2" /> Refresh Data
                  </button>
                </div>
              )}
            </div>
          )}
          
          {/* Accounts tab */}
          {activeTab === 'accounts' && (
            <div>
              <h2 className="text-lg text-black font-semibold mb-4">Financial Accounts</h2>
              {accounts.length > 0 ? (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Code</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Balance</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {accounts.map((account) => (
                        <tr key={account.AccountID}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{account.Code}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{account.Name}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{account.Type}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {account.CurrentBalance ? `$${parseFloat(account.CurrentBalance).toFixed(2)}` : '-'}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="text-center py-10 bg-gray-50 rounded-lg">
                  <p className="text-gray-500">No accounts found.</p>
                  <button 
                    onClick={fetchXeroData} 
                    className="mt-2 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-teal-700 bg-teal-100 hover:bg-teal-200"
                  >
                    <FaCog className="mr-2" /> Refresh Data
                  </button>
                </div>
              )}
            </div>
          )}
          
          {/* Scan Results tab */}
          {activeTab === 'scan_results' && scanResults && (
            <div>
              <h2 className="text-lg text-black font-semibold mb-4">Scan Results</h2>
              <div className="bg-gray-50 p-4 rounded-lg">
                <p className="font-medium text-gray-700 mb-2">Raw JSON Response:</p>
                <pre className="bg-gray-100 p-4 text-gray-700 rounded overflow-auto max-h-96 text-xs">
                  {JSON.stringify(scanResults, null, 2)}
                </pre>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
} 