"use client";

import { motion } from "framer-motion";

export default function InvoiceAutomation() {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="space-y-6"
    >
      <h2 className="text-2xl font-bold mb-6 text-gray-800">Invoice Automation</h2>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-gradient-to-br from-teal-50 to-cyan-50 p-8 rounded-lg border border-teal-100">
          <h3 className="font-medium text-teal-700 mb-4">Recurring Invoices</h3>
          <p className="text-gray-600">Set up automatic billing cycles for your regular clients.</p>
        </div>
        <div className="bg-gradient-to-br from-cyan-50 to-teal-50 p-8 rounded-lg border border-cyan-100">
          <h3 className="font-medium text-teal-700 mb-4">Payment Reminders</h3>
          <p className="text-gray-600">Automatically send payment reminders to clients with outstanding invoices.</p>
        </div>
      </div>
    </motion.div>
  );
} 