'use client';

import SetPasswordForm from '@/components/auth/SetPasswordForm';
import { useSearchParams } from 'next/navigation';
import { Suspense, useEffect, useState } from 'react';

// Client component that uses searchParams
function SetPasswordContent() {
  const searchParams = useSearchParams();
  const email = searchParams.get('email');
  const [initialEmail, setInitialEmail] = useState('');

  useEffect(() => {
    if (email) {
      setInitialEmail(email);
    }
  }, [email]);

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gray-100 px-4 py-12">
      <div className="w-full max-w-md">
        <div className="mb-8 text-center">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">MizuFlow</h1>
          <p className="text-gray-600">Complete your account setup</p>
        </div>
        <SetPasswordForm initialEmail={initialEmail} />
      </div>
    </div>
  );
}

// Main page component with Suspense boundary
export default function SetPasswordPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen flex flex-col items-center justify-center bg-gray-100">
        <div className="w-full max-w-md text-center">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">MizuFlow</h1>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    }>
      <SetPasswordContent />
    </Suspense>
  );
} 