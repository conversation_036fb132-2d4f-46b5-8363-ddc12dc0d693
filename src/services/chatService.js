import Cookies from "js-cookie";

class ChatService {
  constructor() {
    this.baseURL = process.env.NEXT_PUBLIC_CURRENT_BACKEND_SERVER;
    this.creditUpdateCallback = null;
  }

  // Set callback for credit updates
  setCreditUpdateCallback(callback) {
    this.creditUpdateCallback = callback;
  }

  // Get authentication headers
  getAuthHeaders() {
    const token = Cookies.get("token");
    return {
      "Content-Type": "application/json",
      ...(token && { Authorization: `Bearer ${token}` }),
    };
  }

  // Send a chat message
  async sendMessage(data) {
    try {
      const response = await fetch(`${this.baseURL}/tax-acc-assistant/chat/`, {
        method: "POST",
        headers: this.getAuthHeaders(),
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();

        // Handle insufficient credits (402 Payment Required)
        if (response.status === 402) {
          // Trigger credit balance refresh on insufficient credits
          if (this.creditUpdateCallback) {
            this.creditUpdateCallback();
          }
          throw new Error(errorData.error || "Insufficient credit balance");
        }

        throw new Error(errorData.error || "Failed to send message");
      }

      const result = await response.json();

      // Trigger credit balance update after successful chat request
      // Since credits are deducted on the backend, we need to refresh the balance
      if (this.creditUpdateCallback) {
        // Small delay to ensure backend transaction is complete
        setTimeout(() => {
          this.creditUpdateCallback();
        }, 500);
      }

      return result;
    } catch (error) {
      console.error("Error sending message:", error);
      throw error;
    }
  }

  // Get chat history
  async getChatHistory(limit = 10) {
    try {
      const response = await fetch(
        `${this.baseURL}/tax-acc-assistant/chat-history/?limit=${limit}`,
        {
          method: "GET",
          headers: this.getAuthHeaders(),
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to fetch chat history");
      }

      const data = await response.json();
      return data.history || [];
    } catch (error) {
      console.error("Error fetching chat history:", error);
      throw error;
    }
  }

  // Send memo via email
  async sendMemoEmail(data) {
    try {
      const response = await fetch(
        `${this.baseURL}/tax-acc-assistant/send-memo-email/`,
        {
          method: "POST",
          headers: this.getAuthHeaders(),
          body: JSON.stringify(data),
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to send email");
      }

      return await response.json();
    } catch (error) {
      console.error("Error sending memo email:", error);
      throw error;
    }
  }

  // Download memo file
  async downloadMemo(memoUrl) {
    try {
      const token = Cookies.get("token");
      if (!token) {
        throw new Error("No authentication token found. Please log in again.");
      }

      const response = await fetch(memoUrl, {
        method: "GET",
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        let errorMessage = "Failed to download memo";
        try {
          const errorData = await response.json();
          errorMessage = errorData.error || errorMessage;
        } catch {
          // If response is not JSON, use status text
          errorMessage = response.statusText || errorMessage;
        }
        throw new Error(errorMessage);
      }

      // Get filename from Content-Disposition header or URL
      const contentDisposition = response.headers.get("Content-Disposition");
      let filename = "memo.docx";

      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename="(.+)"/);
        if (filenameMatch) {
          filename = filenameMatch[1];
        }
      } else {
        // Extract filename from URL
        const urlParts = memoUrl.split("/");
        const lastPart = urlParts[urlParts.length - 1];
        if (lastPart && lastPart.includes(".")) {
          filename = lastPart;
        }
      }

      // Create blob and download
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      return true;
    } catch (error) {
      console.error("Error downloading memo:", error);
      throw error;
    }
  }

  // Get all chat sessions for the user
  async getAllChatSessions() {
    try {
      const response = await fetch(
        `${this.baseURL}/tax-acc-assistant/chat-history/?limit=100`,
        {
          method: "GET",
          headers: this.getAuthHeaders(),
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to fetch chat sessions");
      }

      const data = await response.json();
      return data.history || [];
    } catch (error) {
      console.error("Error fetching chat sessions:", error);
      throw error;
    }
  }

  // Get messages for a specific chat session
  async getChatSession(chatSessionId) {
    try {
      const allSessions = await this.getAllChatSessions();
      return allSessions.find(session => session.chat_session_id === chatSessionId);
    } catch (error) {
      console.error("Error fetching chat session:", error);
      throw error;
    }
  }
}

// Create and export a singleton instance
export const chatService = new ChatService();
