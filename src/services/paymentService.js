import Cookies from 'js-cookie';
import { API_ENDPOINTS } from '@/config/api';

/**
 * Payment Service for handling Stripe payments
 */
class PaymentService {
  /**
   * Get authentication headers
   */
  getAuthHeaders() {
    const token = Cookies.get('token');
    return {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
    };
  }

  /**
   * Create a Stripe checkout session for credit purchase
   * @param {number} amount_usd - Amount in USD
   * @param {string} currency - Currency code (default: 'usd')
   * @returns {Promise<Object>} Checkout session data
   */
  async createCheckoutSession(amount_usd, currency = 'usd') {
    try {
      const response = await fetch(API_ENDPOINTS.CREATE_CHECKOUT_SESSION, {
        method: 'POST',
        headers: this.getAuthHeaders(),
        body: JSON.stringify({
          amount: amount_usd,
          currency: currency
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create checkout session');
      }

      const data = await response.json();
      return data;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Redirect to Stripe checkout
   * @param {string} checkoutUrl - Stripe checkout URL
   */
  redirectToCheckout(checkoutUrl) {
    window.location.href = checkoutUrl;
  }

  /**
   * Verify payment success
   * @param {string} sessionId - Stripe session ID
   * @returns {Promise<Object>} Payment verification data
   */
  async verifyPaymentSuccess(sessionId) {
    try {
      const url = new URL(API_ENDPOINTS.PAYMENT_SUCCESS);
      url.searchParams.append('session_id', sessionId);

      const response = await fetch(url.toString(), {
        method: 'GET',
        headers: this.getAuthHeaders(),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to verify payment');
      }

      return await response.json();
    } catch (error) {
      console.error('Error verifying payment:', error);
      throw error;
    }
  }

  /**
   * Get purchase history for the current user
   * @returns {Promise<Array>} Purchase history
   */
  async getPurchaseHistory() {
    try {
      const response = await fetch(API_ENDPOINTS.PURCHASE_HISTORY, {
        method: 'GET',
        headers: this.getAuthHeaders(),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch purchase history');
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching purchase history:', error);
      throw error;
    }
  }

  /**
   * Convert USD prices to different currencies
   * @param {Array<number>} prices - Array of prices in USD
   * @param {string} currency - Target currency code (default: 'usd')
   * @returns {Promise<Object>} Converted prices data
   */
  async convertPrices(prices, currency = 'usd') {
    try {
      const response = await fetch(API_ENDPOINTS.CONVERT_PRICES, {
        method: 'POST',
        headers: this.getAuthHeaders(),
        body: JSON.stringify({
          prices: prices,
          currency: currency
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to convert prices');
      }

      const data = await response.json();
      return data;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Purchase credits using predefined packages
   * @param {Object} creditPackage - Credit package object with price and credits
   * @param {string} currency - Currency code (default: 'usd')
   * @returns {Promise<void>} Redirects to Stripe checkout
   */
  async purchaseCredits(creditPackage, currency = 'usd') {
    try {
      // Create checkout session
      const checkoutData = await this.createCheckoutSession(creditPackage.price, currency);

      // Redirect to Stripe checkout
      this.redirectToCheckout(checkoutData.checkout_url);
    } catch (error) {
      console.error('Error purchasing credits:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const paymentService = new PaymentService();
export default paymentService;
