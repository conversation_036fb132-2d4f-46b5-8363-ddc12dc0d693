#!/bin/bash

# db-setup.sh - One-time script to initialize MySQL database user and permissions

echo "Setting up MySQL database user and permissions..."

# Check if containers are running
if ! docker ps | grep -q mizu_mysql; then
    echo "Error: MySQL container is not running. Please start containers first with './docker-run.sh start'"
    exit 1
fi

# Get environment variables from .env file
if [ ! -f .env ]; then
    echo "Error: .env file not found. Please create it first."
    exit 1
fi

# Source environment variables from .env file
source .env

echo "Creating database and user..."

# Connect to MySQL container and set up database user
docker exec -i mizu_mysql mysql -uroot -p"${DB_PASSWORD}" <<EOF
# Create database if it doesn't exist
CREATE DATABASE IF NOT EXISTS ${DB_NAME};

# Create user with password if it doesn't exist
CREATE USER IF NOT EXISTS '${DB_USER}'@'%' IDENTIFIED BY '${DB_PASSWORD}';

# Grant all privileges on the database to the user
GRANT ALL PRIVILEGES ON ${DB_NAME}.* TO '${DB_USER}'@'%';

# Grant additional privileges needed for Django migrations
GRANT CREATE, ALTER, INDEX, DROP, REFERENCES ON ${DB_NAME}.* TO '${DB_USER}'@'%';

# Apply the changes
FLUSH PRIVILEGES;

# Show the grants for the user to verify
SHOW GRANTS FOR '${DB_USER}'@'%';
EOF

echo "Database setup completed successfully!"
echo "User '${DB_USER}' has been created with access to database '${DB_NAME}'."
echo "You can now use the application with your Docker setup."