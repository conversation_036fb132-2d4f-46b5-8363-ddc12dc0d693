version: "3.8"

services:
  nextjs:
    build:
      context: .
      dockerfile: Dockerfile.dev
    env_file:
      - .env.development.local   
    command: npm run dev
    image: mizu/demo-nextjs:latest
    container_name: mizu-demo-nextjs
    environment:
      - NODE_ENV=development
      - PORT=3000
      - HOSTNAME=0.0.0.0
    ports:
      - "3000:3000"
    networks:
      - app-network
    restart: unless-stopped

  nginx:
    build:
      context: ./nginx
      dockerfile: Dockerfile
    image: mizu/demo-next-nginx:latest
    container_name: mizu-demo-nextjs-nginx
    ports:
      - "3005:80"
    depends_on:
      - nextjs
    networks:
      - app-network
    restart: unless-stopped

networks:
  app-network:
    driver: bridge
