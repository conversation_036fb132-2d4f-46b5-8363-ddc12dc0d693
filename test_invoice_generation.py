#!/usr/bin/env python3
"""
Test script for the Sales Invoice Generator workflow.
This script tests the complete workflow from CSV upload to invoice generation.
"""

import os
import sys
import django
import json
import csv
import tempfile
from io import StringIO

# Setup Django environment
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
django.setup()

from django.contrib.auth import get_user_model
from django.core.files.uploadedfile import SimpleUploadedFile
from apps.invoice_generator.models import CompanyTemplate
from apps.invoice_generator.views import GenerateInvoicesView
from apps.invoice_generator.csv_processor import CSVProcessor
from rest_framework.test import APIRequestFactory
from django.contrib.auth.models import AnonymousUser

User = get_user_model()


def create_test_user():
    """Create a test user for the workflow."""
    user, created = User.objects.get_or_create(
        email="<EMAIL>",
        defaults={"first_name": "Test", "last_name": "User", "is_active": True},
    )
    if created:
        user.set_password("testpassword123")
        user.save()
    return user


def create_test_company_template(user):
    """Create a test company template."""
    template, created = CompanyTemplate.objects.get_or_create(
        user=user,
        template_name="Test Company Template",
        defaults={
            "company_name": "Test Company Inc.",
            "address_line_1": "123 Test Street",
            "city": "Test City",
            "state_province": "Test State",
            "postal_code": "12345",
            "country": "Test Country",
            "phone": "******-0123",
            "email": "<EMAIL>",
            "website": "https://testcompany.com",
            "default_payment_terms": "Net 30 days",
            "template_id": "clean_business",
        },
    )
    return template


def create_test_csv_data():
    """Create test CSV data similar to test_sales_data.csv."""
    csv_data = [
        {
            "Invoice ID": "INV-2024-001",
            "Bill Date": "2024-01-15",
            "Payment Due": "2024-02-14",
            "Client Company": "Acme Corporation",
            "Contact Email": "<EMAIL>",
            "Client Address": "123 Business Ave, New York, NY 10001",
            "Phone Number": "******-0101",
            "Service Description": "Website Development",
            "Qty": "1",
            "Unit Rate": "2500.00",
            "Line Amount": "2500.00",
            "Tax Percent": "8.5",
            "Tax": "212.50",
            "Discount Amount": "0.00",
            "Order Reference": "PO-ACME-001",
            "Notes": "Initial website development project",
        },
        {
            "Invoice ID": "INV-2024-002",
            "Bill Date": "2024-01-16",
            "Payment Due": "2024-02-15",
            "Client Company": "Beta Corp",
            "Contact Email": "<EMAIL>",
            "Client Address": "456 Corporate Blvd, Los Angeles, CA 90210",
            "Phone Number": "******-0202",
            "Service Description": "SEO Optimization",
            "Qty": "1",
            "Unit Rate": "800.00",
            "Line Amount": "800.00",
            "Tax Percent": "8.5",
            "Tax": "68.00",
            "Discount Amount": "0.00",
            "Order Reference": "PO-BETA-001",
            "Notes": "Monthly SEO services",
        },
    ]
    return csv_data


def test_csv_processing():
    """Test CSV column detection and mapping suggestions."""
    print("Testing CSV processing...")

    # Create a CSV file in memory
    csv_content = StringIO()
    csv_data = create_test_csv_data()

    if csv_data:
        fieldnames = csv_data[0].keys()
        writer = csv.DictWriter(csv_content, fieldnames=fieldnames)
        writer.writeheader()
        writer.writerows(csv_data)

        # Create a file-like object
        csv_file = SimpleUploadedFile(
            "test_data.csv",
            csv_content.getvalue().encode("utf-8"),
            content_type="text/csv",
        )

        # Test column detection
        columns = CSVProcessor.detect_columns(csv_file)
        print(f"Detected columns: {columns}")

        # Test mapping suggestions
        mapping_result = CSVProcessor.suggest_column_mappings(columns)
        print(f"Suggested mappings: {mapping_result['suggested_mappings']}")
        print(f"Confidence scores: {mapping_result['confidence_scores']}")

        return columns, mapping_result

    return None, None


def test_invoice_generation():
    """Test the complete invoice generation workflow."""
    print("\nTesting invoice generation workflow...")

    # Create test user and company template
    user = create_test_user()
    company_template = create_test_company_template(user)

    # Test CSV processing
    columns, mapping_result = test_csv_processing()
    if not columns or not mapping_result:
        print("CSV processing failed!")
        return False

    # Create test data for invoice generation
    csv_data = create_test_csv_data()
    column_mappings = mapping_result["suggested_mappings"]

    # Create a mock request
    factory = APIRequestFactory()
    request_data = {
        "csv_data": csv_data,
        "column_mappings": column_mappings,
        "company_template_id": company_template.id,
        "template_id": company_template.template_id,
    }

    request = factory.post(
        "/api/v1/invoice-generator/generate-invoices/", data=request_data, format="json"
    )
    request.user = user

    # Test the invoice generation view
    view = GenerateInvoicesView()

    try:
        response = view.post(request)
        print(f"Response status: {response.status_code}")

        if response.status_code == 200:
            response_data = response.data
            print(f"Success: {response_data.get('success', False)}")
            print(f"Generated invoices: {response_data.get('total_generated', 0)}")

            if response_data.get("generated_invoices"):
                for invoice in response_data["generated_invoices"]:
                    print(f"  - {invoice['client_name']}: {invoice['filename']}")

            return True
        else:
            print(f"Error: {response.data}")
            return False

    except Exception as e:
        print(f"Exception during invoice generation: {str(e)}")
        return False


def main():
    """Run all tests."""
    print("Starting Sales Invoice Generator Tests")
    print("=" * 50)

    try:
        # Test invoice generation workflow
        success = test_invoice_generation()

        if success:
            print("\n✅ All tests passed!")
            print("The Sales Invoice Generator workflow is working correctly.")
        else:
            print("\n❌ Tests failed!")
            print("There are issues with the Sales Invoice Generator workflow.")

    except Exception as e:
        print(f"\n❌ Test execution failed: {str(e)}")
        import traceback

        traceback.print_exc()


if __name__ == "__main__":
    main()
