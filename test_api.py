#!/usr/bin/env python
"""
Test script for the sales invoice workflow API endpoints.
"""

import os
import sys
import django
import requests
import json

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from django.contrib.auth import get_user_model
from apps.invoice_generator.models import CompanyTemplate

User = get_user_model()

# API base URL
BASE_URL = "http://localhost:8005/api/v1/invoice-generator"

def create_test_user():
    """Create a test user for API testing."""
    try:
        user = User.objects.get(email="<EMAIL>")
        print(f"Using existing test user: {user.email}")
    except User.DoesNotExist:
        user = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            first_name="Test",
            last_name="User",
            is_active=True
        )
        print(f"Created test user: {user.email}")
    return user

def get_auth_token():
    """Get authentication token for the test user."""
    response = requests.post("http://localhost:8005/api/v1/users/token/", {
        "email": "<EMAIL>",
        "password": "testpass123"
    })
    
    if response.status_code == 200:
        token = response.json()["access"]
        print("✓ Authentication successful")
        return token
    else:
        print(f"✗ Authentication failed: {response.status_code}")
        print(response.text)
        return None

def test_company_template_creation(token):
    """Test creating a company template."""
    headers = {"Authorization": f"Bearer {token}"}
    
    template_data = {
        "template_name": "Test Company Template",
        "template_id": "clean_business",
        "template_display_name": "Clean Business",
        "company_name": "Test Company Inc",
        "address_line_1": "123 Test Street",
        "city": "Test City",
        "state_province": "Test State",
        "postal_code": "12345",
        "country": "Test Country",
        "phone": "******-0123",
        "email": "<EMAIL>",
        "website": "https://testcompany.com"
    }
    
    response = requests.post(f"{BASE_URL}/company-templates/", 
                           json=template_data, headers=headers)
    
    if response.status_code == 201:
        template = response.json()
        print(f"✓ Company template created: {template['template_name']} (ID: {template['id']})")
        return template
    else:
        print(f"✗ Company template creation failed: {response.status_code}")
        print(response.text)
        return None

def test_company_template_list(token):
    """Test listing company templates."""
    headers = {"Authorization": f"Bearer {token}"}
    
    response = requests.get(f"{BASE_URL}/company-templates/", headers=headers)
    
    if response.status_code == 200:
        templates = response.json()["results"]
        print(f"✓ Retrieved {len(templates)} company templates")
        return templates
    else:
        print(f"✗ Company template listing failed: {response.status_code}")
        print(response.text)
        return []

def test_csv_upload():
    """Test CSV upload and column detection."""
    token = get_auth_token()
    if not token:
        return
        
    headers = {"Authorization": f"Bearer {token}"}
    
    # Upload the test CSV file
    with open('test_sales_data.csv', 'rb') as f:
        files = {'file': ('test_sales_data.csv', f, 'text/csv')}
        response = requests.post(f"{BASE_URL}/csv-upload/", 
                               files=files, headers=headers)
    
    if response.status_code == 200:
        result = response.json()
        print(f"✓ CSV upload successful")
        print(f"  - Detected {len(result['detected_columns'])} columns")
        print(f"  - Generated {len(result['suggested_mappings'])} mappings")
        return result
    else:
        print(f"✗ CSV upload failed: {response.status_code}")
        print(response.text)
        return None

def test_workflow_start(token, template_id):
    """Test starting a sales workflow."""
    headers = {"Authorization": f"Bearer {token}"}
    
    workflow_data = {
        "company_template_id": template_id
    }
    
    response = requests.post(f"{BASE_URL}/workflows/start/", 
                           json=workflow_data, headers=headers)
    
    if response.status_code == 200:
        workflow = response.json()["workflow"]
        print(f"✓ Workflow started: ID {workflow['id']}, Status: {workflow['status']}")
        return workflow
    else:
        print(f"✗ Workflow start failed: {response.status_code}")
        print(response.text)
        return None

def main():
    """Run all API tests."""
    print("🧪 Testing Sales Invoice Workflow API")
    print("=" * 50)
    
    # Create test user
    user = create_test_user()
    
    # Get authentication token
    token = get_auth_token()
    if not token:
        return
    
    # Test company template creation
    template = test_company_template_creation(token)
    if not template:
        return
    
    # Test company template listing
    templates = test_company_template_list(token)
    
    # Test CSV upload
    csv_result = test_csv_upload()
    
    # Test workflow start
    if template:
        workflow = test_workflow_start(token, template['id'])
    
    print("\n" + "=" * 50)
    print("✅ All tests completed!")

if __name__ == "__main__":
    main()
