#!/usr/bin/env python3
"""
Simple test for invoice generation without using the view.
"""

import os
import django
from django.conf import settings

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from django.contrib.auth import get_user_model
from apps.invoice_generator.models import CompanyTemplate
import pystache
import weasyprint
from datetime import datetime

User = get_user_model()

def test_simple_invoice_generation():
    """Test simple invoice generation."""
    print("Testing simple invoice generation...")
    
    # Create test user
    user, created = User.objects.get_or_create(
        email='<EMAIL>',
        defaults={
            'first_name': 'Test',
            'last_name': 'User',
            'is_active': True
        }
    )
    
    # Create test company template
    company_template, created = CompanyTemplate.objects.get_or_create(
        user=user,
        template_name='Test Company Template',
        defaults={
            'company_name': 'Test Company Inc.',
            'address_line_1': '123 Test Street',
            'city': 'Test City',
            'state_province': 'Test State',
            'postal_code': '12345',
            'country': 'Test Country',
            'phone': '******-0123',
            'email': '<EMAIL>',
            'website': 'https://testcompany.com',
            'default_payment_terms': 'Net 30 days',
            'template_id': 'clean_business',
            'bank_name': 'Test Bank',
            'account_number': '*********',
            'routing_number': '*********',
            'tax_id': 'TAX123456',
            'business_registration': 'REG789012'
        }
    )
    
    # Test data
    invoice_data = {
        'invoice_number': 'INV-2024-001',
        'bill_date': '2024-01-15',
        'due_date': '2024-02-14',
        'client_company': 'Acme Corporation',
        'contact_email': '<EMAIL>',
        'client_address': '123 Business Ave, New York, NY 10001',
        'phone_number': '******-0101',
        'service_description': 'Website Development',
        'quantity': '1',
        'unit_rate': '2500.00',
        'line_amount': '2500.00',
        'tax_percent': '8.5',
        'tax_value': '212.50',
        'discount_amount': '0.00',
        'order_reference': 'PO-ACME-001',
        'notes': 'Initial website development project',
        
        # Company data
        'company_name': company_template.company_name,
        'company_address_line_1': company_template.address_line_1,
        'company_city': company_template.city,
        'company_state_province': company_template.state_province,
        'company_postal_code': company_template.postal_code,
        'company_country': company_template.country,
        'company_phone': company_template.phone,
        'company_email': company_template.email,
        'company_website': company_template.website,
        'payment_terms': company_template.default_payment_terms,
        'bank_name': company_template.bank_name,
        'account_number': company_template.account_number,
        'routing_number': company_template.routing_number,
        'tax_id': company_template.tax_id,
        'business_registration': company_template.business_registration,
    }
    
    try:
        # Get template
        templates_dir = os.path.join(settings.BASE_DIR, "invoice_templates")
        template_file = f"{company_template.template_id}.html"
        template_path = os.path.join(templates_dir, template_file)
        
        print(f"Loading template from: {template_path}")
        
        with open(template_path, "r", encoding="utf-8") as file:
            template_html = file.read()
        
        print(f"Template loaded, length: {len(template_html)}")
        
        # Render template
        print("Rendering template with pystache...")
        rendered_html = pystache.render(template_html, invoice_data)
        
        print(f"Template rendered successfully, length: {len(rendered_html)}")
        
        # Generate PDF
        print("Generating PDF with weasyprint...")
        pdf_bytes = weasyprint.HTML(string=rendered_html).write_pdf()
        
        if pdf_bytes:
            print(f"✅ PDF generated successfully! Size: {len(pdf_bytes)} bytes")
            
            # Save to file for verification
            with open('test_invoice.pdf', 'wb') as f:
                f.write(pdf_bytes)
            print("✅ PDF saved as test_invoice.pdf")
            
            return True
        else:
            print("❌ Failed to generate PDF")
            return False
            
    except Exception as e:
        print(f"❌ Exception during invoice generation: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run the test."""
    print("Simple Invoice Generation Test")
    print("=" * 40)
    
    success = test_simple_invoice_generation()
    
    if success:
        print("\n✅ Test passed! Invoice generation is working.")
    else:
        print("\n❌ Test failed! There are issues with invoice generation.")

if __name__ == '__main__':
    main()
