# Stripe Webhook Synchronization Guide

This guide explains how to handle Stripe webhook synchronization issues and prevent stuck payments in the MizuFlow credit purchase system.

## Problem Overview

When Stripe webhooks are not properly configured or miss events, payments can get stuck in a "PENDING" state even though they were successfully processed by <PERSON><PERSON>. This creates a mismatch between <PERSON><PERSON>'s records and your local database.

## Run stripe cli in local development

```bash
stripe login
stripe listen --forward-to localhost:8005/api/v1/payments/webhook/
```

## Solutions Implemented

### 1. 🔧 Quick Fix Script (`fix_stuck_payment.py`)

**Use this for immediate resolution of stuck payments.**

```bash
# Fix a specific payment by session ID
python fix_stuck_payment.py --session-id cs_test_xxxxxxxxxxxxx

# Fix all pending payments for a user
python fix_stuck_payment.py --user-email <EMAIL>

# Fix all pending payments from last 7 days
python fix_stuck_payment.py --all-pending
```

**Example output:**

```
🔧 MizuFlow Payment Sync Tool
========================================
🔑 Using Stripe key: sk_test_xxxxx...

🎯 Fixing specific session: cs_test_xxxxxxxxxxxxx
🔍 Found purchase record:
   ID: 123
   User: <EMAIL>
   Amount: $29.99 USD
   Credits: 2999
   Status: pending
   Created: 2024-01-15 10:30:00

📋 Stripe session cs_test_xxxxxxxxxxxxx:
   Status: complete
   Payment Status: paid
   Amount: 29.99 USD

✅ Successfully completed purchase 123
   Added 2999 <NAME_EMAIL>
```

### 2. 🛠️ Django Management Command

**Use this for ongoing maintenance and batch operations.**

```bash
# Sync specific session
python manage.py sync_payments --session-id cs_test_xxxxxxxxxxxxx

# Sync all pending payments for a user
python manage.py sync_payments --user-email <EMAIL>

# Sync all pending payments from last 7 days (default)
python manage.py sync_payments

# Dry run to see what would be synced
python manage.py sync_payments --dry-run

# Force sync even if payment appears to be processing
python manage.py sync_payments --force

# Look back 14 days instead of 7
python manage.py sync_payments --days 14
```

### 3. 🔍 Webhook Health Check API

**Prevents creating checkout sessions when webhooks are not configured.**

```bash
# Check webhook health
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     http://localhost:8000/api/v1/payments/webhook-health/
```

**Response examples:**

✅ **Healthy (Production):**

```json
{
  "webhook_healthy": true,
  "message": "Webhook configuration appears valid",
  "is_development": false
}
```

⚠️ **Development Warning:**

```json
{
  "webhook_healthy": true,
  "message": "Development mode - ensure Stripe CLI is running",
  "recommendation": "Run: stripe listen --forward-to localhost:8005/api/v1/payments/webhook/",
  "is_development": true
}
```

❌ **Not Configured:**

```json
{
  "webhook_healthy": false,
  "message": "Webhook secret not configured",
  "recommendation": "Configure STRIPE_WEBHOOK_SECRET in environment"
}
```

### 4. 🚫 Checkout Session Protection

**Automatically prevents checkout session creation when webhooks are not configured in development.**

When trying to create a checkout session without proper webhook configuration:

```json
{
  "error": "Webhook not configured. Please set STRIPE_WEBHOOK_SECRET environment variable.",
  "webhook_required": true,
  "recommendation": "Run: stripe listen --forward-to localhost:8005/api/v1/payments/webhook/"
}
```

## Step-by-Step Resolution for Your Current Issue

### Immediate Fix

1. **Find your stuck payment session ID** from Stripe Dashboard
2. **Run the quick fix script:**

   ```bash
   python fix_stuck_payment.py --session-id cs_test_YOUR_SESSION_ID
   ```

### Alternative: Fix by user email

```bash
python fix_stuck_payment.py --user-email <EMAIL>
```

## Prevention for Future Development

### 1. Set up Stripe CLI for local development

```bash
# Install Stripe CLI
# https://stripe.com/docs/stripe-cli

# Login to your Stripe account
stripe login

# Forward webhooks to your local server
stripe listen --forward-to localhost:8005/api/v1/payments/webhook/
```

### 2. Configure environment variables

Add to your `.env` file:

```bash
STRIPE_SECRET_KEY=sk_test_xxxxxxxxxxxxx
STRIPE_PUBLISHABLE_KEY=pk_test_xxxxxxxxxxxxx
STRIPE_WEBHOOK_SECRET=whsec_xxxxxxxxxxxxx  # From stripe listen command
```

### 3. Verify webhook health before testing

```bash
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     http://localhost:8005/api/v1/payments/webhook-health/
```

## Production Deployment

### 1. Configure webhook endpoint in Stripe Dashboard

1. Go to Stripe Dashboard → Developers → Webhooks
2. Add endpoint: `https://your-domain.com/api/v1/payments/webhook/`
3. Select events:
   - `checkout.session.completed`
   - `checkout.session.expired`
   - `refund.created`
   - `refund.failed`
   - `charge.refunded`

### 2. Set production environment variables

```bash
STRIPE_WEBHOOK_SECRET=whsec_xxxxxxxxxxxxx  # From Stripe Dashboard
```

## Monitoring and Maintenance

### Regular Health Checks

Set up monitoring to check webhook health:

```bash
# Add to cron job or monitoring system
curl -f -H "Authorization: Bearer TOKEN" \
     https://your-domain.com/api/v1/payments/webhook-health/
```

### Periodic Payment Sync

Run periodic sync to catch any missed webhooks:

```bash
# Daily cron job
0 2 * * * cd /path/to/project && python manage.py sync_payments --days 1
```

## Troubleshooting

### Common Issues

1. **"No purchase record found"**
   - Check if the session ID is correct
   - Verify the purchase was created in your database

2. **"Stripe error: No such checkout session"**
   - Session ID might be expired or invalid
   - Check Stripe Dashboard for the correct session ID

3. **"Credits already added"**
   - Payment was already processed successfully
   - No action needed

### Debug Commands

```bash
# Check pending payments
python manage.py shell -c "
from apps.payments.models import CreditPurchase, PaymentStatus
pending = CreditPurchase.objects.filter(status=PaymentStatus.PENDING)
for p in pending:
    print(f'{p.id}: {p.user.email} - {p.stripe_checkout_session_id}')
"

# Check user credit balance
python manage.py shell -c "
from apps.users.models import User
from apps.credit_balance.models import CreditBalance
user = User.objects.get(email='<EMAIL>')
balance = CreditBalance.objects.get(user=user)
print(f'Balance: {balance.balance}')
"
```

## API Endpoints Summary

- `GET /api/v1/payments/webhook-health/` - Check webhook configuration
- `POST /api/v1/payments/create-checkout-session/` - Create payment session (with webhook validation)
- `GET /api/v1/payments/payment-success/` - Verify payment (with fallback sync)
- `POST /api/v1/payments/webhook/` - Stripe webhook handler
- `GET /api/v1/payments/purchase-history/` - Get purchase history

## Support

If you encounter issues:

1. Check the webhook health endpoint
2. Run the sync command with `--dry-run` first
3. Check Django logs for error details
4. Verify Stripe Dashboard shows the payment as successful
5. Contact support with the session ID and error details
