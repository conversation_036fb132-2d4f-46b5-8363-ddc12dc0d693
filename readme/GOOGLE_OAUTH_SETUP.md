# Google OAuth Integration Setup Guide

This guide will help you set up Google OAuth authentication for both the frontend and backend of MizuFlow.

## Prerequisites

1. A Google Cloud Console account
2. Backend dependencies installed (see requirements.txt)
3. Frontend dependencies already include `@react-oauth/google`

## Backend Setup

### 1. Install Dependencies

The following packages have been added to `requirements.txt`:
```
google-auth==2.35.0
google-auth-oauthlib==1.2.1
google-auth-httplib2==0.2.0
```

Install them:
```bash
pip install -r requirements.txt
```

### 2. Database Migration

Run the migration to add Google OAuth fields to the User model:
```bash
python manage.py migrate
```

### 3. Environment Variables

Add the following to your `.env` file:
```env
GOOGLE_OAUTH2_CLIENT_ID=your-google-client-id.apps.googleusercontent.com
GOOGLE_OAUTH2_CLIENT_SECRET=your-google-client-secret
GOOGLE_OAUTH2_REDIRECT_URI=http://localhost:8000/users/google-oauth/
```

## Frontend Setup

### 1. Environment Variables

Add to your `.env.local` file:
```env
NEXT_PUBLIC_GOOGLE_OAUTH_CLIENT_ID=your-google-client-id.apps.googleusercontent.com
```

## Google Cloud Console Setup

### 1. Create a New Project (or use existing)

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one

### 2. Enable Google+ API

1. Go to "APIs & Services" > "Library"
2. Search for "Google+ API" and enable it
3. Also enable "Google Identity" if available

### 3. Create OAuth 2.0 Credentials

1. Go to "APIs & Services" > "Credentials"
2. Click "Create Credentials" > "OAuth 2.0 Client IDs"
3. Configure the consent screen if prompted
4. Choose "Web application" as the application type
5. Add authorized origins:
   - `http://localhost:3000` (frontend dev)
   - `http://localhost:8000` (backend dev)
   - Your production domains
6. Add authorized redirect URIs:
   - `http://localhost:3000` (for frontend)
   - `http://localhost:8000/users/google-oauth/` (for backend)
   - Your production URLs

### 4. Get Your Credentials

1. Copy the Client ID and Client Secret
2. Add them to your environment variables

## Testing the Integration

### 1. Start the Backend

```bash
python manage.py runserver
```

### 2. Start the Frontend

```bash
npm run dev
```

### 3. Test Google Login

1. Go to `http://localhost:3000/login`
2. Click "Sign in with Google"
3. Complete the Google OAuth flow
4. You should be redirected to the dashboard

## API Endpoints

The following endpoint has been added:

- `POST /users/google-oauth/` - Authenticate with Google OAuth token

## Features Implemented

### Backend
- ✅ Google OAuth serializer for token validation
- ✅ Google OAuth view for authentication
- ✅ User model extended with Google fields
- ✅ Automatic user creation/linking
- ✅ JWT token generation for Google users

### Frontend
- ✅ Google OAuth provider setup
- ✅ Google login button component
- ✅ Integration with existing auth context
- ✅ Added to login and register forms

## User Flow

1. **New Google User**: Creates account automatically with Google info
2. **Existing User**: Links Google account to existing email-based account
3. **Returning Google User**: Logs in directly with Google

## Security Features

- Token validation with Google's servers
- Audience verification
- Automatic user activation for Google users
- JWT token generation for session management

## Troubleshooting

### Common Issues

1. **"Invalid token audience"**: Check that your Client ID matches in both frontend and backend
2. **CORS errors**: Ensure your domains are added to Google Console authorized origins
3. **"Google+ API not enabled"**: Enable the Google+ API in Google Cloud Console

### Debug Mode

Set `DEBUG=True` in your backend `.env` file to see detailed error messages.
