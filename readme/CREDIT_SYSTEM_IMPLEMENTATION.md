# Credit Point System Implementation

## Overview

This document outlines the comprehensive credit point system implemented across both frontend and backend of the MizuFlow application. The system manages user credits for AI-powered features across the platform, including the Tax & Accounting Assistant, invoice automation, and other AI services.

## Backend Implementation

### 1. User Registration Credit Grant

**Files Modified:**
- `apps/users/views.py`
- `apps/users/models.py`

**Changes:**
- **Regular Registration**: Automatically grants 500 credits when a new user registers via email
- **Google OAuth Registration**: Automatically grants 500 credits when a new user registers via Google OAuth
- **Error Handling**: Credit granting failures don't prevent user registration

### 2. Credit Consumption for Chat Requests

**Files Modified:**
- `apps/tax_acc_assistant/views.py`

**Changes:**
- **Pre-request Validation**: Checks user's credit balance before processing chat requests
- **Credit Deduction**: Deducts credits after successful chat processing
- **Environment Variable**: `CREDITS_PER_AI_CHAT_REQUEST` (default: 10 credits)
- **Error Responses**: Returns 402 Payment Required for insufficient credits

**Credit Cost Recommendation:**
- **10 credits per chat request** (based on gpt-4.1-mini model usage)
- Allows 50 chat requests with initial 500 credits
- Provides good user experience while encouraging credit purchases

### 3. API Endpoints

**Existing Endpoints (already implemented):**
- `GET /credit-balance/balance/me/` - Get current user's credit balance
- `GET /credit-balance/transactions/` - Get user's transaction history
- `POST /credit-balance/balance/{user_id}/add/` - Add credits (admin only)
- `POST /credit-balance/balance/{user_id}/deduct/` - Deduct credits (admin only)

## Frontend Implementation

### 1. User Menu Enhancement

**Files Modified:**
- `src/components/TopBar.jsx`

**Changes:**
- **Credit Balance Display**: Shows current credit balance in the top bar
- **Dropdown Menu**: User icon now opens a dropdown with Profile and Logout options
- **Real-time Updates**: Credit balance updates automatically

### 2. Profile Page Creation

**New Files Created:**
- `src/app/profile/page.js` - Profile page route
- `src/components/profile/ProfilePage.jsx` - Main profile component
- `src/components/profile/CreditBalance.jsx` - Credit management component
- `src/components/profile/TransactionHistory.jsx` - Transaction history component

**Features:**
- **User Information**: Displays basic user account details
- **Credit Balance**: Shows current balance with usage information
- **Transaction History**: Filterable list of all credit transactions
- **Credit Packages**: UI for future credit purchase integration
- **Usage Tips**: Helpful information about credit usage

### 3. Chat Interface Integration

**Files Modified:**
- `src/components/chat/ChatInterface.jsx`
- `src/services/chatService.js`

**Changes:**
- **Error Handling**: Gracefully handles insufficient credit errors
- **User Feedback**: Shows specific error messages for credit-related issues
- **Status Code Handling**: Properly handles 402 Payment Required responses

### 4. Routing Updates

**Files Modified:**
- `src/components/AppLayout.jsx`

**Changes:**
- **Profile Route**: Added `/profile` route mapping
- **Navigation**: Profile page uses dashboard as active sidebar item

## Environment Variables

### Credit System Environment Variables

```bash
# Credit System Configuration
CREDITS_PER_AI_CHAT_REQUEST=10
WELCOME_BONUS_CREDITS=500
```

**Description:**
- `CREDITS_PER_AI_CHAT_REQUEST`: Controls how many credits are deducted per chat request (default: 10 credits)
- `WELCOME_BONUS_CREDITS`: Controls how many credits new users receive as welcome bonus (default: 500 credits)
- Both values are configurable based on business requirements and cost considerations

## Credit System Flow

### 1. New User Registration
```
User Registers → User Created → 500 Credits Granted → Welcome Email Sent
```

### 2. Chat Request Processing
```
User Sends Message → Check Credit Balance →
  ├─ Sufficient Credits → Process Request → Deduct Credits → Return Response
  └─ Insufficient Credits → Return 402 Error → Show Error Message
```

### 3. Credit Management
```
User Views Profile →
  ├─ Current Balance Display
  ├─ Transaction History
  └─ Credit Purchase Options (Future)
```

## Database Schema

### Existing Tables (already implemented)

**CreditBalance Table:**
- `user` (OneToOne to User)
- `balance` (PositiveInteger, default=0)
- `created_at` (DateTime)
- `updated_at` (DateTime)

**CreditBalanceTransaction Table:**
- `user` (ForeignKey to User)
- `amount` (Integer, positive for credit, negative for debit)
- `balance_after` (PositiveInteger)
- `transaction_type` (CharField: CREDIT/DEBIT)
- `description` (TextField)
- `created_at` (DateTime)

## Testing Recommendations

### Backend Testing
1. **User Registration**: Verify 500 credits are granted to new users
2. **Chat Requests**: Test credit deduction and insufficient balance scenarios
3. **API Endpoints**: Verify credit balance and transaction history endpoints
4. **Error Handling**: Test various error scenarios

### Frontend Testing
1. **Credit Display**: Verify credit balance shows correctly in TopBar
2. **Profile Page**: Test all profile page components and functionality
3. **Chat Interface**: Test insufficient credit error handling
4. **Navigation**: Verify profile page routing works correctly

## Future Enhancements

### 1. Credit Purchase Integration
- Stripe payment integration for credit packages
- Automated credit addition after successful payment
- Receipt generation and email notifications

### 2. Credit Usage Analytics
- Usage patterns and statistics
- Credit consumption forecasting
- Personalized credit recommendations

### 3. Credit Rewards System
- Referral bonuses
- Loyalty rewards
- Promotional credit campaigns

## Security Considerations

### 1. Credit Balance Protection
- All credit operations are server-side validated
- User authentication required for all credit-related endpoints
- Admin-only access for manual credit adjustments

### 2. Transaction Integrity
- Atomic database transactions for credit operations
- Comprehensive logging of all credit changes
- Audit trail for all credit-related activities

## Deployment Notes

### 1. Environment Setup
- Add `CREDITS_PER_AI_CHAT_REQUEST=10` to production environment
- Verify all existing environment variables are properly configured
- Test credit system in staging environment before production deployment

### 2. Database Migration
- No new migrations required (credit system already exists)
- Verify existing credit_balance app is properly configured
- Test credit granting for new user registrations

### 3. Monitoring
- Monitor credit usage patterns
- Set up alerts for unusual credit consumption
- Track user registration and credit granting success rates

## Support and Maintenance

### 1. Common Issues
- **Credit not granted**: Check user registration logs and credit_balance service
- **Insufficient credits error**: Verify user's actual balance and transaction history
- **Frontend not updating**: Check API endpoints and authentication tokens

### 2. Maintenance Tasks
- Regular cleanup of old transaction records
- Monitor credit usage trends
- Update credit costs based on OpenAI pricing changes

This implementation provides a robust, scalable credit system that enhances user experience while providing clear usage tracking and management capabilities.
