#!/usr/bin/env bash
set -e

# Compose filenames
PROD_COMPOSE="docker-compose.yml"
DEV_COMPOSE="docker-compose.dev.yml"

usage() {
  cat <<EOF
Usage: $0 <command>

Commands:
  start                 Start production services (detached)
  stop                  Stop and remove production services
  restart               Restart production services
  logs                  Follow logs of all services

  build-prod            Rebuild production images and bring up prod
  build-no-cache-prod   Rebuild prod images (no cache) and bring up prod
  build-dev             Build & start dev services (with live-reload)
  build-no-cache-dev    Build dev images (no cache) only

  clean                 Teardown prod & prune ALL unused Docker objects
EOF
  exit 1
}

if [ $# -ne 1 ]; then usage; fi
CMD="$1"

case "$CMD" in
  start)
    echo "🚀 Starting production services…"
    docker compose -f "$PROD_COMPOSE" up -d
    ;;

  stop)
    echo "Stopping production services…"
    docker compose -f "$PROD_COMPOSE" down
    ;;

  restart)
    echo "Restarting production services…"
    docker compose -f "$PROD_COMPOSE" down
    docker compose -f "$PROD_COMPOSE" up -d
    ;;

  logs)
    echo "Attaching to logs… (CTRL+C to exit)"
    docker compose -f "$PROD_COMPOSE" logs -f
    ;;

  build-prod)
    echo "Building production images…"
    docker compose -f "$PROD_COMPOSE" down
    docker compose -f "$PROD_COMPOSE" build
    echo "Build complete; starting production services…"
    docker compose -f "$PROD_COMPOSE" up -d
    ;;

  build-no-cache-prod)
    echo "Building production images (no cache)…"
    docker compose -f "$PROD_COMPOSE" down
    docker compose -f "$PROD_COMPOSE" build --no-cache
    echo "No-cache build complete; starting production services…"
    docker compose -f "$PROD_COMPOSE" up -d
    ;;

  build-dev)
    if [ ! -f "$DEV_COMPOSE" ]; then
      echo "Dev compose override not found: $DEV_COMPOSE"
      exit 2
    fi
    echo "Building & starting dev services…"
    docker compose -f "$PROD_COMPOSE" down
    docker compose -f "$PROD_COMPOSE" -f "$DEV_COMPOSE" up --build -d
    ;;

  build-no-cache-dev)
    if [ ! -f "$DEV_COMPOSE" ]; then
      echo "Dev compose override not found: $DEV_COMPOSE"
      exit 2
    fi
    echo "Building dev images (no cache)…"
    docker compose -f "$PROD_COMPOSE" down
    docker compose -f "$PROD_COMPOSE" -f "$DEV_COMPOSE" build --no-cache
    echo "No-cache dev build complete."
    ;;

  clean)
    echo "Cleaning environment…"
    docker compose -f "$PROD_COMPOSE" down --volumes --remove-orphans
    docker system prune -af --volumes
    ;;

  *)
    echo "Unknown command: $CMD"
    usage
    ;;
esac
