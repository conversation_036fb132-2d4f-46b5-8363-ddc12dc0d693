# Dependencies
node_modules
.pnp
.pnp.js

# Testing
/coverage

# Next.js build output
.next
out

# Debug & IDE
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# Local env files
.env
.env.test

# Vercel
.vercel

# TypeScript
*.tsbuildinfo

# Git
.git
.gitignore

# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
.DS_Store

# Project specific
README.md
.cursor/

# Docker
Dockerfile
.dockerignore
docker-compose*
nginx/ 