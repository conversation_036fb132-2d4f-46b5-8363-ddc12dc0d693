/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    domains: ['placehold.co'],
  },
  webpack: (config, { isServer }) => {
    // Add custom webpack config for module resolution
    config.resolve.alias = {
      ...config.resolve.alias,
      '@': '/app/src'  // This maps to the src directory in the Docker container
    };
    return config;
  }
};

export default nextConfig;
