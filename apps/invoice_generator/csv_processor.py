import csv
import io
import difflib
from typing import Dict, List, Any
from django.core.files.uploadedfile import UploadedFile
from utils.logger import get_logger

logger = get_logger("invoice_generator.csv_processor")


class CSVProcessor:
    """Utility class for processing CSV files and mapping columns."""

    # Standard invoice field mappings with possible variations
    INVOICE_FIELD_MAPPINGS = {
        "invoice_number": [
            "invoice id",
            "invoice #",
            "invoice number",
            "inv #",
            "inv id",
            "invoice_id",
            "invoice_number",
            "invoiceid",
            "invoicenumber",
        ],
        "bill_date": [
            "bill date",
            "invoice date",
            "date",
            "issue date",
            "created date",
            "bill_date",
            "invoice_date",
            "issue_date",
            "created_date",
        ],
        "due_date": [
            "due date",
            "payment due",
            "due",
            "payment_due",
            "due_date",
            "payment due date",
            "payment_due_date",
        ],
        "client_company": [
            "client company",
            "company",
            "customer",
            "client",
            "customer name",
            "client_company",
            "customer_name",
            "company_name",
            "client_name",
        ],
        "contact_email": [
            "email",
            "contact email",
            "customer email",
            "client email",
            "contact_email",
            "customer_email",
            "client_email",
        ],
        "client_address": [
            "address",
            "client address",
            "customer address",
            "billing address",
            "client_address",
            "customer_address",
            "billing_address",
        ],
        "phone_number": [
            "phone",
            "phone number",
            "contact phone",
            "telephone",
            "phone_number",
            "contact_phone",
        ],
        "service_description": [
            "description",
            "service",
            "product",
            "item",
            "service description",
            "product description",
            "service_description",
            "product_description",
        ],
        "quantity": ["qty", "quantity", "amount", "units"],
        "unit_rate": [
            "rate",
            "unit rate",
            "price",
            "unit price",
            "cost",
            "unit_rate",
            "unit_price",
        ],
        "line_amount": [
            "total",
            "amount",
            "line total",
            "subtotal",
            "line amount",
            "line_amount",
            "line_total",
        ],
        "tax_percent": [
            "tax rate",
            "tax %",
            "tax percent",
            "tax",
            "vat rate",
            "tax_rate",
            "tax_percent",
            "vat_rate",
        ],
        "tax_value": [
            "tax amount",
            "tax value",
            "vat amount",
            "tax_amount",
            "tax_value",
            "vat_amount",
        ],
        "discount_amount": ["discount", "discount amount", "discount_amount"],
        "order_reference": [
            "reference",
            "order ref",
            "po number",
            "purchase order",
            "order_reference",
            "po_number",
            "purchase_order",
        ],
        "notes": ["notes", "comments", "remarks", "memo"],
    }

    @classmethod
    def detect_columns(cls, file: UploadedFile) -> List[str]:
        """
        Detect column names from the uploaded CSV file.

        Args:
            file: Uploaded CSV file

        Returns:
            List of column names
        """
        try:
            # Read the file content
            file.seek(0)  # Reset file pointer
            content = file.read()

            # Try to decode as UTF-8, fallback to latin-1
            try:
                content_str = content.decode("utf-8")
            except UnicodeDecodeError:
                content_str = content.decode("latin-1")

            # Create a StringIO object for CSV reading
            csv_file = io.StringIO(content_str)

            # Detect delimiter
            sample = csv_file.read(1024)
            csv_file.seek(0)
            sniffer = csv.Sniffer()
            delimiter = sniffer.sniff(sample).delimiter

            # Read the header row
            reader = csv.reader(csv_file, delimiter=delimiter)
            headers = next(reader, [])

            # Clean and normalize headers
            cleaned_headers = [header.strip() for header in headers if header.strip()]

            logger.info(f"Detected {len(cleaned_headers)} columns: {cleaned_headers}")
            return cleaned_headers

        except Exception as e:
            logger.error(f"Error detecting columns: {str(e)}")
            raise ValueError(f"Failed to process CSV file: {str(e)}")

    @classmethod
    def suggest_column_mappings(cls, csv_columns: List[str]) -> Dict[str, Any]:
        """
        Suggest column mappings using difflib for fuzzy matching.

        Args:
            csv_columns: List of column names from the CSV file

        Returns:
            Dictionary containing suggested mappings and confidence scores
        """
        suggested_mappings = {}
        confidence_scores = {}

        # Normalize CSV columns for better matching
        normalized_csv_columns = [col.lower().strip() for col in csv_columns]

        for invoice_field, possible_matches in cls.INVOICE_FIELD_MAPPINGS.items():
            best_match = None
            best_score = 0.0
            best_original_column = None

            # Check each possible match against all CSV columns
            for possible_match in possible_matches:
                matches = difflib.get_close_matches(
                    possible_match.lower(),
                    normalized_csv_columns,
                    n=1,
                    cutoff=0.6,  # Minimum similarity threshold
                )

                if matches:
                    # Calculate similarity score
                    similarity = difflib.SequenceMatcher(
                        None, possible_match.lower(), matches[0]
                    ).ratio()

                    if similarity > best_score:
                        best_score = similarity
                        best_match = matches[0]
                        # Find the original column name (with original casing)
                        best_original_column = csv_columns[
                            normalized_csv_columns.index(matches[0])
                        ]

            # Only suggest mappings with reasonable confidence
            if best_match and best_score >= 0.6:
                suggested_mappings[invoice_field] = best_original_column
                confidence_scores[invoice_field] = round(best_score, 2)

        logger.info(f"Generated {len(suggested_mappings)} suggested mappings")
        return {
            "suggested_mappings": suggested_mappings,
            "confidence_scores": confidence_scores,
        }

    @classmethod
    def parse_csv_data(
        cls, file: UploadedFile, column_mappings: Dict[str, str]
    ) -> List[Dict[str, Any]]:
        """
        Parse CSV data using the provided column mappings.

        Args:
            file: Uploaded CSV file
            column_mappings: Mapping of invoice fields to CSV columns

        Returns:
            List of dictionaries containing parsed invoice data
        """
        try:
            # Read the file content
            file.seek(0)  # Reset file pointer
            content = file.read()

            # Try to decode as UTF-8, fallback to latin-1
            try:
                content_str = content.decode("utf-8")
            except UnicodeDecodeError:
                content_str = content.decode("latin-1")

            # Create a StringIO object for CSV reading
            csv_file = io.StringIO(content_str)

            # Detect delimiter
            sample = csv_file.read(1024)
            csv_file.seek(0)
            sniffer = csv.Sniffer()
            delimiter = sniffer.sniff(sample).delimiter

            # Read all data
            reader = csv.DictReader(csv_file, delimiter=delimiter)
            parsed_data = []

            for row_num, row in enumerate(reader, start=1):
                invoice_data = {}

                # Map CSV columns to invoice fields
                for invoice_field, csv_column in column_mappings.items():
                    if csv_column in row:
                        invoice_data[invoice_field] = row[csv_column]
                    else:
                        invoice_data[invoice_field] = ""

                # Add row number for tracking
                invoice_data["_row_number"] = row_num
                parsed_data.append(invoice_data)

            logger.info(f"Parsed {len(parsed_data)} rows of data")
            return parsed_data

        except Exception as e:
            logger.error(f"Error parsing CSV data: {str(e)}")
            raise ValueError(f"Failed to parse CSV data: {str(e)}")

    @classmethod
    def validate_required_fields(cls, column_mappings: Dict[str, str]) -> List[str]:
        """
        Validate that required fields are mapped.

        Args:
            column_mappings: Mapping of invoice fields to CSV columns

        Returns:
            List of missing required fields
        """
        required_fields = ["invoice_number", "client_company", "line_amount"]
        missing_fields = []

        for field in required_fields:
            if field not in column_mappings or not column_mappings[field]:
                missing_fields.append(field)

        return missing_fields
