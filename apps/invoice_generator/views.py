from django.shortcuts import render
from django.http import JsonResponse, HttpResponse
from django.utils import timezone
from rest_framework import status, generics
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny, IsAuthenticated
from rest_framework.renderers import StaticHTMLRenderer
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.parsers import MultiPartParser, FormParser
import os
import uuid
import zipfile
import tempfile
import io
from django.conf import settings
from django.core.files.storage import default_storage
from django.core.files.base import ContentFile
import weasyprint
import pystache
import re
from datetime import datetime
from .models import CompanyTemplate, SalesWorkflow, SalesInvoiceData
from .serializers import (
    CompanyTemplateSerializer,
    CompanyTemplateListSerializer,
    SalesWorkflowSerializer,
    ColumnMappingRequestSerializer,
    ColumnMappingResponseSerializer,
    WorkflowStartRequestSerializer,
)
from .csv_processor import CSVProcessor
from utils.logger import get_logger

logger = get_logger("invoice_generator.views")

# Create your views here


@api_view(["POST"])
def extract_template_information(request):
    """
    Endpoint to extract information from an uploaded invoice PDF file.
    Returns dummy data for now.
    """
    if "file" not in request.FILES:
        return JsonResponse({"error": "No PDF file provided"}, status=400)

    pdf_file = request.FILES["file"]

    # Here we would process the PDF, but for now we return dummy data
    invoice_data = {
        "company_name": "Example Company Ltd",
        "address": "123 Business Street, City, Country",
        "contact_email": "<EMAIL>",
        "payment_terms": "Net 30 days",
        "bank_info": "Bank: Example Bank, Account: ********, Sort Code: 01-02-03",
        "company_logo": "base64_encoded_image_data_would_be_here",
        "Template Name": "",
    }

    return JsonResponse(invoice_data)


class InvoiceTemplateListView(APIView):
    """
    API endpoint to fetch all available invoice templates.
    Returns a list of templates with their metadata and raw HTML content.
    """

    permission_classes = [AllowAny]

    def get(self, request):
        templates_dir = os.path.join(settings.BASE_DIR, "invoice_templates")
        templates = []

        # Template metadata
        template_info = {
            "clean_business.html": {
                "name": "Clean Business",
                "description": "A clean and professional business invoice template",
                "preview_image": "https://placehold.co/300x400/059669/ffffff?text=Clean+Business",
            },
            "corporate.html": {
                "name": "Corporate",
                "description": "A formal corporate invoice template with traditional styling",
                "preview_image": "https://placehold.co/300x400/1f2937/ffffff?text=Corporate",
            },
            "minimalist.html": {
                "name": "Minimalist",
                "description": "A simple and elegant minimalist invoice template",
                "preview_image": "https://placehold.co/300x400/64748b/ffffff?text=Minimalist",
            },
            "elegant_classic.html": {
                "name": "Elegant Classic",
                "description": "An elegant classic invoice template with decorative elements",
                "preview_image": "https://placehold.co/300x400/d97706/ffffff?text=Elegant+Classic",
            },
            "contemporary.html": {
                "name": "Contemporary",
                "description": "A modern contemporary invoice template with vibrant colors",
                "preview_image": "https://placehold.co/300x400/7c3aed/ffffff?text=Contemporary",
            },
        }

        try:
            for filename in os.listdir(templates_dir):
                if filename.endswith(".html"):
                    file_path = os.path.join(templates_dir, filename)
                    with open(file_path, "r", encoding="utf-8") as file:
                        html_content = file.read()

                    template_data = {
                        "id": filename.replace(".html", ""),
                        "filename": filename,
                        "html_content": html_content,
                        **template_info.get(
                            filename,
                            {
                                "name": filename.replace(".html", "")
                                .replace("_", " ")
                                .title(),
                                "description": f"Invoice template: {filename}",
                                "preview_image": "/static/template-previews/default.png",
                            },
                        ),
                    }
                    templates.append(template_data)

            return Response(
                {"success": True, "templates": templates, "count": len(templates)}
            )

        except Exception as e:
            return Response({"success": False, "error": str(e)}, status=500)


class InvoiceTemplateDetailView(APIView):
    """
    API endpoint to fetch a specific invoice template by ID.
    Returns the raw HTML content using StaticHTMLRenderer.
    """

    renderer_classes = [StaticHTMLRenderer]
    permission_classes = [AllowAny]

    def get(self, request, template_id):
        templates_dir = os.path.join(settings.BASE_DIR, "invoice_templates")
        template_file = f"{template_id}.html"
        file_path = os.path.join(templates_dir, template_file)

        try:
            if not os.path.exists(file_path):
                return Response(
                    "<html><body><h1>Template not found</h1></body></html>", status=404
                )

            with open(file_path, "r", encoding="utf-8") as file:
                html_content = file.read()

            return Response(html_content)

        except Exception as e:
            return Response(
                f"<html><body><h1>Error loading template: {str(e)}</h1></body></html>",
                status=500,
            )


# Company Template Views


class CompanyTemplateListCreateView(generics.ListCreateAPIView):
    """
    API endpoint for listing and creating company templates.
    """

    permission_classes = [IsAuthenticated]

    def get_serializer_class(self):
        if self.request.method == "GET":
            return CompanyTemplateListSerializer
        return CompanyTemplateSerializer

    def get_queryset(self):
        return CompanyTemplate.objects.filter(user=self.request.user)

    def perform_create(self, serializer):
        serializer.save(user=self.request.user)


class CompanyTemplateDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    API endpoint for retrieving, updating, and deleting company templates.
    """

    serializer_class = CompanyTemplateSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return CompanyTemplate.objects.filter(user=self.request.user)

    def perform_update(self, serializer):
        # Update last_used timestamp when template is updated
        serializer.save(last_used=timezone.now())


class CompanyLogoUploadView(APIView):
    """
    API endpoint for uploading company logos.
    """

    permission_classes = [IsAuthenticated]
    parser_classes = [MultiPartParser, FormParser]

    def post(self, request):
        try:
            if "logo" not in request.FILES:
                return Response(
                    {"error": "No logo file provided"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            logo_file = request.FILES["logo"]

            # Validate file type
            allowed_types = [
                "image/jpeg",
                "image/jpg",
                "image/png",
                "image/gif",
                "image/webp",
            ]
            if logo_file.content_type not in allowed_types:
                return Response(
                    {"error": "Only image files (JPEG, PNG, GIF, WebP) are allowed"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Validate file size (max 5MB)
            if logo_file.size > 5 * 1024 * 1024:
                return Response(
                    {"error": "File size must be less than 5MB"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Generate unique filename
            file_extension = logo_file.name.split(".")[-1].lower()
            unique_filename = f"company_logos/{uuid.uuid4()}.{file_extension}"

            # Save file
            file_path = default_storage.save(
                unique_filename, ContentFile(logo_file.read())
            )
            logo_url = request.build_absolute_uri(settings.MEDIA_URL + file_path)

            return Response(
                {"success": True, "logo_url": logo_url, "file_path": file_path}
            )

        except Exception as e:
            return Response(
                {"error": f"Failed to upload logo: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


# Sales Workflow Views


class SalesWorkflowListCreateView(generics.ListCreateAPIView):
    """
    API endpoint for listing and creating sales workflows.
    """

    serializer_class = SalesWorkflowSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return SalesWorkflow.objects.filter(user=self.request.user)


class SalesWorkflowDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    API endpoint for retrieving, updating, and deleting sales workflows.
    """

    serializer_class = SalesWorkflowSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return SalesWorkflow.objects.filter(user=self.request.user)


class CSVUploadView(APIView):
    """
    API endpoint for uploading CSV files and detecting columns.
    """

    permission_classes = [IsAuthenticated]
    parser_classes = [MultiPartParser, FormParser]

    def post(self, request):
        try:
            if "file" not in request.FILES:
                return Response(
                    {"error": "No file provided"}, status=status.HTTP_400_BAD_REQUEST
                )

            file = request.FILES["file"]

            # Validate file type
            if not file.name.endswith((".csv", ".CSV")):
                return Response(
                    {"error": "Only CSV files are allowed"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Detect columns
            columns = CSVProcessor.detect_columns(file)

            # Generate suggested mappings
            mapping_result = CSVProcessor.suggest_column_mappings(columns)

            return Response(
                {
                    "success": True,
                    "detected_columns": columns,
                    "suggested_mappings": mapping_result["suggested_mappings"],
                    "confidence_scores": mapping_result["confidence_scores"],
                }
            )

        except Exception as e:
            logger.error(f"Error processing CSV upload: {str(e)}")
            return Response(
                {"error": f"Failed to process CSV file: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class ColumnMappingSuggestionsView(APIView):
    """
    API endpoint for getting column mapping suggestions.
    """

    permission_classes = [IsAuthenticated]

    def post(self, request):
        try:
            serializer = ColumnMappingRequestSerializer(data=request.data)
            if not serializer.is_valid():
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

            csv_columns = serializer.validated_data["csv_columns"]
            mapping_result = CSVProcessor.suggest_column_mappings(csv_columns)

            response_serializer = ColumnMappingResponseSerializer(mapping_result)
            return Response(response_serializer.data)

        except Exception as e:
            logger.error(f"Error generating column mapping suggestions: {str(e)}")
            return Response(
                {"error": f"Failed to generate suggestions: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class StartWorkflowView(APIView):
    """
    API endpoint for starting a new sales workflow.
    """

    permission_classes = [IsAuthenticated]

    def post(self, request):
        try:
            serializer = WorkflowStartRequestSerializer(
                data=request.data, context={"request": request}
            )
            if not serializer.is_valid():
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

            company_template_id = serializer.validated_data["company_template_id"]
            company_template = CompanyTemplate.objects.get(
                id=company_template_id, user=request.user
            )

            # Create new workflow
            workflow = SalesWorkflow.objects.create(
                user=request.user,
                company_template=company_template,
                status="file_upload",
            )

            # Update template last_used timestamp
            company_template.last_used = timezone.now()
            company_template.save()

            workflow_serializer = SalesWorkflowSerializer(workflow)
            return Response({"success": True, "workflow": workflow_serializer.data})

        except CompanyTemplate.DoesNotExist:
            return Response(
                {"error": "Company template not found"},
                status=status.HTTP_404_NOT_FOUND,
            )
        except Exception as e:
            logger.error(f"Error starting workflow: {str(e)}")
            return Response(
                {"error": f"Failed to start workflow: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


def get_user_email_folder(user):
    """
    Get the folder name for a user's invoices.
    Use sanitized email or 'anonymous' if user is not authenticated.
    """
    if user and user.is_authenticated and user.email:
        # Sanitize email to use as folder name (replace @ and . with _)
        return user.email.replace("@", "_at_").replace(".", "_dot_")
    return "anonymous"


def sanitize_filename(filename):
    """
    Sanitize filename to be safe for filesystem.
    """
    # Remove or replace invalid characters
    filename = re.sub(r'[<>:"/\\|?*]', "_", filename)
    # Remove extra spaces and replace with underscores
    filename = re.sub(r"\s+", "_", filename)
    # Remove leading/trailing dots and spaces
    filename = filename.strip(". ")
    # Ensure it's not empty
    if not filename:
        filename = "invoice"
    return filename


class GenerateInvoicesView(APIView):
    """
    API endpoint for generating PDF invoices from CSV data and template.
    """

    permission_classes = [IsAuthenticated]

    def post(self, request):
        try:
            # Get required data from request
            csv_data = request.data.get("csv_data", [])
            column_mappings = request.data.get("column_mappings", {})
            company_template_id = request.data.get("company_template_id")
            template_id = request.data.get("template_id")

            if not csv_data:
                return Response(
                    {"error": "No CSV data provided"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            if not column_mappings:
                return Response(
                    {"error": "No column mappings provided"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Get company template
            try:
                company_template = CompanyTemplate.objects.get(
                    id=company_template_id, user=request.user
                )
            except CompanyTemplate.DoesNotExist:
                return Response(
                    {"error": "Company template not found"},
                    status=status.HTTP_404_NOT_FOUND,
                )

            # Get invoice template HTML
            templates_dir = os.path.join(settings.BASE_DIR, "invoice_templates")
            template_file = f"{template_id}.html"
            template_path = os.path.join(templates_dir, template_file)

            if not os.path.exists(template_path):
                return Response(
                    {"error": "Invoice template not found"},
                    status=status.HTTP_404_NOT_FOUND,
                )

            with open(template_path, "r", encoding="utf-8") as file:
                template_html = file.read()

            # Process CSV data and generate invoices
            generated_invoices = []
            user_folder = get_user_email_folder(request.user)

            for row_data in csv_data:
                try:
                    # Map CSV data to invoice fields
                    invoice_data = {}
                    for field, csv_column in column_mappings.items():
                        invoice_data[field] = row_data.get(csv_column, "")

                    # Add company data to invoice
                    invoice_data.update(
                        {
                            "company_name": company_template.company_name,
                            "company_address_line_1": company_template.address_line_1,
                            "company_address_line_2": company_template.address_line_2,
                            "company_city": company_template.city,
                            "company_state_province": company_template.state_province,
                            "company_postal_code": company_template.postal_code,
                            "company_country": company_template.country,
                            "company_phone": company_template.phone,
                            "company_email": company_template.email,
                            "company_website": company_template.website,
                            "payment_terms": company_template.default_payment_terms,
                            "bank_name": company_template.bank_name,
                            "account_number": company_template.account_number,
                            "routing_number": company_template.routing_number,
                            "swift_code": company_template.swift_code,
                            "tax_id": company_template.tax_id,
                            "business_registration": company_template.business_registration,
                        }
                    )

                    # Add current date if not provided
                    if not invoice_data.get("bill_date"):
                        invoice_data["bill_date"] = datetime.now().strftime("%Y-%m-%d")

                    # Render template with data
                    rendered_html = pystache.render(template_html, invoice_data)

                    # Generate PDF
                    pdf_bytes = weasyprint.HTML(string=rendered_html).write_pdf()

                    if pdf_bytes is None:
                        logger.error(
                            "Failed to generate PDF - weasyprint returned None"
                        )
                        continue

                    # Create filename
                    client_name = sanitize_filename(
                        invoice_data.get("client_company", "Unknown_Client")
                    )
                    invoice_number = sanitize_filename(
                        invoice_data.get("invoice_number", "INV_001")
                    )
                    filename = f"{client_name}_{invoice_number}.pdf"

                    # Create folder structure: invoice_pdfs/{user_folder}/{client_name}/
                    client_folder = sanitize_filename(client_name)
                    file_path = f"invoice_pdfs/{user_folder}/{client_folder}/{filename}"

                    # Save PDF to storage
                    saved_path = default_storage.save(file_path, ContentFile(pdf_bytes))

                    generated_invoices.append(
                        {
                            "client_name": invoice_data.get(
                                "client_company", "Unknown Client"
                            ),
                            "invoice_number": invoice_data.get(
                                "invoice_number", "INV_001"
                            ),
                            "filename": filename,
                            "file_path": saved_path,
                            "download_url": f"/api/invoice-generator/download-invoice/{saved_path.replace('invoice_pdfs/', '')}",
                        }
                    )

                except Exception as e:
                    logger.error(f"Error generating invoice for row: {str(e)}")
                    continue

            return Response(
                {
                    "success": True,
                    "generated_invoices": generated_invoices,
                    "total_generated": len(generated_invoices),
                    "zip_download_url": f"/api/invoice-generator/download-invoices-zip/{user_folder}/",
                }
            )

        except Exception as e:
            logger.error(f"Error in GenerateInvoicesView: {str(e)}")
            return Response(
                {"error": f"Failed to generate invoices: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class DownloadInvoiceView(APIView):
    """
    API endpoint for downloading individual invoice PDFs.
    """

    permission_classes = [IsAuthenticated]

    def get(self, request, file_path):
        try:
            # Reconstruct full file path
            full_path = f"invoice_pdfs/{file_path}"

            # Check if file exists
            if not default_storage.exists(full_path):
                return Response(
                    {"error": "Invoice file not found"},
                    status=status.HTTP_404_NOT_FOUND,
                )

            # Get the file
            file_obj = default_storage.open(full_path, "rb")
            file_content = file_obj.read()
            file_obj.close()

            # Extract filename from path
            filename = os.path.basename(full_path)

            # Create response
            response = HttpResponse(file_content, content_type="application/pdf")
            response["Content-Disposition"] = f'attachment; filename="{filename}"'
            return response

        except Exception as e:
            logger.error(f"Error downloading invoice: {str(e)}")
            return Response(
                {"error": f"Failed to download invoice: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class DownloadInvoicesZipView(APIView):
    """
    API endpoint for downloading all invoices as a ZIP file.
    """

    permission_classes = [IsAuthenticated]

    def get(self, request, user_folder):
        try:
            # Verify user folder matches current user
            current_user_folder = get_user_email_folder(request.user)
            if user_folder != current_user_folder:
                return Response(
                    {"error": "Access denied"},
                    status=status.HTTP_403_FORBIDDEN,
                )

            # Create temporary file for ZIP
            with tempfile.NamedTemporaryFile(delete=False, suffix=".zip") as temp_zip:
                with zipfile.ZipFile(temp_zip, "w", zipfile.ZIP_DEFLATED) as zip_file:
                    # Get all invoice files for this user
                    user_invoice_dir = f"invoice_pdfs/{user_folder}/"

                    # List all files in user's invoice directory
                    try:
                        dirs, files = default_storage.listdir(user_invoice_dir)

                        # Add all PDF files from all client folders
                        for client_dir in dirs:
                            client_path = f"{user_invoice_dir}{client_dir}/"
                            try:
                                _, client_files = default_storage.listdir(client_path)
                                for file_name in client_files:
                                    if file_name.endswith(".pdf"):
                                        file_path = f"{client_path}{file_name}"
                                        if default_storage.exists(file_path):
                                            file_obj = default_storage.open(
                                                file_path, "rb"
                                            )
                                            file_content = file_obj.read()
                                            file_obj.close()

                                            # Add to ZIP with client folder structure
                                            zip_file.writestr(
                                                f"{client_dir}/{file_name}",
                                                file_content,
                                            )
                            except Exception as e:
                                logger.warning(
                                    f"Error processing client folder {client_dir}: {str(e)}"
                                )
                                continue

                    except Exception as e:
                        logger.error(f"Error listing invoice directory: {str(e)}")
                        return Response(
                            {"error": "No invoices found"},
                            status=status.HTTP_404_NOT_FOUND,
                        )

                temp_zip_path = temp_zip.name

            # Read the ZIP file
            with open(temp_zip_path, "rb") as zip_file:
                zip_content = zip_file.read()

            # Clean up temporary file
            os.unlink(temp_zip_path)

            # Create response
            response = HttpResponse(zip_content, content_type="application/zip")
            response["Content-Disposition"] = (
                f'attachment; filename="invoices_{user_folder}.zip"'
            )
            return response

        except Exception as e:
            logger.error(f"Error creating ZIP file: {str(e)}")
            return Response(
                {"error": f"Failed to create ZIP file: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
