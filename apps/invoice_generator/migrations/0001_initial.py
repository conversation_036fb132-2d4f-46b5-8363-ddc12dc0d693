# Generated by Django 5.2 on 2025-05-30 16:19

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='CompanyTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('template_name', models.Char<PERSON>ield(help_text='User-defined name for this template', max_length=100)),
                ('template_id', models.CharField(help_text='ID of the selected MizuFlow template', max_length=50)),
                ('template_display_name', models.CharField(help_text='Display name of the MizuFlow template', max_length=100)),
                ('company_name', models.<PERSON>r<PERSON><PERSON>(max_length=200)),
                ('address_line_1', models.Char<PERSON><PERSON>(max_length=200)),
                ('address_line_2', models.Char<PERSON>ield(blank=True, max_length=200)),
                ('city', models.Char<PERSON>ield(max_length=100)),
                ('state_province', models.CharField(max_length=100)),
                ('postal_code', models.CharField(max_length=20)),
                ('country', models.CharField(max_length=100)),
                ('phone', models.CharField(blank=True, max_length=20)),
                ('email', models.EmailField(max_length=254, validators=[django.core.validators.EmailValidator()])),
                ('website', models.URLField(blank=True)),
                ('default_payment_terms', models.CharField(default='Net 30 days', max_length=100)),
                ('bank_name', models.CharField(blank=True, max_length=100)),
                ('account_number', models.CharField(blank=True, max_length=50)),
                ('routing_number', models.CharField(blank=True, max_length=20)),
                ('swift_code', models.CharField(blank=True, max_length=20)),
                ('tax_id', models.CharField(blank=True, max_length=50)),
                ('business_registration', models.CharField(blank=True, max_length=100)),
                ('logo_url', models.URLField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('last_used', models.DateTimeField(blank=True, null=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='company_templates', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-last_used', '-updated_at'],
                'unique_together': {('user', 'template_name')},
            },
        ),
        migrations.CreateModel(
            name='SalesWorkflow',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(choices=[('file_upload', 'File Upload'), ('column_mapping', 'Column Mapping'), ('data_processing', 'Data Processing'), ('invoice_generation', 'Invoice Generation'), ('completed', 'Completed'), ('failed', 'Failed')], default='file_upload', max_length=20)),
                ('uploaded_file_name', models.CharField(blank=True, max_length=255)),
                ('uploaded_file_path', models.CharField(blank=True, max_length=500)),
                ('detected_columns', models.JSONField(default=list, help_text='List of columns detected in the CSV file')),
                ('column_mappings', models.JSONField(default=dict, help_text='Mapping of invoice fields to CSV columns')),
                ('suggested_mappings', models.JSONField(default=dict, help_text='AI-suggested column mappings')),
                ('total_rows', models.IntegerField(default=0)),
                ('processed_rows', models.IntegerField(default=0)),
                ('failed_rows', models.IntegerField(default=0)),
                ('error_message', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('company_template', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='workflows', to='invoice_generator.companytemplate')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sales_workflows', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='SalesInvoiceData',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('row_number', models.IntegerField(help_text='Row number in the original CSV file')),
                ('invoice_data', models.JSONField(help_text='Processed invoice data mapped from CSV')),
                ('is_processed', models.BooleanField(default=False)),
                ('processing_error', models.TextField(blank=True)),
                ('generated_invoice_path', models.CharField(blank=True, max_length=500)),
                ('invoice_number', models.CharField(blank=True, max_length=50)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('workflow', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='invoice_data', to='invoice_generator.salesworkflow')),
            ],
            options={
                'ordering': ['row_number'],
                'unique_together': {('workflow', 'row_number')},
            },
        ),
    ]
