import os
from django.urls import reverse
from django.conf import settings
from django.core.mail import send_mail
from rest_framework.views import APIView
from rest_framework.request import Request
from rest_framework import generics, status
from rest_framework.response import Response
from django.contrib.auth import get_user_model
from django.shortcuts import get_object_or_404
from rest_framework.exceptions import ValidationError
from django.utils.encoding import force_bytes, force_str
from rest_framework.permissions import AllowAny, IsAuthenticated
from rest_framework_simplejwt.tokens import RefreshToken
from django.utils.http import urlsafe_base64_encode, urlsafe_base64_decode

from .serializers import (
    UserSerializer,
    RegisterSerializer,
    SetPasswordSerializer,
    GoogleOAuthSerializer,
)
from apps.credit_balance.services import add_credit

User = get_user_model()


class RegisterView(generics.CreateAPIView):
    """View for registering new users with only email."""

    queryset = User.objects.all()
    permission_classes = (AllowAny,)
    serializer_class = RegisterSerializer

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        user = serializer.save()

        # Generate activation token
        uid = urlsafe_base64_encode(force_bytes(user.pk))

        # Create activation link using FRONTEND_URL from settings
        activation_link = f"{settings.FRONTEND_URL}/activate/{uid}"

        # Grant initial credit points to new user (configurable via environment variable)
        welcome_bonus = int(os.getenv("WELCOME_BONUS_CREDITS", "0"))
        try:
            add_credit(
                user=user,
                amount=welcome_bonus,
                description=f"Welcome bonus - {welcome_bonus} credits for new user registration",
            )
        except Exception as e:
            # Log the error but don't fail registration
            print(f"Failed to grant initial credits to user {user.email}: {str(e)}")

        # Send email with activation link
        from django.core.mail import EmailMessage

        email = EmailMessage(
            subject="Activate Your Account",
            body=f"Please click the following link to activate your account: {activation_link}",
            from_email=settings.DEFAULT_FROM_EMAIL,
            to=[user.email],
            reply_to=[settings.NO_REPLY_EMAIL],
        )
        email.send(fail_silently=False)

        return Response(
            {
                "detail": "Registration successful. Please check your email for activation instructions."
            },
            status=status.HTTP_201_CREATED,
        )


class ActivateUserView(APIView):
    """View for activating a user account."""

    permission_classes = (AllowAny,)

    def get(self, request, uid):
        try:
            # Decode the user ID
            user_id = force_str(urlsafe_base64_decode(uid))
            user = get_object_or_404(User, id=user_id)

            # Activate user
            user.is_active = True
            user.save()

            return Response(
                {
                    "detail": "Account activated successfully. You can now set your password.",
                    "email": user.email,  # Return email to frontend for convenience
                },
                status=status.HTTP_200_OK,
            )
        except Exception:
            return Response(
                {"detail": "Activation link is invalid."},
                status=status.HTTP_400_BAD_REQUEST,
            )


class SetPasswordView(APIView):
    """View for setting password after activation."""

    permission_classes = (AllowAny,)

    def post(self, request):
        email = request.data.get("email")
        if not email:
            return Response(
                {"email": "Email is required."}, status=status.HTTP_400_BAD_REQUEST
            )

        user = get_object_or_404(User, email=email)

        serializer = SetPasswordSerializer(user, data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(
                {"detail": "Password set successfully."}, status=status.HTTP_200_OK
            )
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class UserDetailView(generics.RetrieveUpdateAPIView):
    """View for retrieving and updating user details."""

    serializer_class = UserSerializer
    permission_classes = (IsAuthenticated,)

    def get_object(self):
        """Retrieve and return authenticated user."""
        return self.request.user


class UserListView(generics.ListAPIView):
    """View to list all users."""

    queryset = User.objects.all()
    serializer_class = UserSerializer
    permission_classes = (IsAuthenticated,)


class MeView(APIView):
    """View to retrieve logged in user's information."""

    permission_classes = (IsAuthenticated,)

    def get(self, request):
        serializer = UserSerializer(request.user)
        user_data = serializer.data

        # Add credit cost information from environment variable
        credits_per_request = int(os.getenv("CREDITS_PER_TAX_ACC_CHAT_REQUEST", "10"))
        user_data["credits_per_chat_request"] = credits_per_request

        return Response(user_data)


class GoogleOAuthView(APIView):
    """View for Google OAuth authentication."""

    permission_classes = (AllowAny,)

    def post(self, request):
        """Authenticate user with Google OAuth token."""
        serializer = GoogleOAuthSerializer(data=request.data)
        if serializer.is_valid():
            user = serializer.save()

            # Generate JWT tokens for the user
            refresh = RefreshToken.for_user(user)

            # Get user data and add credit cost information
            user_data = UserSerializer(user).data
            credits_per_request = int(
                os.getenv("CREDITS_PER_TAX_ACC_CHAT_REQUEST", "10")
            )
            user_data["credits_per_chat_request"] = credits_per_request

            return Response(
                {
                    "access": str(refresh.access_token),
                    "refresh": str(refresh),
                    "user": user_data,
                },
                status=status.HTTP_200_OK,
            )

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
