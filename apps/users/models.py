from django.db import models
from django.utils.translation import gettext_lazy as _
from django.contrib.auth.models import AbstractUser, BaseUserManager


class UserManager(BaseUserManager):
    """Define a model manager for User model with no username field."""

    def _create_user(self, email, password=None, **extra_fields):
        """Create and save a User with the given email and password."""
        if not email:
            raise ValueError("The Email field must be set")
        email = self.normalize_email(email)
        user = self.model(email=email, **extra_fields)
        user.set_password(password)
        user.save(using=self._db)
        return user

    def create_user(self, email, password=None, **extra_fields):
        extra_fields.setdefault("is_staff", False)
        extra_fields.setdefault("is_superuser", False)
        extra_fields.setdefault("is_active", False)
        return self._create_user(email, password, **extra_fields)

    def create_superuser(self, email, password=None, **extra_fields):
        """Create and save a SuperUser with the given email and password."""
        extra_fields.setdefault("is_staff", True)
        extra_fields.setdefault("is_superuser", True)
        extra_fields.setdefault("is_active", True)

        if extra_fields.get("is_staff") is not True:
            raise ValueError("Superuser must have is_staff=True.")
        if extra_fields.get("is_superuser") is not True:
            raise ValueError("Superuser must have is_superuser=True.")

        return self._create_user(email, password, **extra_fields)

    def create_google_user(
        self, email, google_id, first_name, last_name, profile_picture=None
    ):
        """Create and save a User from Google OAuth."""
        if not email:
            raise ValueError("The Email field must be set")
        if not google_id:
            raise ValueError("The Google ID field must be set")

        email = self.normalize_email(email)
        user = self.model(
            email=email,
            google_id=google_id,
            first_name=first_name,
            last_name=last_name,
            profile_picture=profile_picture,
            is_google_user=True,
            is_active=True,  # Google users are automatically active
        )
        user.save(using=self._db)

        # Grant initial credit points to new Google user (configurable via environment variable)
        try:
            import os
            from apps.credit_balance.services import add_credit

            welcome_bonus = int(os.getenv("WELCOME_BONUS_CREDITS", "0"))
            add_credit(
                user=user,
                amount=welcome_bonus,
                description=f"Welcome bonus - {welcome_bonus} credits for new Google user registration",
            )
        except Exception as e:
            # Log the error but don't fail user creation
            print(
                f"Failed to grant initial credits to Google user {user.email}: {str(e)}"
            )

        return user


class User(AbstractUser):
    """Custom User model."""

    username = None
    email = models.EmailField(_("email address"), unique=True)
    first_name = models.CharField(_("first name"), max_length=30, blank=False)
    last_name = models.CharField(_("last name"), max_length=150, blank=False)
    date_joined = models.DateTimeField(_("date joined"), auto_now_add=True)
    is_active = models.BooleanField(_("active"), default=False)

    # Google OAuth fields
    google_id = models.CharField(max_length=100, blank=True, null=True, unique=True)
    profile_picture = models.URLField(blank=True, null=True)
    is_google_user = models.BooleanField(default=False)

    USERNAME_FIELD = "email"
    REQUIRED_FIELDS = ["first_name", "last_name"]

    objects: UserManager = UserManager()

    class Meta:
        ordering = ["id"]  # Add default ordering by primary key

    def __str__(self):
        return self.email
