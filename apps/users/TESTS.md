# MizuFlow API Test Documentation

This document outlines the test strategy and test cases implemented for the MizuFlow backend API.

## Test Structure

The tests are organized into the following categories:

1. **User Model Tests** - Tests for the custom User model functionality
2. **API Endpoint Tests** - Tests for all REST API endpoints

## Running the Tests

You can run the tests using the provided script:

```bash
./run_tests.sh
```

Or directly with Django:

```bash
python manage.py test apps/users
```

## Standardized Test Variables

To maintain consistency across tests, the following standardized test variables are defined in the test file:

```python
# Load from environment variables with fallbacks to defaults
# Regular test user variables
TEST_EMAIL = os.getenv("TEST_EMAIL", "<EMAIL>")
TEST_PASSWORD = os.getenv("TEST_PASSWORD", "testpass123")
TEST_FIRST_NAME = os.getenv("TEST_FIRST_NAME", "Test")
TEST_LAST_NAME = os.getenv("TEST_LAST_NAME", "User")

# Admin user variables
ADMIN_EMAIL = os.getenv("ADMIN_EMAIL", "<EMAIL>")
ADMIN_PASSWORD = os.getenv("ADMIN_PASSWORD", "testpass123")

# Existing user variables for API tests
EXISTING_EMAIL = os.getenv("EXISTING_EMAIL", "<EMAIL>")
EXISTING_FIRST_NAME = os.getenv("EXISTING_FIRST_NAME", "Existing")
EXISTING_LAST_NAME = os.getenv("EXISTING_LAST_NAME", "User")
EXISTING_PASSWORD = os.getenv("EXISTING_PASSWORD", "existing123")

# New user variables for registration tests
NEW_USER_EMAIL = os.getenv("NEW_USER_EMAIL", "<EMAIL>")
NEW_USER_FIRST_NAME = os.getenv("NEW_USER_FIRST_NAME", "Test")
NEW_USER_LAST_NAME = os.getenv("NEW_USER_LAST_NAME", "UserA")
NEW_USER_PASSWORD = os.getenv("NEW_USER_PASSWORD", "changeme@123")
```

These variables are used consistently throughout the test suite to ensure clarity and maintainability.

## Test Coverage

### User Model Tests

These tests verify the functionality of the custom User model:

- **test_create_user**: Verifies that a user can be created with an email address as the username field
- **test_create_user_empty_email**: Verifies that creating a user without an email raises an error
- **test_create_superuser**: Verifies that superuser creation works and sets proper permission flags
- **test_email_normalized**: Verifies that email addresses are normalized to lowercase

### API Endpoint Tests

#### Authentication Endpoints

- **test_user_registration**: Tests user registration functionality
- **test_user_registration_with_invalid_data**: Tests registration validation for:
  - Missing required fields
  - Password mismatch
  - Email uniqueness

- **test_user_token_obtain**: Tests JWT token acquisition with valid credentials
- **test_user_token_obtain_invalid_credentials**: Tests token acquisition with invalid credentials
- **test_token_refresh**: Tests JWT token refresh functionality
- **test_token_verify**: Tests JWT token verification

#### User Management Endpoints

- **test_me_endpoint**: Tests retrieval of authenticated user's profile
- **test_user_list_endpoint**: Tests listing all users (restricted to authenticated users)
- **test_user_detail_endpoint**: Tests user profile update
- **test_user_password_update**: Tests password change functionality
- **test_user_authentication_required**: Tests that authentication is required for protected endpoints

## Security Testing

The test suite includes security-related tests:

- Verifies that authentication is required for protected endpoints
- Ensures password hashing is working correctly (not stored in plaintext)
- Tests that token refresh works as expected

## Additional Test Considerations

For a production environment, consider adding the following tests:

1. **Rate limiting tests**: Verify that API rate limiting is working
2. **Permission tests**: Verify that proper permissions are enforced for different user roles
3. **Integration tests**: Test interactions between multiple components
4. **Performance tests**: Test API under load conditions
5. **Data validation tests**: More comprehensive tests for input validation and edge cases

## Known Linter Issues

The test file shows linter errors due to:

1. The custom User model has username=None but the linter expects a username parameter
2. DRF's APIClient returns Response objects with a .data attribute, but the linter doesn't recognize this
3. Some import-related linter issues that don't affect functionality

These errors can be safely ignored as the tests will work correctly at runtime. The comment at the top of the tests.py file also explains these expected linter errors:

```python
# NOTE: This file may show linter errors for two main reasons:
# 1. The custom User model has username=None but the linter expects a username parameter
# 2. DRF's APIClient returns Response objects with a .data attribute, but the linter doesn't recognize this
# These are expected and can be safely ignored as the tests will work correctly at runtime.
``` 