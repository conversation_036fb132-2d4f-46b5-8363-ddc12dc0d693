from django.urls import path, include
from rest_framework_simplejwt.views import (
    TokenVerifyView,
    TokenRefreshView,
    TokenObtainPairView,
)
from .views import (
    MeView,
    RegisterView,
    UserListView,
    UserDetailView,
    SetPasswordView,
    ActivateUserView,
    GoogleOAuthView,
)

urlpatterns = [
    # JWT Authentication
    path("token/", TokenObtainPairView.as_view(), name="token_obtain_pair"),
    path("token/refresh/", TokenRefreshView.as_view(), name="token_refresh"),
    path("token/verify/", TokenVerifyView.as_view(), name="token_verify"),
    # User registration and management
    path("register/", RegisterView.as_view(), name="register"),
    path("activate/<str:uid>/", ActivateUserView.as_view(), name="activate"),
    path("set-password/", SetPasswordView.as_view(), name="set-password"),
    # Google OAuth
    path("google-oauth/", GoogleOAuthView.as_view(), name="google-oauth"),
    path("", UserListView.as_view(), name="user-list"),
    path("me/", MeView.as_view(), name="me"),
    path("profile/", UserDetailView.as_view(), name="user-detail"),
]
