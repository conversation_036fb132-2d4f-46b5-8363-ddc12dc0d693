import os
from dotenv import load_dotenv
from django.urls import reverse
from django.test import TestCase
from rest_framework import status
from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
from rest_framework.test import APIClient, APITestCase

# Load environment variables from .env file
load_dotenv()

# NOTE: This file may show linter errors for two main reasons:
# 1. The custom User model has username=None but the linter expects a username parameter
# 2. DRF's APIClient returns Response objects with a .data attribute, but the linter doesn't recognize this
# These are expected and can be safely ignored as the tests will work correctly at runtime.

User = get_user_model()

# Reusable test user variables - load from environment or use defaults
TEST_EMAIL = os.getenv("TEST_EMAIL", "<EMAIL>")
TEST_PASSWORD = os.getenv("TEST_PASSWORD", "testpass123")
TEST_FIRST_NAME = os.getenv("TEST_FIRST_NAME", "Test")
TEST_LAST_NAME = os.getenv("TEST_LAST_NAME", "User")

# Admin user variables
ADMIN_EMAIL = os.getenv("ADMIN_EMAIL", "<EMAIL>")
ADMIN_PASSWORD = os.getenv("ADMIN_PASSWORD", "testpass123")

# Existing user variables for API tests
EXISTING_EMAIL = os.getenv("EXISTING_EMAIL", "<EMAIL>")
EXISTING_FIRST_NAME = os.getenv("EXISTING_FIRST_NAME", "Existing")
EXISTING_LAST_NAME = os.getenv("EXISTING_LAST_NAME", "User")
EXISTING_PASSWORD = os.getenv("EXISTING_PASSWORD", "existing123")

# New user variables for registration tests
NEW_USER_EMAIL = os.getenv("NEW_USER_EMAIL", "<EMAIL>")
NEW_USER_FIRST_NAME = os.getenv("NEW_USER_FIRST_NAME", "Test")
NEW_USER_LAST_NAME = os.getenv("NEW_USER_LAST_NAME", "UserA")
NEW_USER_PASSWORD = os.getenv("NEW_USER_PASSWORD", "changeme@123")


class UserModelTests(TestCase):
    """Tests for the custom User model."""

    def test_create_user(self):
        """Test creating a user with email is successful."""
        user = User.objects.create_user(
            email=TEST_EMAIL,
            password=TEST_PASSWORD,
            first_name=TEST_FIRST_NAME,
            last_name=TEST_LAST_NAME,
        )

        self.assertEqual(user.email, TEST_EMAIL)
        self.assertTrue(user.check_password(TEST_PASSWORD))
        self.assertFalse(user.is_staff)
        self.assertFalse(user.is_superuser)

    def test_create_user_empty_email(self):
        """Test that creating a user without an email raises error."""
        with self.assertRaises(ValueError):
            User.objects.create_user(email="", password=TEST_PASSWORD)

    def test_create_superuser(self):
        """Test creating a superuser."""
        user = User.objects.create_superuser(email=ADMIN_EMAIL, password=ADMIN_PASSWORD)

        self.assertEqual(user.email, ADMIN_EMAIL)
        self.assertTrue(user.check_password(ADMIN_PASSWORD))
        self.assertTrue(user.is_staff)
        self.assertTrue(user.is_superuser)

    def test_email_normalized(self):
        """Test email is normalized when creating a user."""
        email = "<EMAIL>"
        user = User.objects.create_user(
            email=email,
            password=TEST_PASSWORD,
            first_name=TEST_FIRST_NAME,
            last_name=TEST_LAST_NAME,
        )
        self.assertEqual(user.email, email.lower())


class UserAPITests(APITestCase):
    """Tests for the user API endpoints."""

    def setUp(self):
        self.client = APIClient()
        self.register_url = reverse("register")
        self.token_url = reverse("token_obtain_pair")
        self.token_refresh_url = reverse("token_refresh")
        self.token_verify_url = reverse("token_verify")
        self.me_url = reverse("me")
        self.user_list_url = reverse("user-list")
        self.user_detail_url = reverse("user-detail")

        self.user_data = {
            "email": NEW_USER_EMAIL,
            "first_name": NEW_USER_FIRST_NAME,
            "last_name": NEW_USER_LAST_NAME,
            "password": NEW_USER_PASSWORD,
            "password2": NEW_USER_PASSWORD,
        }

        self.test_user = User.objects.create_user(
            email=EXISTING_EMAIL,
            first_name=EXISTING_FIRST_NAME,
            last_name=EXISTING_LAST_NAME,
            password=EXISTING_PASSWORD,
        )

    def test_user_registration(self):
        """Test user registration endpoint."""
        response = self.client.post(self.register_url, self.user_data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(User.objects.count(), 2)  # Test user + new user

    def test_user_registration_with_invalid_data(self):
        """Test user registration with invalid data."""
        # Missing required field
        invalid_data = self.user_data.copy()
        invalid_data.pop("email")
        response = self.client.post(self.register_url, invalid_data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        # Passwords don't match
        invalid_data = self.user_data.copy()
        invalid_data["password2"] = "different123"
        response = self.client.post(self.register_url, invalid_data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        # Email already exists
        invalid_data = self.user_data.copy()
        invalid_data["email"] = EXISTING_EMAIL
        response = self.client.post(self.register_url, invalid_data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_user_token_obtain(self):
        """Test token obtain endpoint."""
        response = self.client.post(
            self.token_url, {"email": EXISTING_EMAIL, "password": EXISTING_PASSWORD}
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn("access", response.data)
        self.assertIn("refresh", response.data)

    def test_user_token_obtain_invalid_credentials(self):
        """Test token obtain with invalid credentials."""
        response = self.client.post(
            self.token_url,
            {"email": EXISTING_EMAIL, "password": "wrongpassword"},
        )
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_token_refresh(self):
        """Test token refresh endpoint."""
        # First get a token
        token_response = self.client.post(
            self.token_url, {"email": EXISTING_EMAIL, "password": EXISTING_PASSWORD}
        )
        refresh_token = token_response.data["refresh"]

        # Refresh the token
        response = self.client.post(self.token_refresh_url, {"refresh": refresh_token})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn("access", response.data)

    def test_token_verify(self):
        """Test token verify endpoint."""
        # First get a token
        token_response = self.client.post(
            self.token_url, {"email": EXISTING_EMAIL, "password": EXISTING_PASSWORD}
        )
        access_token = token_response.data["access"]

        # Verify the token
        response = self.client.post(self.token_verify_url, {"token": access_token})
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_me_endpoint(self):
        """Test me endpoint returns user data."""
        # Authenticate
        token_response = self.client.post(
            self.token_url, {"email": EXISTING_EMAIL, "password": EXISTING_PASSWORD}
        )
        access_token = token_response.data["access"]

        # Access me endpoint
        self.client.credentials(HTTP_AUTHORIZATION=f"Bearer {access_token}")
        response = self.client.get(self.me_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["email"], EXISTING_EMAIL)
        self.assertEqual(response.data["first_name"], EXISTING_FIRST_NAME)
        self.assertEqual(response.data["last_name"], EXISTING_LAST_NAME)

    def test_user_list_endpoint(self):
        """Test user list endpoint."""
        # Authenticate
        token_response = self.client.post(
            self.token_url, {"email": EXISTING_EMAIL, "password": EXISTING_PASSWORD}
        )
        access_token = token_response.data["access"]

        # Access user list endpoint
        self.client.credentials(HTTP_AUTHORIZATION=f"Bearer {access_token}")
        response = self.client.get(self.user_list_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        # Check that users exist in the response, not the exact count which may vary
        self.assertGreater(
            len(response.data), 0
        )  # At least one user exists in the system

    def test_user_detail_endpoint(self):
        """Test user detail endpoint for profile update."""
        # Authenticate
        token_response = self.client.post(
            self.token_url, {"email": EXISTING_EMAIL, "password": EXISTING_PASSWORD}
        )
        access_token = token_response.data["access"]
        self.client.credentials(HTTP_AUTHORIZATION=f"Bearer {access_token}")

        # Update user details
        update_data = {"first_name": "Updated", "last_name": "Name"}
        response = self.client.patch(self.user_detail_url, update_data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Verify the update
        self.test_user.refresh_from_db()
        self.assertEqual(self.test_user.first_name, "Updated")
        self.assertEqual(self.test_user.last_name, "Name")

    def test_user_password_update(self):
        """Test updating user password."""
        # Authenticate
        token_response = self.client.post(
            self.token_url, {"email": EXISTING_EMAIL, "password": EXISTING_PASSWORD}
        )
        access_token = token_response.data["access"]
        self.client.credentials(HTTP_AUTHORIZATION=f"Bearer {access_token}")

        # Update password
        new_password = "newpassword123"
        update_data = {"password": new_password}
        response = self.client.patch(self.user_detail_url, update_data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Verify the password was updated by getting a new token
        response = self.client.post(
            self.token_url,
            {"email": EXISTING_EMAIL, "password": new_password},
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn("access", response.data)

    def test_user_authentication_required(self):
        """Test authentication is required for protected endpoints."""
        # Try without authentication
        response = self.client.get(self.me_url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

        response = self.client.get(self.user_list_url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

        response = self.client.get(self.user_detail_url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
