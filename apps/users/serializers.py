from rest_framework import serializers
from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
from rest_framework.validators import UniqueValidator
from django.contrib.auth.password_validation import validate_password

User = get_user_model()


class UserSerializer(serializers.ModelSerializer):
    """Serializer for the user object."""

    class Meta:
        model = User
        fields = (
            "id",
            "email",
            "first_name",
            "last_name",
            "password",
            "date_joined",
            "is_google_user",
            "is_active",
        )
        extra_kwargs = {
            "password": {"write_only": True, "min_length": 8, "required": False},
            "email": {"required": False},
            "date_joined": {"read_only": True},
            "is_google_user": {"read_only": True},
            "is_active": {"read_only": True},
        }

    def create(self, validated_data):
        """Create and return a user with encrypted password."""
        return User.objects.create_user(**validated_data)

    def update(self, instance, validated_data):
        """Update and return user."""
        password = validated_data.pop("password", None)
        user = super().update(instance, validated_data)

        if password:
            user.set_password(password)
            user.save()

        return user


class RegisterSerializer(serializers.ModelSerializer):
    """Serializer for user registration with only email required."""

    email = serializers.EmailField(
        required=True, validators=[UniqueValidator(queryset=User.objects.all())]
    )

    class Meta:
        model = User
        fields = ("email",)

    def create(self, validated_data):
        user = User.objects.create_user(**validated_data)
        # Password will be set later after activation
        return user


class SetPasswordSerializer(serializers.Serializer):
    """Serializer for setting a user's password after activation."""

    password = serializers.CharField(
        write_only=True, required=True, validators=[validate_password]
    )
    password2 = serializers.CharField(write_only=True, required=True)
    first_name = serializers.CharField(required=True)
    last_name = serializers.CharField(required=True)

    def validate(self, attrs):
        if attrs["password"] != attrs["password2"]:
            raise serializers.ValidationError(
                {"password": "Password fields didn't match."}
            )
        return attrs

    def update(self, instance, validated_data):
        if not instance.is_active:
            raise ValidationError("This user account is not activated yet.")

        instance.set_password(validated_data.get("password"))

        # Update required fields
        instance.first_name = validated_data.get("first_name")
        instance.last_name = validated_data.get("last_name")

        instance.save()
        return instance


class GoogleOAuthSerializer(serializers.Serializer):
    """Serializer for Google OAuth authentication."""

    access_token = serializers.CharField(required=True)

    def validate_access_token(self, value):
        """Validate the Google access token and extract user info."""
        from google.auth.transport import requests
        from google.oauth2 import id_token
        from django.conf import settings

        try:
            # Verify the token with Google
            idinfo = id_token.verify_oauth2_token(
                value, requests.Request(), settings.GOOGLE_OAUTH2_CLIENT_ID
            )

            # Check if the token is for our app
            if idinfo["aud"] != settings.GOOGLE_OAUTH2_CLIENT_ID:
                raise serializers.ValidationError("Invalid token audience")

            return idinfo
        except ValueError as e:
            raise serializers.ValidationError(f"Invalid token: {str(e)}")

    def create(self, validated_data):
        """Create or get user from Google OAuth data."""
        idinfo = validated_data["access_token"]

        email = idinfo.get("email")
        google_id = idinfo.get("sub")
        first_name = idinfo.get("given_name", "")
        last_name = idinfo.get("family_name", "")
        profile_picture = idinfo.get("picture", "")

        if not email or not google_id:
            raise serializers.ValidationError(
                "Missing required user information from Google"
            )

        # Try to find existing user by Google ID
        try:
            user = User.objects.get(google_id=google_id)
            return user
        except User.DoesNotExist:
            pass

        # Try to find existing user by email
        try:
            user = User.objects.get(email=email)
            # Link Google account to existing user
            user.google_id = google_id
            user.profile_picture = profile_picture
            user.is_google_user = True
            user.is_active = True
            user.save()
            return user
        except User.DoesNotExist:
            pass

        # Create new user
        user = User.objects.create_google_user(
            email=email,
            google_id=google_id,
            first_name=first_name,
            last_name=last_name,
            profile_picture=profile_picture,
        )
        return user
