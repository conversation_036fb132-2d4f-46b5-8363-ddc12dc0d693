import stripe
from decimal import Decimal
from django.conf import settings
from django.utils import timezone

# Configure stripe with the secret key
stripe.api_key = settings.STRIPE_SECRET_KEY

# Conversion rates - based on 1 USD to X of other currency
CONVERSION_RATES = {
    "usd": Decimal("1.0"),  # 1 USD = 1 USD
    "cad": Decimal("1.4"),  # 1 USD = 1.4 CAD
}


def convert_usd_to_currency(amount_usd, target_currency):
    """
    Convert an amount in USD to the target currency

    Args:
        amount_usd (Decimal): Amount in USD
        target_currency (str): Target currency code (lowercase)

    Returns:
        Decimal: Amount in target currency
    """
    if target_currency.lower() not in CONVERSION_RATES:
        raise ValueError(f"Unsupported currency: {target_currency}")

    # Ensure we're working with Decimal for precision
    if not isinstance(amount_usd, Decimal):
        amount_usd = Decimal(str(amount_usd))

    conversion_rate = CONVERSION_RATES[target_currency.lower()]
    return amount_usd * conversion_rate


def convert_currency_to_usd(amount, source_currency):
    """
    Convert an amount from a currency to USD

    Args:
        amount (Decimal): Amount in source currency
        source_currency (str): Source currency code (lowercase)

    Returns:
        Decimal: Amount in USD
    """
    if source_currency.lower() not in CONVERSION_RATES:
        raise ValueError(f"Unsupported currency: {source_currency}")

    # Ensure we're working with Decimal for precision
    if not isinstance(amount, Decimal):
        amount = Decimal(str(amount))

    conversion_rate = CONVERSION_RATES[source_currency.lower()]
    return amount / conversion_rate


def process_stripe_refund(payment_intent_id, amount=None, reason=None):
    """
    Process a refund through Stripe

    Args:
        payment_intent_id (str): The Stripe payment intent ID to refund
        amount (Decimal, optional): Amount to refund (in smallest currency unit, e.g., cents).
                                   If None, refunds the entire payment.
        reason (str, optional): Reason for the refund

    Returns:
        dict: The Stripe refund object
    """
    try:
        refund_params = {
            "payment_intent": payment_intent_id,
        }

        if amount is not None:
            # Convert to cents/smallest currency unit for Stripe
            refund_params["amount"] = int(amount * 100)

        if reason:
            refund_params["reason"] = reason

        # Create the refund in Stripe
        refund = stripe.Refund.create(**refund_params)
        return refund
    except stripe.StripeError as e:
        # Handle any Stripe errors
        raise ValueError(f"Stripe refund error: {str(e)}")


def validate_refund_eligibility(purchase):
    """
    Validate if a purchase is eligible for refund

    Args:
        purchase: The CreditPurchase instance to check

    Returns:
        tuple: (is_eligible, reason)
    """
    from .models import PaymentStatus

    # Check if purchase is completed
    if purchase.status != PaymentStatus.COMPLETED:
        return False, "Purchase is not in completed status"

    # Check if already refunded
    if purchase.status == PaymentStatus.REFUNDED:
        return False, "Purchase has already been refunded"

    # Check if there's a payment intent ID (required for refund)
    if not purchase.stripe_payment_intent_id:
        return False, "No payment intent ID found for this purchase"

    # Check if the payment was completed within the last 7 days
    if purchase.payment_date:
        seven_days_ago = timezone.now() - timezone.timedelta(days=7)
        if purchase.payment_date < seven_days_ago:
            return (
                False,
                "Refunds are only available within 7 days of payment completion",
            )

    return True, None
