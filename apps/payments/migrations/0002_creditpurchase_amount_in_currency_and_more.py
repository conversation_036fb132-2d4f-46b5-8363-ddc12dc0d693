# Generated by Django 5.2 on 2025-05-05 00:52

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('payments', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='creditpurchase',
            name='amount_in_currency',
            field=models.DecimalField(blank=True, decimal_places=2, help_text='Amount in the selected currency', max_digits=10, null=True, verbose_name='amount in selected currency'),
        ),
        migrations.AddField(
            model_name='creditpurchase',
            name='currency',
            field=models.CharField(choices=[('usd', 'USD'), ('cad', 'CAD')], default='usd', help_text='Currency used for payment', max_length=3),
        ),
    ]
