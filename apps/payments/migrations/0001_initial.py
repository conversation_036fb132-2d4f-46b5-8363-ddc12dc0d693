# Generated by Django 5.2 on 2025-05-04 22:48

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='CreditPurchase',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount_usd', models.DecimalField(decimal_places=2, help_text='Amount paid in USD', max_digits=10, verbose_name='amount in USD')),
                ('credits_amount', models.PositiveIntegerField(help_text='Number of credits purchased (1 USD = 100 credits)', verbose_name='credits amount')),
                ('stripe_payment_intent_id', models.CharField(blank=True, max_length=255, null=True, unique=True)),
                ('stripe_checkout_session_id', models.Char<PERSON>ield(blank=True, max_length=255, null=True, unique=True)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('completed', 'Completed'), ('failed', 'Failed'), ('refunded', 'Refunded'), ('canceled', 'Canceled')], default='pending', max_length=20)),
                ('payment_date', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='credit_purchases', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Credit Purchase',
                'verbose_name_plural': 'Credit Purchases',
                'ordering': ['-created_at'],
            },
        ),
    ]
