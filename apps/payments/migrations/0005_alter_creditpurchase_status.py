# Generated by Django 5.2 on 2025-05-05 03:46

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('payments', '0004_alter_refund_reason'),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name='creditpurchase',
            name='status',
            field=models.CharField(choices=[('pending', 'Pending'), ('completed', 'Completed'), ('failed', 'Failed'), ('refunded', 'Refunded'), ('canceled', 'Canceled'), ('expired', 'Expired')], default='pending', max_length=20),
        ),
    ]
