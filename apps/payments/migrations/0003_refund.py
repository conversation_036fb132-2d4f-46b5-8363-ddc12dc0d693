# Generated by Django 5.2 on 2025-05-05 02:57

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('payments', '0002_creditpurchase_amount_in_currency_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='Refund',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount_usd', models.DecimalField(decimal_places=2, help_text='Amount refunded in USD', max_digits=10, verbose_name='amount in USD')),
                ('amount_in_currency', models.DecimalField(decimal_places=2, help_text='Amount refunded in original currency', max_digits=10, verbose_name='amount in original currency')),
                ('credits_amount', models.PositiveIntegerField(help_text='Number of credits refunded', verbose_name='credits amount')),
                ('stripe_refund_id', models.Char<PERSON>ield(blank=True, max_length=255, null=True, unique=True)),
                ('reason', models.TextField(help_text='Reason for the refund', verbose_name='refund reason')),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('completed', 'Completed'), ('failed', 'Failed')], default='pending', max_length=20)),
                ('refund_date', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('purchase', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='refunds', to='payments.creditpurchase')),
            ],
            options={
                'verbose_name': 'Refund',
                'verbose_name_plural': 'Refunds',
                'ordering': ['-created_at'],
            },
        ),
    ]
