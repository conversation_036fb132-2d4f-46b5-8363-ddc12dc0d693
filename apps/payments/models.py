from django.db import models
from django.conf import settings
from django.utils.translation import gettext_lazy as _


class PaymentStatus(models.TextChoices):
    """Payment status choices"""

    PENDING = "pending", _("Pending")
    COMPLETED = "completed", _("Completed")
    FAILED = "failed", _("Failed")
    REFUNDED = "refunded", _("Refunded")
    CANCELED = "canceled", _("Canceled")
    EXPIRED = "expired", _("Expired")


class RefundStatus(models.TextChoices):
    """Refund status choices"""

    PENDING = "pending", _("Pending")
    COMPLETED = "completed", _("Completed")
    FAILED = "failed", _("Failed")


class RefundReason(models.TextChoices):
    """Refund reason choices (must match <PERSON><PERSON>'s allowed values)"""

    DUPLICATE = "duplicate", _("Duplicate payment")
    FRAUDULENT = "fraudulent", _("Fraudulent payment")
    REQUESTED_BY_CUSTOMER = "requested_by_customer", _("Requested by customer")


class Currency(models.TextChoices):
    """Currency choices"""

    USD = "usd", _("USD")
    CAD = "cad", _("CAD")


class CreditPurchase(models.Model):
    """
    Model to track credit purchases through Stripe
    """

    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="credit_purchases",
    )
    amount_usd = models.DecimalField(
        _("amount in USD"),
        max_digits=10,
        decimal_places=2,
        help_text=_("Amount paid in USD"),
    )
    currency = models.CharField(
        max_length=3,
        choices=Currency.choices,
        default=Currency.USD,
        help_text=_("Currency used for payment"),
    )
    amount_in_currency = models.DecimalField(
        _("amount in selected currency"),
        max_digits=10,
        decimal_places=2,
        help_text=_("Amount in the selected currency"),
        null=True,
        blank=True,
    )
    credits_amount = models.PositiveIntegerField(
        _("credits amount"),
        help_text=_("Number of credits purchased (1 USD = 100 credits)"),
    )
    stripe_payment_intent_id = models.CharField(
        max_length=255, blank=True, null=True, unique=True
    )
    stripe_checkout_session_id = models.CharField(
        max_length=255, blank=True, null=True, unique=True
    )
    status = models.CharField(
        max_length=20, choices=PaymentStatus.choices, default=PaymentStatus.PENDING
    )
    payment_date = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.user.email} - ${self.amount_usd} ({self.credits_amount} credits) - {self.status}"

    class Meta:
        verbose_name = _("Credit Purchase")
        verbose_name_plural = _("Credit Purchases")
        ordering = ["-created_at"]


class Refund(models.Model):
    """
    Model to track refunds for credit purchases
    """

    purchase = models.ForeignKey(
        CreditPurchase,
        on_delete=models.CASCADE,
        related_name="refunds",
    )
    amount_usd = models.DecimalField(
        _("amount in USD"),
        max_digits=10,
        decimal_places=2,
        help_text=_("Amount refunded in USD"),
    )
    amount_in_currency = models.DecimalField(
        _("amount in original currency"),
        max_digits=10,
        decimal_places=2,
        help_text=_("Amount refunded in original currency"),
    )
    credits_amount = models.PositiveIntegerField(
        _("credits amount"),
        help_text=_("Number of credits refunded"),
    )
    stripe_refund_id = models.CharField(
        max_length=255, blank=True, null=True, unique=True
    )
    reason = models.CharField(
        _("refund reason"),
        max_length=50,
        choices=RefundReason.choices,
        default=RefundReason.REQUESTED_BY_CUSTOMER,
        help_text=_("Reason for the refund"),
    )
    status = models.CharField(
        max_length=20, choices=RefundStatus.choices, default=RefundStatus.PENDING
    )
    refund_date = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Refund for {self.purchase} - ${self.amount_usd} ({self.credits_amount} credits) - {self.status}"

    class Meta:
        verbose_name = _("Refund")
        verbose_name_plural = _("Refunds")
        ordering = ["-created_at"]
