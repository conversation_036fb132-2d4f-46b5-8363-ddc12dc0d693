from rest_framework import serializers
from .models import CreditPurchase, Refund


class RefundSerializer(serializers.ModelSerializer):
    """Serializer for Refund model"""

    amount_usd = serializers.FloatField()
    amount_in_currency = serializers.FloatField()

    class Meta:
        model = Refund
        fields = [
            "id",
            "amount_usd",
            "amount_in_currency",
            "credits_amount",
            "status",
            "reason",
            "refund_date",
            "created_at",
        ]


class PurchaseSerializer(serializers.ModelSerializer):
    """Serializer for CreditPurchase model"""

    amount_usd = serializers.FloatField()
    amount_in_currency = serializers.SerializerMethodField()
    refunds = RefundSerializer(many=True, read_only=True)
    user_email = serializers.SerializerMethodField()

    class Meta:
        model = CreditPurchase
        fields = [
            "id",
            "user_email",
            "amount_usd",
            "currency",
            "amount_in_currency",
            "credits_amount",
            "status",
            "payment_date",
            "created_at",
            "refunds",
        ]

    def get_amount_in_currency(self, obj):
        if obj.amount_in_currency:
            return float(obj.amount_in_currency)
        return float(obj.amount_usd)

    def get_user_email(self, obj):
        return obj.user.email if obj.user else None
