import json
import time
import stripe
from decimal import Decimal
from django.conf import settings
from django.utils import timezone
from django.shortcuts import render
from django.http import JsonResponse
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status, serializers
from django.contrib.auth import get_user_model
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_POST
from rest_framework.permissions import IsAuthenticated
from rest_framework.pagination import PageNumberPagination
import os
from dotenv import load_dotenv

load_dotenv()
from .serializers import PurchaseSerializer
from apps.credit_balance.services import add_credit, deduct_credit
from .models import (
    Refund,
    Currency,
    RefundStatus,
    RefundReason,
    PaymentStatus,
    CreditPurchase,
)
from .utils import (
    process_stripe_refund,
    convert_usd_to_currency,
    convert_currency_to_usd,
    validate_refund_eligibility,
)

User = get_user_model()

# Configure stripe with the secret key
stripe.api_key = settings.STRIPE_SECRET_KEY

# Minimum payment amount in USD (using Decimal for precision)
MIN_PAYMENT_AMOUNT = Decimal(os.getenv("MIN_PAYMENT_AMOUNT_FOR_CREDITS", "19.99"))


class PurchaseHistoryView(APIView):
    """
    API view to get purchase history for the authenticated user
    """

    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            # Use the authenticated user
            user = request.user

            # Get status filter (optional)
            status_filter = request.query_params.get("status")

            # Get page size (optional, default to 10)
            page_size = int(request.query_params.get("page_size", 10))
            page_size = min(page_size, 100)  # Cap at 100 to prevent performance issues

            # Get page number (optional, default to 1)
            page = int(request.query_params.get("page", 1))

            # Get user's purchases
            purchases_query = CreditPurchase.objects.filter(user=user)

            # Apply status filter if provided
            if status_filter and status_filter in [
                choice[0] for choice in PaymentStatus.choices
            ]:
                purchases_query = purchases_query.filter(status=status_filter)

            # Order by most recent first
            purchases_query = purchases_query.order_by("-created_at")

            # Count total purchases for pagination
            total_count = purchases_query.count()

            # Apply pagination
            start_idx = (page - 1) * page_size
            end_idx = start_idx + page_size
            purchases = purchases_query[start_idx:end_idx]

            # Serialize the purchases
            serializer = PurchaseSerializer(purchases, many=True)

            # Calculate pagination info
            total_pages = (
                (total_count + page_size - 1) // page_size if total_count > 0 else 0
            )
            has_next = page < total_pages
            has_previous = page > 1

            # Build the URL with all existing query parameters
            current_params = request.query_params.dict()

            # Create next page URL
            if has_next:
                next_params = current_params.copy()
                next_params["page"] = page + 1
                next_url = request.build_absolute_uri(
                    "?"
                    + "&".join([f"{key}={value}" for key, value in next_params.items()])
                )
            else:
                next_url = None

            # Create previous page URL
            if has_previous:
                prev_params = current_params.copy()
                prev_params["page"] = page - 1
                prev_url = request.build_absolute_uri(
                    "?"
                    + "&".join([f"{key}={value}" for key, value in prev_params.items()])
                )
            else:
                prev_url = None

            return Response(
                {
                    "count": total_count,
                    "next": next_url,
                    "previous": prev_url,
                    "current_page": page,
                    "total_pages": total_pages,
                    "results": serializer.data,
                }
            )

        except ValueError as e:
            return Response(
                {"error": "Invalid pagination parameters"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class RefundView(APIView):
    """
    API view to process refunds for credit purchases
    """

    permission_classes = [IsAuthenticated]

    def post(self, request, purchase_id):
        try:
            # Get the purchase
            try:
                purchase = CreditPurchase.objects.get(id=purchase_id, user=request.user)
            except CreditPurchase.DoesNotExist:
                return Response(
                    {"error": "Purchase not found"},
                    status=status.HTTP_404_NOT_FOUND,
                )

            # Check if refund is already in progress
            existing_refunds = Refund.objects.filter(
                purchase=purchase,
                status__in=[RefundStatus.PENDING, RefundStatus.COMPLETED],
            )
            if existing_refunds.exists():
                return Response(
                    {"error": "A refund for this purchase already exists"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Validate refund eligibility
            is_eligible, reason = validate_refund_eligibility(purchase)
            if not is_eligible:
                return Response(
                    {"error": reason},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Get refund reason (required)
            refund_reason = request.data.get("reason")

            # Validate the refund reason matches Stripe's allowed values
            if not refund_reason or refund_reason not in [
                choice[0] for choice in RefundReason.choices
            ]:
                return Response(
                    {
                        "error": f"Invalid refund reason. Must be one of: {', '.join([choice[0] for choice in RefundReason.choices])}"
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Process the refund in Stripe
            try:
                refund_response = process_stripe_refund(
                    payment_intent_id=purchase.stripe_payment_intent_id,
                    reason=refund_reason,
                )
            except ValueError as e:
                return Response(
                    {"error": str(e)},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Create a refund record
            refund = Refund.objects.create(
                purchase=purchase,
                amount_usd=purchase.amount_usd,
                amount_in_currency=purchase.amount_in_currency or purchase.amount_usd,
                credits_amount=purchase.credits_amount,
                stripe_refund_id=refund_response.id,
                reason=refund_reason,
                status=(
                    RefundStatus.COMPLETED
                    if refund_response.status == "succeeded"
                    else RefundStatus.PENDING
                ),
                refund_date=(
                    timezone.now() if refund_response.status == "succeeded" else None
                ),
            )

            # Update purchase status
            purchase.status = PaymentStatus.REFUNDED
            purchase.save()

            # Deduct credits from user's balance
            deduct_credit(
                user=purchase.user,
                amount=purchase.credits_amount,
                description=f"Refund for purchase of {purchase.credits_amount} credits",
            )

            return Response(
                {
                    "status": "success",
                    "message": "Refund processed successfully",
                    "refund_id": refund.id,
                    "refund_status": refund.status,
                    "amount_refunded_usd": float(refund.amount_usd),
                    "credits_refunded": refund.credits_amount,
                }
            )

        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class CreditConversionView(APIView):
    """
    API view to convert credits to monetary amount in different currencies
    """

    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            # Get credits amount and target currency from request
            credits = int(request.query_params.get("credits", 0))
            currency = request.query_params.get("currency", "usd").lower()

            # Validate currency
            if currency not in [c[0] for c in Currency.choices]:
                return Response(
                    {
                        "error": f"Unsupported currency: {currency}. Supported currencies: {', '.join([c[0] for c in Currency.choices])}"
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Convert credits to USD (100 credits = 1 USD)
            amount_usd = Decimal(credits) / 100

            # Convert USD to target currency
            amount_in_currency = convert_usd_to_currency(amount_usd, currency)

            return Response(
                {
                    "credits": credits,
                    "currency": currency,
                    "amount_usd": float(amount_usd),
                    "amount_in_currency": float(amount_in_currency),
                    "conversion_rate": float(
                        convert_usd_to_currency(Decimal("1.0"), currency)
                    ),
                }
            )

        except ValueError as e:
            return Response(
                {"error": "Invalid credits value. Please provide a valid integer."},
                status=status.HTTP_400_BAD_REQUEST,
            )
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class ConvertPricesView(APIView):
    """
    API view to convert USD prices to different currencies for credit packages
    """

    permission_classes = [IsAuthenticated]

    def post(self, request):
        try:
            # Get prices array and target currency from request
            prices = request.data.get("prices", [])
            currency = request.data.get("currency", "usd").lower()

            # Validate currency
            if currency not in [c[0] for c in Currency.choices]:
                return Response(
                    {
                        "error": f"Unsupported currency: {currency}. Supported currencies: {', '.join([c[0] for c in Currency.choices])}"
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Validate prices array
            if not isinstance(prices, list):
                return Response(
                    {"error": "Prices must be provided as an array"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            converted_prices = []
            conversion_rate = convert_usd_to_currency(Decimal("1.0"), currency)

            for price in prices:
                try:
                    # Convert to Decimal for precision
                    price_usd = Decimal(str(price))
                    price_in_currency = convert_usd_to_currency(price_usd, currency)

                    converted_prices.append(
                        {
                            "original_usd": float(price_usd),
                            "converted": float(price_in_currency),
                            "currency": currency.upper(),
                        }
                    )
                except (ValueError, TypeError) as e:
                    return Response(
                        {"error": f"Invalid price value: {price}"},
                        status=status.HTTP_400_BAD_REQUEST,
                    )

            return Response(
                {
                    "currency": currency.upper(),
                    "conversion_rate": float(conversion_rate),
                    "prices": converted_prices,
                }
            )

        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class CreateCheckoutSessionView(APIView):
    """
    API view to create a Stripe checkout session for credit purchase
    """

    permission_classes = [IsAuthenticated]

    def post(self, request):
        try:
            # Get the amount and currency from the request
            # Convert to string first to avoid floating-point precision issues
            amount_raw = request.data.get("amount", 0)
            amount_usd = Decimal(str(amount_raw))
            currency = request.data.get("currency", "usd").lower()

            # Validate currency
            if currency not in [c[0] for c in Currency.choices]:
                return Response(
                    {
                        "error": f"Unsupported currency: {currency}. Supported currencies: {', '.join([c[0] for c in Currency.choices])}"
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Validate minimum amount in USD
            if amount_usd < MIN_PAYMENT_AMOUNT:
                return Response(
                    {"error": f"Minimum payment amount is ${MIN_PAYMENT_AMOUNT} USD"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Calculate amount in selected currency
            amount_in_currency = convert_usd_to_currency(amount_usd, currency)

            # Calculate credits (1 USD = 100 credits)
            credits_amount = int(amount_usd * 100)

            # Convert to cents for Stripe (using Decimal for precision)
            # This prevents floating-point precision issues like 19.99 * 100 = 1998.9999999999998
            unit_amount_cents = int(amount_in_currency * 100)

            # Create the checkout session with the selected currency
            checkout_session = stripe.checkout.Session.create(
                payment_method_types=["card"],
                line_items=[
                    {
                        "price_data": {
                            "currency": currency,
                            "product_data": {
                                "name": "MizuCredits",
                                "description": f"Buy {credits_amount} credits for MizuFlow AI",
                            },
                            "unit_amount": unit_amount_cents,  # Use precise cents calculation
                            "tax_behavior": "exclusive",
                        },
                        "quantity": 1,
                    },
                ],
                mode="payment",
                automatic_tax={"enabled": True},
                expires_at=int(time.time() + (3600 * 2)),  # Expire in 2 hours
                success_url=f"{settings.FRONTEND_URL}/payment/success?session_id={{CHECKOUT_SESSION_ID}}",
                cancel_url=f"{settings.FRONTEND_URL}/payment/cancel",
                metadata={
                    "user_id": str(request.user.id),
                    "credits_amount": str(credits_amount),
                    "currency": currency,
                    "amount_usd": str(amount_usd),
                    "amount_in_currency": str(amount_in_currency),
                },
            )

            # Record the purchase
            purchase = CreditPurchase.objects.create(
                user=request.user,
                amount_usd=amount_usd,
                currency=currency,
                amount_in_currency=amount_in_currency,
                credits_amount=credits_amount,
                stripe_checkout_session_id=checkout_session.id,
                status=PaymentStatus.PENDING,
            )

            return Response(
                {
                    "checkout_url": checkout_session.url,
                    "currency": currency,
                    "amount_usd": float(amount_usd),
                    "amount_in_currency": float(amount_in_currency),
                    "credits_amount": credits_amount,
                }
            )

        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class PaymentSuccessView(APIView):
    """
    API view to verify payment success
    """

    permission_classes = [IsAuthenticated]

    def get(self, request):
        session_id = request.query_params.get("session_id")
        if not session_id:
            return Response(
                {"error": "No session ID provided"}, status=status.HTTP_400_BAD_REQUEST
            )

        try:
            # Verify the session status
            checkout_session = stripe.checkout.Session.retrieve(session_id)

            # Attempt to find the purchase record
            try:
                purchase = CreditPurchase.objects.get(
                    stripe_checkout_session_id=session_id
                )
            except CreditPurchase.DoesNotExist:
                return Response(
                    {"error": "Purchase record not found"},
                    status=status.HTTP_404_NOT_FOUND,
                )

            # If payment is completed and purchase is not yet marked as completed
            if (
                checkout_session.payment_status == "paid"
                and purchase.status != PaymentStatus.COMPLETED
            ):
                # Update purchase record
                purchase.status = PaymentStatus.COMPLETED
                purchase.stripe_payment_intent_id = checkout_session.payment_intent
                purchase.payment_date = timezone.now()
                purchase.save()

                # Add credits to the user
                add_credit(
                    user=purchase.user,
                    amount=purchase.credits_amount,
                    description=f"Purchase of {purchase.credits_amount} credits",
                )

            # If payment is already completed, still show success info
            if (
                checkout_session.payment_status == "paid"
                and purchase.status == PaymentStatus.COMPLETED
            ):
                return Response(
                    {
                        "status": "success",
                        "message": f"Payment successful. {purchase.credits_amount} credits were added to your account.",
                        "purchase_id": purchase.id,
                        "amount_usd": float(purchase.amount_usd),
                        "currency": purchase.currency,
                        "amount_in_currency": (
                            float(purchase.amount_in_currency)
                            if purchase.amount_in_currency
                            else None
                        ),
                        "credits_amount": purchase.credits_amount,
                        "payment_date": purchase.payment_date,
                    }
                )

            # For pending or other status payments
            return Response(
                {
                    "status": (
                        "pending"
                        if checkout_session.payment_status != "paid"
                        else "processing"
                    ),
                    "message": "Payment is being processed.",
                }
            )

        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


@csrf_exempt
@require_POST
def stripe_webhook(request):
    """
    Webhook to handle Stripe events
    """
    payload = request.body
    sig_header = request.META.get("HTTP_STRIPE_SIGNATURE")

    try:
        event = stripe.Webhook.construct_event(
            payload, sig_header, settings.STRIPE_WEBHOOK_SECRET
        )
    except ValueError:
        return JsonResponse({"error": "Invalid payload"}, status=400)
    except stripe.SignatureVerificationError:
        return JsonResponse({"error": "Invalid signature"}, status=400)

    # Log the event type for debugging
    print(f"Processing Stripe event: {event.type}")

    # Extract common parameters
    event_data = event.data.object

    # Handle checkout.session.completed event
    if event.type == "checkout.session.completed":
        session = event_data
        session_id = session.id

        try:
            purchase = CreditPurchase.objects.get(stripe_checkout_session_id=session_id)

            if purchase.status != PaymentStatus.COMPLETED:
                # Update purchase record
                purchase.status = PaymentStatus.COMPLETED
                purchase.stripe_payment_intent_id = session.payment_intent
                purchase.payment_date = timezone.now()
                purchase.save()

                # Add credits to the user
                add_credit(
                    user=purchase.user,
                    amount=purchase.credits_amount,
                    description=f"Purchase of {purchase.credits_amount} credits (paid {purchase.amount_in_currency} {purchase.currency.upper()})",
                )
        except CreditPurchase.DoesNotExist:
            # Log or handle the error appropriately
            pass

    # Handle checkout.session.expired event
    elif event.type == "checkout.session.expired":
        session = event_data
        session_id = session.id

        try:
            # Find the purchase by checkout session ID
            purchase = CreditPurchase.objects.get(
                stripe_checkout_session_id=session_id,
                status=PaymentStatus.PENDING,  # Only update if still pending
            )

            # Update purchase record to expired
            purchase.status = PaymentStatus.EXPIRED
            purchase.save()

            # Log the expired session
            print(
                f"Payment session expired: {session_id} for user {purchase.user.email}"
            )

        except CreditPurchase.DoesNotExist:
            # Session wasn't found or already processed
            pass

    # Handle refund.created event - Deduct credits immediately when refund starts processing
    elif event.type == "refund.created":
        refund_obj = event_data
        payment_intent_id = refund_obj.payment_intent
        refund_id = refund_obj.id

        try:
            # Find the purchase by payment intent ID
            purchase = CreditPurchase.objects.get(
                stripe_payment_intent_id=payment_intent_id
            )

            # Check if we already have a refund record for this refund_id
            existing_refund = Refund.objects.filter(stripe_refund_id=refund_id).first()

            if not existing_refund:
                # Create a new refund record with PENDING status
                refund = Refund.objects.create(
                    purchase=purchase,
                    amount_usd=purchase.amount_usd,
                    amount_in_currency=purchase.amount_in_currency
                    or purchase.amount_usd,
                    credits_amount=purchase.credits_amount,
                    stripe_refund_id=refund_id,
                    reason=RefundReason.REQUESTED_BY_CUSTOMER,  # Default reason
                    status=RefundStatus.PENDING,
                    refund_date=None,  # Will be set when refund completes
                )

                # Deduct credits immediately to prevent usage during processing
                deduct_credit(
                    user=purchase.user,
                    amount=purchase.credits_amount,
                    description=f"Refund initiated for {purchase.credits_amount} credits (ID: {refund_id})",
                )

                # Update purchase status to PENDING refund
                purchase.status = PaymentStatus.REFUNDED
                purchase.save()

                print(
                    f"Refund created: {refund_id} for purchase {purchase.pk}. Credits deducted."
                )

            else:
                print(f"Refund already recorded: {refund_id}")

        except CreditPurchase.DoesNotExist:
            print(f"Purchase not found for refund: {refund_id}")
            pass

    # Handle refund.failed event - Restore credits if refund fails
    elif event.type == "refund.failed":
        refund_obj = event_data
        refund_id = refund_obj.id

        try:
            # Find our refund record
            refund = Refund.objects.get(stripe_refund_id=refund_id)
            purchase = refund.purchase

            # Only process if refund status is still pending
            if refund.status == RefundStatus.PENDING:
                # Set status to FAILED
                refund.status = RefundStatus.FAILED
                refund.save()

                # Restore credits to the user since refund failed
                add_credit(
                    user=purchase.user,
                    amount=purchase.credits_amount,
                    description=f"Refund failed for {purchase.credits_amount} credits (ID: {refund_id}), credits restored",
                )

                # Revert purchase status to COMPLETED
                purchase.status = PaymentStatus.COMPLETED
                purchase.save()

                print(f"Refund failed: {refund_id}. Credits restored.")

        except Refund.DoesNotExist:
            print(f"Refund record not found: {refund_id}")
            pass
        except CreditPurchase.DoesNotExist:
            print(f"Purchase not found for refund: {refund_id}")
            pass

    # Handle charge.refunded event - Final confirmation
    elif event.type == "charge.refunded":
        charge = event_data
        payment_intent_id = charge.payment_intent

        try:
            # Find the purchase by payment intent ID
            purchase = CreditPurchase.objects.get(
                stripe_payment_intent_id=payment_intent_id
            )

            # Get refund information
            try:
                # Try to retrieve refund info directly from Stripe
                refunds = stripe.Refund.list(payment_intent=payment_intent_id)

                if refunds and refunds.data:
                    latest_refund = refunds.data[0]
                    refund_id = latest_refund.id

                    # Find or create the refund record
                    refund, created = Refund.objects.get_or_create(
                        stripe_refund_id=refund_id,
                        defaults={
                            "purchase": purchase,
                            "amount_usd": purchase.amount_usd,
                            "amount_in_currency": purchase.amount_in_currency
                            or purchase.amount_usd,
                            "credits_amount": purchase.credits_amount,
                            "reason": RefundReason.REQUESTED_BY_CUSTOMER,
                            "status": RefundStatus.COMPLETED,
                            "refund_date": timezone.now(),
                        },
                    )

                    if not created:
                        # Update the existing refund to COMPLETED
                        refund.status = RefundStatus.COMPLETED
                        refund.refund_date = timezone.now()
                        refund.save()

                    # Ensure purchase is marked as refunded
                    if purchase.status != PaymentStatus.REFUNDED:
                        purchase.status = PaymentStatus.REFUNDED
                        purchase.save()

                        # We only deduct credits here if they weren't already deducted in refund.created
                        # This is to handle cases where refund.created webhook wasn't received
                        if (
                            created
                        ):  # If we just created the refund record, credits weren't deducted yet
                            deduct_credit(
                                user=purchase.user,
                                amount=purchase.credits_amount,
                                description=f"Refund completed for {purchase.credits_amount} credits (ID: {refund_id})",
                            )

                    print(
                        f"Charge refunded: {refund_id}. Refund status updated to COMPLETED."
                    )
                else:
                    print(f"No refunds found for payment intent: {payment_intent_id}")
            except stripe.StripeError as e:
                print(f"Error retrieving refund information: {str(e)}")

        except CreditPurchase.DoesNotExist:
            print(f"Purchase not found for payment intent: {payment_intent_id}")
            pass

    return JsonResponse({"status": "success"})
