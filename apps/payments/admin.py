from django.contrib import admin
from .models import CreditPurchase, Refund


@admin.register(CreditPurchase)
class CreditPurchaseAdmin(admin.ModelAdmin):
    list_display = (
        "user",
        "amount_usd",
        "currency",
        "amount_in_currency",
        "credits_amount",
        "status",
        "payment_date",
        "created_at",
    )
    list_filter = ("status", "created_at", "payment_date", "currency")
    search_fields = (
        "user__email",
        "stripe_payment_intent_id",
        "stripe_checkout_session_id",
    )
    readonly_fields = (
        "stripe_payment_intent_id",
        "stripe_checkout_session_id",
        "created_at",
        "updated_at",
    )


@admin.register(Refund)
class RefundAdmin(admin.ModelAdmin):
    list_display = (
        "purchase",
        "amount_usd",
        "amount_in_currency",
        "credits_amount",
        "status",
        "refund_date",
        "created_at",
    )
    list_filter = ("status", "created_at", "refund_date")
    search_fields = (
        "purchase__user__email",
        "stripe_refund_id",
        "reason",
    )
    readonly_fields = (
        "stripe_refund_id",
        "created_at",
        "updated_at",
    )
