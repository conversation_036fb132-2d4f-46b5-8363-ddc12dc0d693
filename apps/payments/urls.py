from . import views
from django.urls import path

app_name = "payments"

urlpatterns = [
    path(
        "create-checkout-session/",
        views.CreateCheckoutSessionView.as_view(),
        name="create_checkout_session",
    ),
    path(
        "payment-success/",
        views.PaymentSuccessView.as_view(),
        name="payment_success",
    ),
    path("webhook/", views.stripe_webhook, name="stripe_webhook"),
    path(
        "convert-credits/",
        views.CreditConversionView.as_view(),
        name="convert_credits",
    ),
    path(
        "convert-prices/",
        views.ConvertPricesView.as_view(),
        name="convert_prices",
    ),
    path(
        "refund/<int:purchase_id>/",
        views.RefundView.as_view(),
        name="refund_purchase",
    ),
    path(
        "purchase-history/",
        views.PurchaseHistoryView.as_view(),
        name="purchase_history",
    ),
]
