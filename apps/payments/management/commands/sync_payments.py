import stripe
from datetime import timed<PERSON>ta
from django.conf import settings
from django.utils import timezone
from django.core.management.base import BaseCommand
from django.db import transaction

from apps.payments.models import CreditPurchase, PaymentStatus
from apps.credit_balance.services import add_credit

# Configure stripe with the secret key
stripe.api_key = settings.STRIPE_SECRET_KEY


class Command(BaseCommand):
    help = "Synchronize pending payments with <PERSON><PERSON> to handle missed webhook events"

    def add_arguments(self, parser):
        parser.add_argument(
            "--session-id",
            type=str,
            help="Specific Stripe checkout session ID to sync",
        )
        parser.add_argument(
            "--user-email",
            type=str,
            help="Sync all pending payments for a specific user email",
        )
        parser.add_argument(
            "--days",
            type=int,
            default=7,
            help="Number of days to look back for pending payments (default: 7)",
        )
        parser.add_argument(
            "--dry-run",
            action="store_true",
            help="Show what would be synced without making changes",
        )
        parser.add_argument(
            "--force",
            action="store_true",
            help="Force sync even if payment appears to be processing",
        )

    def handle(self, *args, **options):
        session_id = options.get("session_id")
        user_email = options.get("user_email")
        days = options["days"]
        dry_run = options["dry_run"]
        force = options["force"]

        if dry_run:
            self.stdout.write(
                self.style.WARNING("DRY RUN MODE - No changes will be made")
            )

        if session_id:
            # Sync specific session
            self.sync_specific_session(session_id, dry_run, force)
        elif user_email:
            # Sync all pending payments for a user
            self.sync_user_payments(user_email, dry_run, force)
        else:
            # Sync all pending payments within the time range
            self.sync_pending_payments(days, dry_run, force)

    def sync_specific_session(self, session_id, dry_run=False, force=False):
        """Sync a specific checkout session"""
        try:
            purchase = CreditPurchase.objects.get(stripe_checkout_session_id=session_id)
            self.stdout.write(f"Found purchase record: {purchase}")
            
            if self.sync_purchase_with_stripe(purchase, dry_run, force):
                self.stdout.write(
                    self.style.SUCCESS(f"Successfully synced session {session_id}")
                )
            else:
                self.stdout.write(
                    self.style.WARNING(f"No sync needed for session {session_id}")
                )
                
        except CreditPurchase.DoesNotExist:
            self.stdout.write(
                self.style.ERROR(f"No purchase record found for session {session_id}")
            )

    def sync_user_payments(self, user_email, dry_run=False, force=False):
        """Sync all pending payments for a specific user"""
        from apps.users.models import User
        
        try:
            user = User.objects.get(email=user_email)
            pending_purchases = CreditPurchase.objects.filter(
                user=user, status=PaymentStatus.PENDING
            )
            
            self.stdout.write(
                f"Found {pending_purchases.count()} pending purchases for {user_email}"
            )
            
            synced_count = 0
            for purchase in pending_purchases:
                if self.sync_purchase_with_stripe(purchase, dry_run, force):
                    synced_count += 1
                    
            self.stdout.write(
                self.style.SUCCESS(f"Synced {synced_count} payments for {user_email}")
            )
            
        except User.DoesNotExist:
            self.stdout.write(
                self.style.ERROR(f"No user found with email {user_email}")
            )

    def sync_pending_payments(self, days, dry_run=False, force=False):
        """Sync all pending payments within the specified time range"""
        cutoff_date = timezone.now() - timedelta(days=days)
        
        pending_purchases = CreditPurchase.objects.filter(
            status=PaymentStatus.PENDING,
            created_at__gte=cutoff_date
        ).order_by('-created_at')
        
        self.stdout.write(
            f"Found {pending_purchases.count()} pending purchases in the last {days} days"
        )
        
        synced_count = 0
        expired_count = 0
        error_count = 0
        
        for purchase in pending_purchases:
            try:
                result = self.sync_purchase_with_stripe(purchase, dry_run, force)
                if result == "synced":
                    synced_count += 1
                elif result == "expired":
                    expired_count += 1
            except Exception as e:
                error_count += 1
                self.stderr.write(
                    f"Error syncing purchase {purchase.id}: {str(e)}"
                )
        
        # Print summary
        self.stdout.write(self.style.SUCCESS(f"Sync completed:"))
        self.stdout.write(f"  - Synced: {synced_count}")
        self.stdout.write(f"  - Expired: {expired_count}")
        if error_count > 0:
            self.stdout.write(self.style.WARNING(f"  - Errors: {error_count}"))

    def sync_purchase_with_stripe(self, purchase, dry_run=False, force=False):
        """
        Sync a single purchase with Stripe
        Returns: "synced", "expired", "no_change", or False
        """
        if not purchase.stripe_checkout_session_id:
            self.stdout.write(
                f"Purchase {purchase.id} has no checkout session ID"
            )
            return False
            
        try:
            # Retrieve session from Stripe
            session = stripe.checkout.Session.retrieve(
                purchase.stripe_checkout_session_id
            )
            
            self.stdout.write(
                f"Stripe session {session.id} status: {session.status}, "
                f"payment_status: {session.payment_status}"
            )
            
            # Handle completed payments
            if session.payment_status == "paid" and session.status == "complete":
                if purchase.status == PaymentStatus.COMPLETED and not force:
                    self.stdout.write(f"Purchase {purchase.id} already completed")
                    return "no_change"
                    
                if not dry_run:
                    with transaction.atomic():
                        # Update purchase record
                        purchase.status = PaymentStatus.COMPLETED
                        purchase.stripe_payment_intent_id = session.payment_intent
                        purchase.payment_date = timezone.now()
                        purchase.save()
                        
                        # Add credits to user (check if already added)
                        add_credit(
                            user=purchase.user,
                            amount=purchase.credits_amount,
                            description=f"Purchase of {purchase.credits_amount} credits (synced from Stripe)",
                        )
                        
                    self.stdout.write(
                        self.style.SUCCESS(
                            f"✓ Completed purchase {purchase.id} - Added {purchase.credits_amount} credits to {purchase.user.email}"
                        )
                    )
                else:
                    self.stdout.write(
                        f"[DRY RUN] Would complete purchase {purchase.id} and add {purchase.credits_amount} credits"
                    )
                return "synced"
                
            # Handle expired sessions
            elif session.status in ["expired"]:
                if not dry_run:
                    purchase.status = PaymentStatus.EXPIRED
                    purchase.save()
                    self.stdout.write(
                        self.style.WARNING(f"✗ Expired purchase {purchase.id}")
                    )
                else:
                    self.stdout.write(
                        f"[DRY RUN] Would expire purchase {purchase.id}"
                    )
                return "expired"
                
            # Handle other statuses
            else:
                self.stdout.write(
                    f"Purchase {purchase.id} - Session status: {session.status}, "
                    f"payment_status: {session.payment_status} (no action needed)"
                )
                return "no_change"
                
        except stripe.StripeError as e:
            self.stderr.write(
                f"Stripe error for purchase {purchase.id}: {str(e)}"
            )
            return False
