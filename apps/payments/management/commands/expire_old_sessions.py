import stripe
from datetime import timedelta
from django.conf import settings
from django.utils import timezone
from django.core.management.base import BaseCommand

from apps.payments.models import CreditPurchase, PaymentStatus

# Configure stripe with the secret key
stripe.api_key = settings.STRIPE_SECRET_KEY


class Command(BaseCommand):
    help = (
        "Expires pending payment sessions that are older than the specified time period"
    )

    def add_arguments(self, parser):
        parser.add_argument(
            "--days",
            type=int,
            default=1,
            help="Number of days before a pending payment is considered expired (default: 1)",
        )
        parser.add_argument(
            "--check-stripe",
            action="store_true",
            help="Check Stripe API to confirm session is expired (slower but more accurate)",
        )

    def handle(self, *args, **options):
        days = options["days"]
        check_stripe = options["check_stripe"]
        cutoff_date = timezone.now() - timedelta(days=days)

        # Get all pending purchases older than the cutoff date
        pending_purchases = CreditPurchase.objects.filter(
            status=PaymentStatus.PENDING, created_at__lt=cutoff_date
        )

        # Initialize counters
        expired_count = 0
        error_count = 0
        skipped_count = 0

        self.stdout.write(f"Found {pending_purchases.count()} old pending purchases")

        for purchase in pending_purchases:
            try:
                # If check_stripe is True, verify with Stripe API
                if check_stripe and purchase.stripe_checkout_session_id:
                    try:
                        # Retrieve the session from Stripe
                        session = stripe.checkout.Session.retrieve(
                            purchase.stripe_checkout_session_id
                        )

                        # Only mark as expired if the session is expired or incomplete
                        if session.status in ["expired", "incomplete"]:
                            purchase.status = PaymentStatus.EXPIRED
                            purchase.save()
                            expired_count += 1
                            self.stdout.write(
                                f"Expired session {purchase.stripe_checkout_session_id} for user {purchase.user.email}"
                            )
                        else:
                            # Session is still valid in Stripe
                            skipped_count += 1
                            self.stdout.write(
                                f"Skipped active session {purchase.stripe_checkout_session_id} with status: {session.status}"
                            )
                    except stripe.StripeError as e:
                        # If Stripe can't find the session, it's likely expired or invalid
                        purchase.status = PaymentStatus.EXPIRED
                        purchase.save()
                        expired_count += 1
                        self.stdout.write(
                            f"Expired session {purchase.stripe_checkout_session_id} after Stripe error: {str(e)}"
                        )
                else:
                    # Just expire based on age without checking Stripe
                    purchase.status = PaymentStatus.EXPIRED
                    purchase.save()
                    expired_count += 1

            except Exception as e:
                error_count += 1
                self.stderr.write(f"Error processing purchase {purchase.id}: {str(e)}")

        # Print summary
        self.stdout.write(
            self.style.SUCCESS(f"Successfully expired {expired_count} pending sessions")
        )
        if skipped_count > 0:
            self.stdout.write(
                f"Skipped {skipped_count} sessions that are still active in Stripe"
            )
        if error_count > 0:
            self.stdout.write(
                self.style.WARNING(f"Encountered errors with {error_count} sessions")
            )
