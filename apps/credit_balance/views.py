from rest_framework.decorators import action
from rest_framework.response import Response
from django.contrib.auth import get_user_model
from django.shortcuts import get_object_or_404
from rest_framework import status, viewsets, mixins
from rest_framework.permissions import IsAuthenticated, IsAdminUser

from .models import CreditBalance, CreditBalanceTransaction
from .serializers import (
    CreditBalanceSerializer,
    UpdateCreditBalanceSerializer,
    CreditBalanceTransactionSerializer,
)
from .services import (
    add_credit,
    deduct_credit,
    get_user_transactions,
    get_or_create_user_credit_balance,
)

User = get_user_model()


class CreditBalanceViewSet(mixins.RetrieveModelMixin, viewsets.GenericViewSet):
    """
    ViewSet for managing user credit balance
    """

    serializer_class = CreditBalanceSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        """Return credit balances based on user permissions"""
        user = self.request.user
        if user.is_staff:
            return CreditBalance.objects.all()
        return CreditBalance.objects.filter(user=user)

    def retrieve(self, request, *args, **kwargs):
        """Get a user's credit balance by user ID"""
        pk = kwargs.get("pk")

        # If admin is checking someone else's balance
        if request.user.is_staff and pk != "me" and pk != str(request.user.id):
            user = get_object_or_404(User, id=pk)
            credit_balance = get_or_create_user_credit_balance(user)
        else:
            # User checking their own balance
            credit_balance = get_or_create_user_credit_balance(request.user)

        serializer = self.get_serializer(credit_balance)
        return Response(serializer.data)

    @action(detail=False, methods=["get"])
    def me(self, request):
        """Get the current user's credit balance"""
        credit_balance = get_or_create_user_credit_balance(request.user)
        serializer = self.get_serializer(credit_balance)
        return Response(serializer.data)

    @action(
        detail=True, methods=["post"], serializer_class=UpdateCreditBalanceSerializer
    )
    def add(self, request, pk=None):
        """Add credit to a user's balance"""
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        amount = serializer.validated_data["amount"]
        description = serializer.validated_data.get("description", "")

        # Determine which user to add credit to
        if pk == "me" or pk == str(request.user.id):
            user = request.user
        else:
            # Only admins can add credit to other users
            if not request.user.is_staff:
                return Response(
                    {"error": "Not authorized to modify other users' credit balance"},
                    status=status.HTTP_403_FORBIDDEN,
                )
            user = get_object_or_404(User, id=pk)

        try:
            transaction = add_credit(
                user=user,
                amount=abs(amount),  # Ensure amount is positive
                description=description,
            )
            return Response(
                {
                    "message": f"Successfully added {abs(amount)} points to balance",
                    "user_id": user.id,
                    "user_email": user.email,
                    "new_balance": transaction.balance_after,
                },
                status=status.HTTP_200_OK,
            )
        except ValueError as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)

    @action(
        detail=True, methods=["post"], serializer_class=UpdateCreditBalanceSerializer
    )
    def deduct(self, request, pk=None):
        """Deduct credit from a user's balance"""
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        amount = serializer.validated_data["amount"]
        description = serializer.validated_data.get("description", "")

        # Determine which user to deduct credit from
        if pk == "me" or pk == str(request.user.id):
            user = request.user
        else:
            # Only admins can deduct credit from other users
            if not request.user.is_staff:
                return Response(
                    {"error": "Not authorized to modify other users' credit balance"},
                    status=status.HTTP_403_FORBIDDEN,
                )
            user = get_object_or_404(User, id=pk)

        try:
            transaction = deduct_credit(
                user=user,
                amount=abs(amount),  # Ensure amount is positive
                description=description,
            )
            return Response(
                {
                    "message": f"Successfully deducted {abs(amount)} points from balance",
                    "user_id": user.id,
                    "user_email": user.email,
                    "new_balance": transaction.balance_after,
                },
                status=status.HTTP_200_OK,
            )
        except ValueError as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class CreditBalanceTransactionViewSet(
    mixins.ListModelMixin, mixins.RetrieveModelMixin, viewsets.GenericViewSet
):
    """
    ViewSet for viewing credit transactions
    """

    serializer_class = CreditBalanceTransactionSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        """Return transactions based on user permissions"""
        user = self.request.user
        user_id = self.kwargs.get("user_id")

        # If admin is viewing someone else's transactions
        if user.is_staff and user_id and user_id != str(user.id):
            return CreditBalanceTransaction.objects.filter(user_id=user_id)

        # User viewing their own transactions
        return CreditBalanceTransaction.objects.filter(user=user)

    @action(detail=False, methods=["get"])
    def user_transactions(self, request, user_id=None):
        """Get transactions for a specific user"""
        if user_id == "me" or user_id == str(request.user.id):
            # User checking their own transactions
            transactions = CreditBalanceTransaction.objects.filter(user=request.user)
        else:
            # Only admins can view other users' transactions
            if not request.user.is_staff:
                return Response(
                    {"error": "Not authorized to view other users' transactions"},
                    status=status.HTTP_403_FORBIDDEN,
                )
            user = get_object_or_404(User, id=user_id)
            transactions = CreditBalanceTransaction.objects.filter(user=user)

        page = self.paginate_queryset(transactions)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(transactions, many=True)
        return Response(serializer.data)
