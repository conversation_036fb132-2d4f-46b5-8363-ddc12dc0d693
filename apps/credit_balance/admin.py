from django.contrib import admin
from .models import CreditBalance, CreditBalanceTransaction


@admin.register(CreditBalance)
class CreditBalanceAdmin(admin.ModelAdmin):
    """Admin config for CreditBalance model"""

    list_display = ("user", "balance", "created_at", "updated_at")
    search_fields = ("user__email", "user__first_name", "user__last_name")
    readonly_fields = ("created_at", "updated_at")


@admin.register(CreditBalanceTransaction)
class CreditBalanceTransactionAdmin(admin.ModelAdmin):
    """Admin config for CreditBalanceTransaction model"""

    list_display = ("user", "transaction_type", "amount", "balance_after", "created_at")
    list_filter = ("transaction_type", "created_at")
    search_fields = (
        "user__email",
        "user__first_name",
        "user__last_name",
        "description",
    )
    readonly_fields = ("created_at",)
