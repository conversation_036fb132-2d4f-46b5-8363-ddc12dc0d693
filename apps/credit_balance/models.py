from django.db import models
from django.conf import settings
from django.utils.translation import gettext_lazy as _


class CreditBalance(models.Model):
    """
    Model to store user credit balance
    1 USD = 100 points
    Default balance for new users: x points
    """

    user = models.OneToOneField(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="credit_balance",
    )
    balance = models.PositiveIntegerField(
        _("credit balance"),
        default=0,
        help_text=_("Credit balance in points (1 USD = 100 points)"),
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.user.email} - {self.balance} points"

    class Meta:
        verbose_name = _("Credit Balance")
        verbose_name_plural = _("Credit Balances")


class TransactionType(models.TextChoices):
    """Transaction types for credit balance changes"""

    CREDIT = "CREDIT", _("Credit")  # Add points
    DEBIT = "DEBIT", _("Debit")  # Remove points


class CreditBalanceTransaction(models.Model):
    """
    Model to record all credit balance transactions
    """

    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="credit_transactions",
    )
    amount = models.IntegerField(
        _("transaction amount"),
        help_text=_("Amount in points (positive for credit, negative for debit)"),
    )
    balance_after = models.PositiveIntegerField(
        _("balance after transaction"),
        help_text=_("Credit balance after this transaction"),
    )
    transaction_type = models.CharField(
        max_length=10, choices=TransactionType.choices, default=TransactionType.CREDIT
    )
    description = models.CharField(max_length=255, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.user.email} - {self.transaction_type} - {self.amount} points"

    class Meta:
        verbose_name = _("Credit Transaction")
        verbose_name_plural = _("Credit Transactions")
        ordering = ["-created_at"]
