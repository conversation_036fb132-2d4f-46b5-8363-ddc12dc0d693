# Generated by Django 5.2 on 2025-05-03 17:59

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='CreditBalance',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('balance', models.PositiveIntegerField(default=1, help_text='Credit balance in points (1 USD = 100 points)', verbose_name='credit balance')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='credit_balance', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Credit Balance',
                'verbose_name_plural': 'Credit Balances',
            },
        ),
        migrations.CreateModel(
            name='CreditBalanceTransaction',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.IntegerField(help_text='Amount in points (positive for credit, negative for debit)', verbose_name='transaction amount')),
                ('balance_after', models.PositiveIntegerField(help_text='Credit balance after this transaction', verbose_name='balance after transaction')),
                ('transaction_type', models.CharField(choices=[('CREDIT', 'Credit'), ('DEBIT', 'Debit')], default='CREDIT', max_length=10)),
                ('description', models.CharField(blank=True, max_length=255)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='credit_transactions', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Credit Transaction',
                'verbose_name_plural': 'Credit Transactions',
                'ordering': ['-created_at'],
            },
        ),
    ]
