from django.urls import path, include
from django.urls.resolvers import URLResolver
from rest_framework.routers import Default<PERSON><PERSON><PERSON>

from .views import CreditBalanceViewSet, CreditBalanceTransactionViewSet

router = DefaultRouter()
router.register(r"balance", CreditBalanceViewSet, basename="credit-balance")
router.register(
    r"transactions", CreditBalanceTransactionViewSet, basename="credit-transactions"
)

# Additional URL patterns for user transactions
urlpatterns = [
    path("", include(router.urls)),
    path(
        "users/<str:user_id>/transactions/",
        CreditBalanceTransactionViewSet.as_view({"get": "user_transactions"}),
        name="user-transactions",
    ),
]
