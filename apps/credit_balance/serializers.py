from rest_framework import serializers
from .models import CreditBalance, CreditBalanceTransaction


class CreditBalanceSerializer(serializers.ModelSerializer):
    """Serializer for the CreditBalance model"""

    class Meta:
        model = CreditBalance
        fields = ["id", "user", "balance", "created_at", "updated_at"]
        read_only_fields = ["user", "created_at", "updated_at"]


class CreditBalanceTransactionSerializer(serializers.ModelSerializer):
    """Serializer for the CreditBalanceTransaction model"""

    class Meta:
        model = CreditBalanceTransaction
        fields = [
            "id",
            "user",
            "amount",
            "balance_after",
            "transaction_type",
            "description",
            "created_at",
        ]
        read_only_fields = ["user", "balance_after", "created_at"]


class UpdateCreditBalanceSerializer(serializers.Serializer):
    """Serializer for updating a user's credit balance"""

    amount = serializers.IntegerField(required=True)
    description = serializers.CharField(max_length=255, required=False, default="")

    def validate_amount(self, value):
        """
        Validate that the amount is not zero.
        """
        if value == 0:
            raise serializers.ValidationError("Amount cannot be zero.")
        return value
