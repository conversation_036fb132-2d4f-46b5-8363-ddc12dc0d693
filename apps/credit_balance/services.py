from django.db import transaction
from django.contrib.auth import get_user_model
from .models import CreditBalance, CreditBalanceTransaction, TransactionType

User = get_user_model()


def get_or_create_user_credit_balance(user):
    """
    Get or create a credit balance for a user
    """
    credit_balance, created = CreditBalance.objects.get_or_create(user=user)
    return credit_balance


def add_credit(user, amount, description=""):
    """
    Add credit to a user's balance

    Args:
        user: User instance
        amount: Amount in points to add (positive integer)
        description: Optional description for the transaction

    Returns:
        CreditBalanceTransaction instance
    """
    if amount <= 0:
        raise ValueError("Amount must be positive for adding credit")

    with transaction.atomic():
        # Get or create user's credit balance
        credit_balance = get_or_create_user_credit_balance(user)

        # Update balance
        credit_balance.balance += amount
        credit_balance.save()

        # Record transaction
        credit_transaction = CreditBalanceTransaction.objects.create(
            user=user,
            amount=amount,
            balance_after=credit_balance.balance,
            transaction_type=TransactionType.CREDIT,
            description=description,
        )

    return credit_transaction


def deduct_credit(user, amount, description=""):
    """
    Deduct credit from a user's balance

    Args:
        user: User instance
        amount: Amount in points to deduct (positive integer)
        description: Optional description for the transaction

    Returns:
        CreditBalanceTransaction instance

    Raises:
        ValueError: If user has insufficient balance
    """
    if amount <= 0:
        raise ValueError("Amount must be positive for deducting credit")

    with transaction.atomic():
        # Get or create user's credit balance
        credit_balance = get_or_create_user_credit_balance(user)

        # Check if user has sufficient balance
        if credit_balance.balance < amount:
            raise ValueError("Insufficient credit balance")

        # Update balance
        credit_balance.balance -= amount
        credit_balance.save()

        # Record transaction
        credit_transaction = CreditBalanceTransaction.objects.create(
            user=user,
            amount=-amount,  # Negative amount for debit
            balance_after=credit_balance.balance,
            transaction_type=TransactionType.DEBIT,
            description=description,
        )

    return credit_transaction


def get_user_credit_balance(user):
    """
    Get a user's current credit balance

    Returns:
        CreditBalance instance or None if not exists
    """
    try:
        return CreditBalance.objects.get(user=user)
    except CreditBalance.DoesNotExist:
        return None


def get_user_transactions(user, limit=None):
    """
    Get a user's transaction history

    Args:
        user: User instance
        limit: Optional limit on number of transactions to return

    Returns:
        QuerySet of CreditBalanceTransaction instances
    """
    transactions = CreditBalanceTransaction.objects.filter(user=user)
    if limit:
        transactions = transactions[:limit]
    return transactions
