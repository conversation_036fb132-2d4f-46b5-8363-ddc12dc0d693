from django.shortcuts import render
import secrets
import base64
import requests
import json
from datetime import date
from django.conf import settings
from django.core.cache import cache
from django.shortcuts import redirect
from django.http import JsonResponse, HttpResponse
from django.views.decorators.http import require_http_methods
from urllib.parse import urlencode
from .models import XeroToken
from django.views.decorators.csrf import csrf_exempt
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny, IsAuthenticated
from rest_framework.response import Response
from apps.invoice_automation.invoice_processor import parse_azure_result
from .invoice_schema import validate_invoice_data, INVOICE_SCHEMA, EXAMPLE_INVOICE
from xero_python.api_client import ApiClient
from xero_python.accounting import AccountingApi
from xero_python.api_client.configuration import Configuration
from xero_python.api_client.oauth2 import OAuth2Token
from rest_framework.exceptions import ValidationError
from xero_python.accounting.models import (
    Invoices,
    Invoice,
    LineItem,
    Contact,
    LineAmountTypes,
    CurrencyCode,
    LineItemTracking,
)
from difflib import get_close_matches
from azure.core.credentials import AzureKeyCredential
from azure.ai.documentintelligence import DocumentIntelligenceClient
import os
import tempfile
from datetime import datetime, timedelta
import uuid

# Create your views here.


# parse ISO8601 date-strings
def parse_date(x):
    if isinstance(x, str):
        return date.fromisoformat(x)
    return x  # assume it's already a date


def find_matching_item(raw_name: str, xero_items: list, cutoff=0.6):
    """
    raw_name: the free-form item text from your client
    xero_items: list of InventoryItem models from Xero
    cutoff: 0–1, minimum similarity for a match
    """
    # build a lookup of Xero names → item models
    name_map = {itm.name: itm for itm in xero_items if itm.name}
    # do fuzzy match on the names list
    matches = get_close_matches(raw_name, name_map.keys(), n=1, cutoff=cutoff)
    if not matches:
        return None
    return name_map[matches[0]]


def find_matching_contact(raw_name: str, xero_contacts: list, cutoff: float = 0.6):
    """
    Fuzzy-match a free-form name to the closest Xero Contact.
    Returns the Contact model or None if no match >= cutoff.
    """
    # build name → model map
    name_map = {c.name: c for c in xero_contacts if c.name}
    # look for the single best match
    matches = get_close_matches(raw_name, name_map.keys(), n=1, cutoff=cutoff)
    if not matches:
        return None
    return name_map[matches[0]]


@api_view(["GET"])
@permission_classes([IsAuthenticated])  # Require authentication
def connect_to_xero(request):
    # Generate a random state value
    state = secrets.token_urlsafe(32)

    # Store state in both session and cache as backup
    request.session["xero_state"] = state
    request.session["xero_user_id"] = (
        request.user.id
    )  # Store user ID for verification in callback
    request.session.modified = True
    cache.set(f"xero_state_{state}", state, timeout=300)  # 5 minute timeout
    cache.set(
        f"xero_user_id_{state}", request.user.id, timeout=300
    )  # Store user ID in cache too

    # Debug information
    print(f"DEBUG - Generated state: {state}")
    print(f"DEBUG - Session key: {request.session.session_key}")
    print(f"DEBUG - Session contains: {dict(request.session.items())}")
    print(f"DEBUG - User ID: {request.user.id}")

    # Define the required scopes for CRUD operations
    scopes = [
        "openid",
        "profile",
        "email",
        "accounting.settings",
        "accounting.transactions",
        "accounting.contacts",
        "accounting.journals.read",
        "offline_access",
        "accounting.attachments",
    ]
    scope_string = " ".join(scopes)

    print(f"DEBUG - Using scopes: {scope_string}")

    # Define the authorization URL parameters
    params = {
        "response_type": "code",
        "client_id": settings.XERO_CLIENT_ID,
        "redirect_uri": settings.XERO_REDIRECT_URI,
        "scope": scope_string,
        "state": state,
    }

    # Construct the authorization URL
    authorization_url = (
        f"https://login.xero.com/identity/connect/authorize?{urlencode(params)}"
    )

    # Return JSON response for frontend to handle
    return JsonResponse({"authorization_url": authorization_url})


@api_view(["GET"])
@permission_classes([AllowAny])  # Allow any for callback
def xero_callback(request):
    # Get state from both session and cache
    received_state = request.GET.get("state")
    stored_state = request.session.get("xero_state")
    cached_state = cache.get(f"xero_state_{received_state}") if received_state else None

    # Get user ID from session and cache
    user_id = request.session.get("xero_user_id")
    cached_user_id = (
        cache.get(f"xero_user_id_{received_state}") if received_state else None
    )

    # Debug information
    print(f"DEBUG - Session key: {request.session.session_key}")
    print(f"DEBUG - Session keys: {list(request.session.keys())}")
    print(f"DEBUG - Session items: {dict(request.session.items())}")
    print(f"DEBUG - Received state: {received_state}")
    print(f"DEBUG - Stored state: {stored_state}")
    print(f"DEBUG - Cached state: {cached_state}")
    print(f"DEBUG - User ID from session: {user_id}")
    print(f"DEBUG - User ID from cache: {cached_user_id}")

    # Validate state using both session and cache
    if not received_state:
        return HttpResponse(
            "No state received in callback. Please try connecting again.", status=400
        )

    if not (stored_state or cached_state):
        return HttpResponse(
            "No state found in session or cache. Please try connecting again.",
            status=400,
        )

    if received_state != (stored_state or cached_state):
        return HttpResponse("State mismatch. Please try connecting again.", status=400)

    # Get the user ID (from session or cache)
    user_id = user_id or cached_user_id
    if not user_id:
        return HttpResponse(
            "User ID not found. Please login and try again.", status=400
        )

    # Get the user model
    from django.contrib.auth import get_user_model

    User = get_user_model()

    try:
        user = User.objects.get(id=user_id)
    except User.DoesNotExist:
        return HttpResponse("User not found. Please login and try again.", status=400)

    # Clear the state from both session and cache
    if "xero_state" in request.session:
        del request.session["xero_state"]
    if "xero_user_id" in request.session:
        del request.session["xero_user_id"]
    cache.delete(f"xero_state_{received_state}")
    cache.delete(f"xero_user_id_{received_state}")

    code = request.GET.get("code")
    if not code:
        return HttpResponse("Authorization code not received.", status=400)

    try:
        # Manual OAuth2 token exchange
        token_url = "https://identity.xero.com/connect/token"
        redirect_uri = settings.XERO_REDIRECT_URI

        # Prepare auth header with client_id and client_secret
        client_id = settings.XERO_CLIENT_ID
        client_secret = settings.XERO_CLIENT_SECRET
        auth_string = f"{client_id}:{client_secret}"
        auth_bytes = auth_string.encode("utf-8")
        auth_b64 = base64.b64encode(auth_bytes).decode("utf-8")

        # Prepare headers and body
        headers = {
            "Authorization": f"Basic {auth_b64}",
            "Content-Type": "application/x-www-form-urlencoded",
        }

        body = {
            "grant_type": "authorization_code",
            "code": code,
            "redirect_uri": redirect_uri,
        }

        # Make token request
        response = requests.post(token_url, headers=headers, data=body)

        if response.status_code != 200:
            print(
                f"DEBUG - Token exchange failed with status {response.status_code}: {response.text}"
            )
            return HttpResponse(
                f"Failed to exchange code for token: {response.text}", status=500
            )

        # Get token data
        token_data = response.json()
        print(f"DEBUG - Received token data with keys: {token_data.keys()}")

        # Store token in database for this specific user
        XeroToken.store_token(token_data, user=user)

        print("DEBUG - Token stored successfully for user, redirecting to frontend")
        # Redirect to frontend home page
        return redirect(f"{settings.FRONTEND_URL}/xero/home")
    except Exception as e:
        print(f"DEBUG - Token error: {str(e)}")
        return HttpResponse(f"Error getting token: {str(e)}", status=500)


@api_view(["GET"])
@permission_classes([IsAuthenticated])  # Require authentication
def get_xero_token(request):
    """API endpoint to get the current Xero token for the authenticated user"""
    try:
        token = XeroToken.objects.filter(user=request.user).first()
        if not token:
            return JsonResponse({"error": "No token found for this user"}, status=404)

        # Check if token is expired
        if token.is_expired:
            # Try to refresh the token
            try:
                new_token = refresh_xero_token(token)
                if new_token:
                    token = new_token
                else:
                    return JsonResponse(
                        {"error": "Token expired and refresh failed"}, status=401
                    )
            except Exception as e:
                return JsonResponse(
                    {"error": f"Token refresh failed: {str(e)}"}, status=401
                )

        return JsonResponse(
            {
                "access_token": token.access_token,
                "token_type": token.token_type,
                "expires_at": token.expires_at.isoformat(),
                "scope": token.scope,
            }
        )
    except Exception as e:
        return JsonResponse({"error": str(e)}, status=500)


def refresh_xero_token(token):
    """Helper function to refresh an expired token"""
    token_url = "https://identity.xero.com/connect/token"

    # Prepare auth header
    client_id = settings.XERO_CLIENT_ID
    client_secret = settings.XERO_CLIENT_SECRET
    auth_string = f"{client_id}:{client_secret}"
    auth_bytes = auth_string.encode("utf-8")
    auth_b64 = base64.b64encode(auth_bytes).decode("utf-8")

    headers = {
        "Authorization": f"Basic {auth_b64}",
        "Content-Type": "application/x-www-form-urlencoded",
    }

    body = {"grant_type": "refresh_token", "refresh_token": token.refresh_token}

    response = requests.post(token_url, headers=headers, data=body)

    if response.status_code == 200:
        token_data = response.json()
        # Pass the user when storing the refreshed token
        return XeroToken.store_token(token_data, user=token.user)

    return None


@api_view(["GET"])
@permission_classes([IsAuthenticated])  # Require authentication
def verify_xero_connection(request):
    """Verify Xero connection by checking token and making a test API call"""
    try:
        # Get the latest token for the current user
        token = XeroToken.objects.filter(user=request.user).first()
        if not token:
            # Return 200 with is_connected=False when no token found
            # This allows the frontend to show the connect button
            return JsonResponse(
                {"is_connected": False, "reason": "No token found"}, status=200
            )

        # Check if token is expired
        if token.is_expired:
            # Try to refresh the token
            try:
                token = refresh_xero_token(token)
                if not token:
                    return JsonResponse(
                        {
                            "is_connected": False,
                            "reason": "Token expired and refresh failed",
                        },
                        status=200,  # Return 200 instead of 401
                    )
            except Exception as e:
                return JsonResponse(
                    {
                        "is_connected": False,
                        "reason": f"Token refresh failed: {str(e)}",
                    },
                    status=200,  # Return 200 instead of 401
                )

        # Prepare headers consistently with other functions
        headers = {
            "Authorization": f"Bearer {token.access_token}",
            "Accept": "application/json",
        }

        # Get tenant ID (first organization)
        tenant_response = requests.get(
            "https://api.xero.com/connections", headers=headers
        )
        if tenant_response.status_code != 200:
            return JsonResponse(
                {
                    "is_connected": False,
                    "reason": f"Failed to get Xero tenants: {tenant_response.status_code}",
                },
                status=200,
            )

        tenants = tenant_response.json()
        if not tenants:
            return JsonResponse(
                {"is_connected": False, "reason": "No Xero organizations found"},
                status=200,
            )

        tenant_id = tenants[0]["tenantId"]

        # Make test API call to Xero
        xero_response = requests.get(
            "https://api.xero.com/api.xro/2.0/Organisation",
            headers={**headers, "Xero-tenant-id": tenant_id},
        )

        if xero_response.ok:
            org_data = xero_response.json()
            return JsonResponse(
                {
                    "is_connected": True,
                    "organisation": org_data.get("Organisations", [{}])[
                        0
                    ],  # Return first org's data
                }
            )
        else:
            return JsonResponse(
                {
                    "is_connected": False,
                    "reason": f"Xero API call failed: {xero_response.status_code}",
                },
                status=200,  # Return 200 instead of 401
            )

    except Exception as e:
        return JsonResponse(
            {"is_connected": False, "reason": str(e)}, status=200
        )  # Return 200 instead of 500


@api_view(["GET"])
@permission_classes([IsAuthenticated])  # Require authentication
def get_xero_invoices(request):
    """Get invoices from Xero for the authenticated user"""
    try:
        # Get the latest token for the current user
        token = XeroToken.objects.filter(user=request.user).first()
        if not token:
            return JsonResponse(
                {"error": "No Xero token found. Please connect to Xero first."},
                status=404,
            )

        # Check if token is expired
        if token.is_expired:
            # Try to refresh the token
            token = refresh_xero_token(token)
            if not token:
                return JsonResponse(
                    {"error": "Token expired and refresh failed"}, status=401
                )

        # Make API call to Xero to get invoices
        headers = {
            "Authorization": f"Bearer {token.access_token}",
            "Accept": "application/json",
        }

        # Get tenant ID (first organization)
        tenant_response = requests.get(
            "https://api.xero.com/connections", headers=headers
        )
        if tenant_response.status_code != 200:
            return JsonResponse({"error": "Failed to get Xero tenants"}, status=500)

        tenants = tenant_response.json()
        if not tenants:
            return JsonResponse({"error": "No Xero organizations found"}, status=404)

        tenant_id = tenants[0]["tenantId"]

        # Get invoices from Xero
        invoices_response = requests.get(
            f"https://api.xero.com/api.xro/2.0/Invoices",
            headers={**headers, "Xero-tenant-id": tenant_id},
        )

        if invoices_response.status_code != 200:
            return JsonResponse(
                {"error": f"Failed to get invoices: {invoices_response.text}"},
                status=500,
            )

        return JsonResponse({"invoices": invoices_response.json().get("Invoices", [])})
    except Exception as e:
        return JsonResponse({"error": str(e)}, status=500)


@api_view(["GET"])
@permission_classes([IsAuthenticated])  # Require authentication
def get_xero_contacts(request):
    """Get contacts from Xero for the authenticated user"""
    try:
        # Get the latest token for the current user
        token = XeroToken.objects.filter(user=request.user).first()
        if not token:
            return JsonResponse(
                {"error": "No Xero token found. Please connect to Xero first."},
                status=404,
            )

        # Check if token is expired
        if token.is_expired:
            # Try to refresh the token
            token = refresh_xero_token(token)
            if not token:
                return JsonResponse(
                    {"error": "Token expired and refresh failed"}, status=401
                )

        # Make API call to Xero to get contacts
        headers = {
            "Authorization": f"Bearer {token.access_token}",
            "Accept": "application/json",
        }

        # Get tenant ID (first organization)
        tenant_response = requests.get(
            "https://api.xero.com/connections", headers=headers
        )
        if tenant_response.status_code != 200:
            return JsonResponse({"error": "Failed to get Xero tenants"}, status=500)

        tenants = tenant_response.json()
        if not tenants:
            return JsonResponse({"error": "No Xero organizations found"}, status=404)

        tenant_id = tenants[0]["tenantId"]

        # Get contacts from Xero
        contacts_response = requests.get(
            f"https://api.xero.com/api.xro/2.0/Contacts",
            headers={**headers, "Xero-tenant-id": tenant_id},
        )

        if contacts_response.status_code != 200:
            return JsonResponse(
                {"error": f"Failed to get contacts: {contacts_response.text}"},
                status=500,
            )

        return JsonResponse({"contacts": contacts_response.json().get("Contacts", [])})
    except Exception as e:
        return JsonResponse({"error": str(e)}, status=500)


@api_view(["GET"])
@permission_classes([IsAuthenticated])  # Require authentication
def get_xero_accounts(request):
    """Get accounts from Xero for the authenticated user"""
    try:
        # Get the latest token for the current user
        token = XeroToken.objects.filter(user=request.user).first()
        if not token:
            return JsonResponse(
                {"error": "No Xero token found. Please connect to Xero first."},
                status=404,
            )

        # Check if token is expired
        if token.is_expired:
            # Try to refresh the token
            token = refresh_xero_token(token)
            if not token:
                return JsonResponse(
                    {"error": "Token expired and refresh failed"}, status=401
                )

        # Make API call to Xero to get accounts
        headers = {
            "Authorization": f"Bearer {token.access_token}",
            "Accept": "application/json",
        }

        # Get tenant ID (first organization)
        tenant_response = requests.get(
            "https://api.xero.com/connections", headers=headers
        )
        if tenant_response.status_code != 200:
            return JsonResponse({"error": "Failed to get Xero tenants"}, status=500)

        tenants = tenant_response.json()
        if not tenants:
            return JsonResponse({"error": "No Xero organizations found"}, status=404)

        tenant_id = tenants[0]["tenantId"]

        # Get accounts from Xero
        accounts_response = requests.get(
            f"https://api.xero.com/api.xro/2.0/Accounts",
            headers={**headers, "Xero-tenant-id": tenant_id},
        )

        if accounts_response.status_code != 200:
            return JsonResponse(
                {"error": f"Failed to get accounts: {accounts_response.text}"},
                status=500,
            )

        return JsonResponse({"accounts": accounts_response.json().get("Accounts", [])})
    except Exception as e:
        return JsonResponse({"error": str(e)}, status=500)


@api_view(["POST"])
@permission_classes([IsAuthenticated])  # Require authentication
def disconnect_xero(request):
    """Disconnect from Xero by deleting the token for the current user"""
    try:
        token = XeroToken.objects.filter(user=request.user).first()
        if token:
            token.delete()
            return JsonResponse(
                {"status": "success", "message": "Disconnected from Xero"}
            )
        else:
            return JsonResponse(
                {
                    "status": "success",
                    "message": "No Xero connection found for this user",
                }
            )
    except Exception as e:
        return JsonResponse({"status": "error", "message": str(e)}, status=200)


@api_view(["GET"])
@permission_classes([IsAuthenticated])  # Require authentication
def get_xero_tax_rates(request):
    """Get tax rates from Xero for the authenticated user"""
    try:
        # Get the latest token for the current user
        token = XeroToken.objects.filter(user=request.user).first()
        if not token:
            return JsonResponse(
                {"error": "No Xero token found. Please connect to Xero first."},
                status=404,
            )

        # Check if token is expired
        if token.is_expired:
            # Try to refresh the token
            token = refresh_xero_token(token)
            if not token:
                return JsonResponse(
                    {"error": "Token expired and refresh failed"}, status=401
                )

        # Make API call to Xero to get tax rates
        headers = {
            "Authorization": f"Bearer {token.access_token}",
            "Accept": "application/json",
        }

        # Get tenant ID (first organization)
        tenant_response = requests.get(
            "https://api.xero.com/connections", headers=headers
        )
        if tenant_response.status_code != 200:
            return JsonResponse({"error": "Failed to get Xero tenants"}, status=500)

        tenants = tenant_response.json()
        if not tenants:
            return JsonResponse({"error": "No Xero organizations found"}, status=404)

        tenant_id = tenants[0]["tenantId"]

        # Get tax rates from Xero
        tax_rates_response = requests.get(
            f"https://api.xero.com/api.xro/2.0/TaxRates",
            headers={**headers, "Xero-tenant-id": tenant_id},
        )

        if tax_rates_response.status_code != 200:
            return JsonResponse(
                {"error": f"Failed to get tax rates: {tax_rates_response.text}"},
                status=500,
            )

        return JsonResponse(
            {"tax_rates": tax_rates_response.json().get("TaxRates", [])}
        )
    except Exception as e:
        return JsonResponse({"error": str(e)}, status=500)


@api_view(["GET"])
@permission_classes([IsAuthenticated])  # Require authentication
def get_xero_invoice_types(request):
    """Get invoice types from Xero"""
    try:
        # Xero invoice types are fixed and don't require an API call
        # Common invoice types in Xero
        invoice_types = [
            {"Type": "ACCREC", "Description": "Accounts Receivable (Sales)"},
            {"Type": "ACCPAY", "Description": "Accounts Payable (Bills)"},
        ]

        return JsonResponse({"invoice_types": invoice_types})
    except Exception as e:
        return JsonResponse({"error": str(e)}, status=500)


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def scan_invoices(request):
    """Scan and analyze PDF invoices using Azure Form Recognizer"""
    try:
        # Check if files were uploaded
        if len(request.FILES) == 0:
            return JsonResponse({"error": "No files were uploaded"}, status=400)

        # Get additional parameters from request data
        account_code = request.data.get("account_code")
        tax_type = request.data.get("tax_type")
        invoice_type = request.data.get("invoice_type")

        # Validate required fields
        if not account_code:
            return JsonResponse({"error": "Account code is required"}, status=400)
        if not tax_type:
            return JsonResponse({"error": "Tax type is required"}, status=400)
        if not invoice_type:
            return JsonResponse({"error": "Invoice type is required"}, status=400)

        # Get Azure Document Intelligence credentials from environment variables
        endpoint = os.getenv("AZURE_DOCUMENT_INTELLIGENCE_ENDPOINT")
        key = os.getenv("AZURE_DOCUMENT_INTELLIGENCE_KEY")

        if not endpoint or not key:
            return JsonResponse(
                {"error": "Azure Document Intelligence credentials not configured"},
                status=500,
            )

        # Initialize the Document Intelligence client
        document_intelligence_client = DocumentIntelligenceClient(
            endpoint=endpoint, credential=AzureKeyCredential(key)
        )

        results = {}
        invoice_data_for_creation = []

        # Create saved_invoices directory if it doesn't exist
        saved_invoices_dir = os.path.join(settings.BASE_DIR, "saved_invoices")
        os.makedirs(saved_invoices_dir, exist_ok=True)

        # Process each uploaded file
        for file_key, file in request.FILES.items():
            # Create a temporary file to store the uploaded PDF
            with tempfile.NamedTemporaryFile(delete=False, suffix=".pdf") as temp_file:
                temp_file_path = temp_file.name
                for chunk in file.chunks():
                    temp_file.write(chunk)

            try:
                # Analyze the invoice using Azure Form Recognizer
                with open(temp_file_path, "rb") as f:
                    poller = document_intelligence_client.begin_analyze_document(
                        "prebuilt-invoice", f
                    )
                result = poller.result()

                # Convert the result to a serializable format
                invoice_data = {
                    "model_id": getattr(result, "model_id", None),
                    "content": getattr(result, "content", None),
                    "pages": len(getattr(result, "pages", [])),
                    "fields": {},
                }

                result = parse_azure_result(result)
                if result:
                    invoice_info_list, invoice_items_list, other_tables_list = result
                else:
                    invoice_info_list, invoice_items_list, other_tables_list = (
                        [],
                        [],
                        [],
                    )

                print(f"Invoice info list: {invoice_info_list}")
                print(f"Invoice items list: {invoice_items_list}")
                print(f"Other tables list: {other_tables_list}")

                # Extract invoice number from invoice_info_list
                invoice_number = None
                for info in invoice_info_list:
                    if "InvoiceId" in info:
                        invoice_number = info["InvoiceId"]
                        break

                # Save the invoice file with invoice number if available
                if invoice_number:
                    saved_invoice_path = os.path.join(
                        saved_invoices_dir, f"{invoice_number}.pdf"
                    )
                    with open(temp_file_path, "rb") as src, open(
                        saved_invoice_path, "wb"
                    ) as dst:
                        dst.write(src.read())

                # Extract fields from the invoice
                for name, field in getattr(result, "fields", {}).items():
                    if field is not None:
                        if hasattr(field, "value") and field.value is not None:
                            invoice_data["fields"][name] = {
                                "value": field.value,
                                "confidence": field.confidence,
                            }
                        elif hasattr(field, "values") and field.values:
                            invoice_data["fields"][name] = {
                                "values": [val.value for val in field.values],
                                "confidence": field.confidence,
                            }

                results[file_key] = invoice_data

                # Prepare data for invoice creation
                prepared_invoice = prepare_invoice_data(
                    invoice_info_list,
                    invoice_items_list,
                    account_code,
                    tax_type,
                    invoice_type,
                )

                if prepared_invoice:
                    invoice_data_for_creation.append(prepared_invoice)

            finally:
                # Clean up the temporary file
                os.unlink(temp_file_path)

        return JsonResponse(
            {"results": results, "invoice_data": invoice_data_for_creation}
        )

    except Exception as e:
        import traceback

        return JsonResponse(
            {"error": str(e), "traceback": traceback.format_exc()}, status=500
        )


def prepare_invoice_data(
    invoice_info_list, invoice_items_list, account_code, tax_type, invoice_type
):
    """
    Prepare invoice data for creating an invoice in Xero
    """
    print(f"Preparing invoice data for {invoice_info_list}")
    try:
        # Extract customer name and invoice date from invoice_info_list
        customer_name = None
        invoice_date = None
        invoice_number = None

        for item in invoice_info_list:
            if "CustomerName" in item:
                customer_name = item["CustomerName"]
            if "InvoiceDate" in item:
                invoice_date = item["InvoiceDate"]
            if "InvoiceId" in item:
                invoice_number = item["InvoiceId"]

        # If customer name or invoice date is missing, try to find them in other fields
        if not customer_name:
            for item in invoice_info_list:
                if "VendorName" in item:
                    customer_name = item["VendorName"]
                if "BillTo" in item:
                    customer_name = item["BillTo"]

        if not invoice_date:
            for item in invoice_info_list:
                if "InvoiceDate" in item:
                    invoice_date = item["InvoiceDate"]
                if "Date" in item:
                    invoice_date = item["Date"]

        # Parse invoice date if found
        if invoice_date:
            try:
                # Handle different date formats
                for fmt in ["%Y-%m-%d", "%d/%m/%Y", "%m/%d/%Y", "%d-%m-%Y", "%m-%d-%Y"]:
                    try:
                        invoice_date = datetime.strptime(invoice_date, fmt).strftime(
                            "%Y-%m-%d"
                        )
                        break
                    except ValueError:
                        continue
            except Exception:
                # If date parsing fails
                invoice_date = datetime.now().strftime("%Y-%m-%d")
                # TODO: Log here
        else:
            # If no date found, use today's date
            invoice_date = datetime.now().strftime("%Y-%m-%d")
            # TODO: Log here

        # TODO: Ask user for default due date time from the issue date and extract due date here?
        if "DueDate" in invoice_info_list:
            due_date = invoice_info_list["DueDate"]
        else:
            due_date = None
            # due_date = (
            #     datetime.strptime(invoice_date, "%Y-%m-%d") + timedelta(days=10)
            # ).strftime("%Y-%m-%d")

        # Prepare line items
        line_items = []
        for item in invoice_items_list:
            description = item.get("Description", "")
            quantity = item.get("Quantity", "1")
            unit_price = item.get("UnitPrice", "0")
            item_code = item.get("ProductCode", "")

            # Clean up quantity and unit price
            try:
                quantity = quantity.replace(",", "")
                # Remove currency symbols and commas
                unit_price = (
                    unit_price.replace("$", "")
                    .replace("€", "")
                    .replace("£", "")
                    .replace(",", "")
                )

                # Convert to numeric values
                quantity = float(quantity)
                unit_price = float(unit_price)
            except (ValueError, AttributeError):
                # Default values if conversion fails
                quantity = 1.0
                unit_price = 0.0

            line_item = {
                "description": description,
                "quantity": quantity,
                "unit_amount": unit_price,
                "account_code": account_code,
                "tax_type": tax_type,
                "item_code": item_code,
            }

            line_items.append(line_item)

        # Prepare the complete invoice data
        invoice_data = {
            "type": invoice_type,
            "contact": {"name": customer_name or "Unknown Customer"},
            "date": invoice_date,
            "due_date": due_date,
            "line_items": line_items,
            "invoice_number": invoice_number if invoice_number else None,
            "reference": str(customer_name) + f" - {datetime.now().strftime('%Y%m%d')}",
            "status": "DRAFT",
            "line_amount_types": "Exclusive",
        }

        return invoice_data

    except Exception as e:
        print(f"Error preparing invoice data: {str(e)}")
        return None


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def create_invoice(request):
    """Create a new invoice in Xero using the SDK"""
    try:
        # Get the latest token for the current user
        token = XeroToken.objects.filter(user=request.user).first()
        if not token:
            return JsonResponse(
                {"error": "No Xero token found. Please connect to Xero first."},
                status=404,
            )

        # Check if token is expired
        if token.is_expired:
            # Try to refresh the token
            token = refresh_xero_token(token)
            if not token:
                return JsonResponse(
                    {"error": "Token expired and refresh failed"}, status=401
                )

        # Get invoice data from request
        invoice_data = request.data

        # Convert string LineAmountTypes to enum if present
        if "line_amount_types" in invoice_data:
            line_amount_type = invoice_data["line_amount_types"]
            # Convert string to enum
            if line_amount_type == "Exclusive":
                invoice_data["line_amount_types"] = LineAmountTypes.EXCLUSIVE
            elif line_amount_type == "Inclusive":
                invoice_data["line_amount_types"] = LineAmountTypes.INCLUSIVE
            elif line_amount_type == "NoTax":
                invoice_data["line_amount_types"] = LineAmountTypes.NOTAX

        # Validate invoice data against schema
        is_valid, errors = validate_invoice_data(invoice_data)
        if not is_valid:
            return JsonResponse(
                {"error": "Invalid invoice data", "details": errors},
                status=400,
            )

        # Configure API client for Xero SDK properly
        config = Configuration(
            oauth2_token=OAuth2Token(
                client_id=settings.XERO_CLIENT_ID,
                client_secret=settings.XERO_CLIENT_SECRET,
            )
        )

        # Calculate expires_in from expires_at if available
        expires_in = 1800  # Default 30 minutes
        if hasattr(token, "expires_at"):
            import datetime
            from django.utils import timezone

            now = timezone.now()
            if token.expires_at > now:
                # Convert timedelta to seconds
                expires_in = int((token.expires_at - now).total_seconds())

        api_client = ApiClient(
            config,
            oauth2_token_saver=lambda x: None,
            oauth2_token_getter=lambda: {
                "access_token": token.access_token,
                "refresh_token": token.refresh_token,
                "scope": token.scope,
                "expires_in": expires_in,
                "token_type": token.token_type,
            },
        )

        # Get tenant ID (first organization)
        headers = {
            "Authorization": f"Bearer {token.access_token}",
            "Accept": "application/json",
        }
        tenant_response = requests.get(
            "https://api.xero.com/connections", headers=headers
        )
        if tenant_response.status_code != 200:
            return JsonResponse({"error": "Failed to get Xero tenants"}, status=500)

        tenants = tenant_response.json()
        if not tenants:
            return JsonResponse({"error": "No Xero organizations found"}, status=404)

        tenant_id = tenants[0]["tenantId"]

        # Initialize Accounting API
        accounting_api = AccountingApi(api_client)

        try:
            raw = invoice_data

            # Use contact_name to find contact_id
            xero_resp = accounting_api.get_contacts(
                xero_tenant_id=tenant_id,
                summary_only="True",
                where='ContactStatus=="ACTIVE"',
                order="Name ASC",
            )

            xero_contacts = xero_resp.contacts

            # Resolve Contact: prefer contact_id, else fuzzy-match contact_name
            if raw["contact"].get("contact_id"):
                contact_model = Contact(contact_id=raw["contact"]["contact_id"])
            else:
                name = raw["contact"].get("name")
                if not name:
                    raise ValidationError(
                        {"contact": "Must supply 'contact_id' or 'contact_name'."}
                    )
                matched = find_matching_contact(name, xero_contacts, cutoff=0.65)
                if not matched:
                    raise ValidationError(
                        {"contact": f"Contact '{name}' not found in Xero."}
                    )
                contact_model = Contact(contact_id=matched.contact_id)

            # Build line_items, resolving `item` → Xero item
            line_items = []
            for idx, li in enumerate(raw.get("line_items", []), start=1):
                # if there is an item_code, use it
                code = li.get("item_code")
                if code:
                    matched_code = code
                else:
                    # Pull all inventory items from Xero
                    xero_resp = accounting_api.get_items(xero_tenant_id=tenant_id)
                    xero_items = xero_resp.items  # a list of InventoryItem models

                    # else there is a free-form 'item' name: fuzzy-match it
                    raw_name = li.get("item")
                    if not raw_name:
                        raise ValidationError(
                            {
                                "line_items": {
                                    idx: "Must supply either 'item_code' or free-form 'item'."
                                }
                            }
                        )
                    matched = find_matching_item(raw_name, xero_items, cutoff=0.65)
                    if not matched:
                        raise ValidationError(
                            {
                                "line_items": {
                                    idx: f"Item '{raw_name}' not available in inventory."
                                }
                            }
                        )
                    matched_code = matched.code  # from the InventoryItem model

                qty = float(li["quantity"])
                ua = float(li["unit_amount"])
                line_items.append(
                    LineItem(
                        description=li.get("description"),
                        quantity=qty,
                        unit_amount=ua,
                        account_code=li.get("account_code"),
                        tax_type=li.get("tax_type"),
                        item_code=matched_code,
                    )
                )

            inv_kwargs = {
                "type": raw["type"],
                "contact": contact_model,
                # convert strings → date:
                "date": parse_date(raw["date"]),
                "due_date": parse_date(raw["due_date"]),
                "line_amount_types": raw["line_amount_types"],
                # only include the other fields you really need:
                "invoice_number": raw.get("invoice_number"),
                "reference": raw.get("reference"),
                "currency_code": raw.get("currency_code"),
                "currency_rate": float(raw.get("currency_rate", 1)),
                "status": raw.get("status"),
                "line_items": line_items,
            }

            xero_invoice = Invoice(**inv_kwargs)
            invoices_wrapper = Invoices(invoices=[xero_invoice])

            # Now pass a list of model objects:
            response = accounting_api.create_invoices(
                xero_tenant_id=tenant_id,
                invoices=invoices_wrapper,
            )

            # Handle different response formats
            if hasattr(response, "invoices") and response.invoices:
                invoice = response.invoices[0].to_dict() if response.invoices else {}

                # Get the invoice number from the request data
                invoice_number = raw.get("invoice_number")

                # Check if there's a saved invoice file
                if invoice_number:
                    saved_invoice_path = os.path.join(
                        settings.BASE_DIR, "saved_invoices", f"{invoice_number}.pdf"
                    )
                    if os.path.exists(saved_invoice_path):
                        try:
                            # Read the saved invoice file
                            with open(saved_invoice_path, "rb") as f:
                                file_content = f.read()

                            # Create a unique idempotency key
                            idempotency_key = (
                                f"attach-pdf-{invoice_number}-{uuid.uuid4()}"
                            )

                            # Attach the invoice file to the Xero invoice
                            accounting_api.create_invoice_attachment_by_file_name(
                                xero_tenant_id=tenant_id,
                                invoice_id=invoice.get("invoice_id"),
                                file_name=f"{invoice_number}.pdf",
                                body=file_content,
                                include_online=True,
                                idempotency_key=idempotency_key,
                            )
                            print(f"Invoice file attached: {invoice_number}")
                        except Exception as e:
                            print(f"Error attaching invoice file: {str(e)}")
                            # Continue even if attachment fails
                            pass
            else:
                # Extract data from response in the safest way possible
                try:
                    # Convert to dict if possible
                    if hasattr(response, "to_dict"):
                        resp_dict = response.to_dict()
                        if "invoices" in resp_dict and resp_dict["invoices"]:
                            invoice = resp_dict["invoices"][0]
                        else:
                            invoice = resp_dict
                    else:
                        # Fallback
                        invoice = {
                            "message": "Invoice created, but details could not be retrieved"
                        }
                except Exception:
                    # Final fallback
                    invoice = {"message": "Invoice created successfully"}
            return JsonResponse(
                {"status": "success", "invoice": invoice},
                json_dumps_params={"default": str},
            )

        except Exception as e:
            # Get more detailed error message if available
            error_message = str(e)
            if hasattr(e, "reason"):
                error_message = str(e.reason)
            if hasattr(e, "body") and e.body:
                try:
                    error_details = json.loads(e.body)
                    if "Elements" in error_details and error_details["Elements"]:
                        validation_errors = error_details["Elements"][0].get(
                            "ValidationErrors", []
                        )
                        if validation_errors:
                            error_message = validation_errors[0].get("Message", str(e))
                    elif "Message" in error_details:
                        error_message = error_details["Message"]
                except:
                    pass  # Use original error message if parsing fails

            return JsonResponse(
                {"error": f"Failed to create invoice in Xero: {error_message}"},
                status=500,
            )

    except Exception as e:
        import traceback

        return JsonResponse(
            {"error": str(e), "traceback": traceback.format_exc()},
            status=500,
        )


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def get_invoice_schema(request):
    """Get the schema for creating an invoice"""
    return JsonResponse({"schema": INVOICE_SCHEMA, "example": EXAMPLE_INVOICE})
