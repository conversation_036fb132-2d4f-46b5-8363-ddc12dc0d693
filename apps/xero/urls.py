from django.urls import path
from . import views

urlpatterns = [
    path("connect/", views.connect_to_xero, name="xero_connect"),
    path("callback/", views.xero_callback, name="xero_callback"),
    path("token/", views.get_xero_token, name="get_xero_token"),
    path("verify/", views.verify_xero_connection, name="verify_xero_connection"),
    path("invoices/", views.get_xero_invoices, name="get_xero_invoices"),
    path("contacts/", views.get_xero_contacts, name="get_xero_contacts"),
    path("accounts/", views.get_xero_accounts, name="get_xero_accounts"),
    path("tax-rates/", views.get_xero_tax_rates, name="get_xero_tax_rates"),
    path("invoice-types/", views.get_xero_invoice_types, name="get_xero_invoice_types"),
    path("disconnect/", views.disconnect_xero, name="disconnect_xero"),
    path("scan-invoices/", views.scan_invoices, name="scan_invoices"),
    path("create-invoice/", views.create_invoice, name="create_invoice"),
    path("invoice-schema/", views.get_invoice_schema, name="get_invoice_schema"),
]
