"""
Schema definition for Xero invoices.
This file defines the structure and validation rules for creating Xero invoices.
"""

# Invoice schema based on Xero API documentation
INVOICE_SCHEMA = {
    # Required fields
    "type": {
        "type": "string",
        "required": True,
        "description": "Type of invoice - ACCPAY (bill) or ACCREC (sales invoice)",
        "choices": ["ACCPAY", "ACCREC"],
    },
    "contact": {
        "type": "object",
        "required": True,
        "description": "Contact information for the invoice",
        "schema": {
            "contact_id": {
                "type": "string",
                "description": "Xero identifier for contact",
                "required": False,
            },
            "name": {
                "type": "string",
                "description": "Full name of contact/organization",
                "required": False,
            },
            # Only one of contact_id or name is required
        },
    },
    "line_items": {
        "type": "array",
        "required": True,
        "description": "Line items for the invoice",
        "schema": {
            "description": {
                "type": "string",
                "description": "Description of the line item",
                "required": True,
            },
            "quantity": {
                "type": "number",
                "description": "Quantity of the line item",
                "required": True,
            },
            "unit_amount": {
                "type": "number",
                "description": "Unit price of the line item",
                "required": True,
            },
            "account_code": {
                "type": "string",
                "description": "Account code for the line item",
                "required": False,
            },
            "item_code": {
                "type": "string",
                "description": "Code for the line item",
                "required": False,
            },
            "tax_type": {
                "type": "string",
                "description": "Tax type for the line item",
                "required": False,
            },
            "line_amount": {
                "type": "number",
                "description": "Line amount (overrides quantity * unit_amount if provided)",
                "required": False,
            },
            "tracking": {
                "type": "array",
                "description": "Tracking categories for the line item",
                "required": False,
            },
        },
    },
    # Optional fields
    "date": {
        "type": "string",
        "description": "Date invoice was issued (YYYY-MM-DD format)",
        "required": False,
    },
    "due_date": {
        "type": "string",
        "description": "Date invoice is due (YYYY-MM-DD format)",
        "required": False,
    },
    "invoice_number": {
        "type": "string",
        "description": "Unique alpha numeric code identifying invoice (when missing will auto-generate from your settings)",
        "required": False,
    },
    "reference": {
        "type": "string",
        "description": "Additional reference number",
        "required": False,
    },
    "branding_theme_id": {
        "type": "string",
        "description": "Xero generated unique identifier for branding theme",
        "required": False,
    },
    "url": {
        "type": "string",
        "description": "URL link to a source document",
        "required": False,
    },
    "currency_code": {
        "type": "string",
        "description": "Currency code for the invoice (e.g., USD, GBP)",
        "required": False,
    },
    "status": {
        "type": "string",
        "description": "Status of the invoice",
        "required": False,
        "choices": ["DRAFT", "SUBMITTED", "AUTHORISED"],
    },
    "line_amount_types": {
        "type": "string",
        "description": "Line amount type",
        "required": False,
        "choices": [
            "Exclusive",
            "Inclusive",
            "NoTax",
            "EXCLUSIVE",
            "INCLUSIVE",
            "NOTAX",
        ],
    },
    "sub_total": {
        "type": "number",
        "description": "Total of invoice excluding taxes",
        "required": False,
    },
    "total_tax": {
        "type": "number",
        "description": "Total tax on invoice",
        "required": False,
    },
    "total": {
        "type": "number",
        "description": "Total of invoice including taxes",
        "required": False,
    },
    "sent_to_contact": {
        "type": "boolean",
        "description": "Set as true if invoice has been sent to contact",
        "required": False,
    },
}

# Example invoice data for reference
EXAMPLE_INVOICE = {
    "type": "ACCREC",
    "contact": {"contact_id": "5cc91445-cbc0-4f26-b668-c79ab7dffb83"},
    "date": "2023-09-15",
    "due_date": "2023-09-30",
    "line_items": [
        {
            "description": "Consulting services",
            "quantity": 10,
            "unit_amount": 100.00,
            "account_code": "200",
            "tax_type": "OUTPUT",
        }
    ],
    "reference": "INV-001",
    "status": "DRAFT",
    "line_amount_types": "EXCLUSIVE",
}


def validate_invoice_data(data):
    """
    Validate the invoice data against the schema.

    Args:
        data (dict): The invoice data to validate

    Returns:
        tuple: (is_valid, errors)
    """
    errors = []

    # Check required fields
    for field_name, field_info in INVOICE_SCHEMA.items():
        if field_info.get("required") and field_name not in data:
            errors.append(f"Missing required field: {field_name}")

    # Validate type field
    if "type" in data:
        if data["type"] not in INVOICE_SCHEMA["type"]["choices"]:
            errors.append(
                f"Invalid type value: {data['type']}. Must be one of {INVOICE_SCHEMA['type']['choices']}"
            )

    # Validate contact
    if "contact" in data:
        contact = data["contact"]
        if not isinstance(contact, dict):
            errors.append("Contact must be an object")
        elif "contact_id" not in contact and "name" not in contact:
            errors.append("Contact must have either contact_id or name")

    # Validate line_items
    if "line_items" in data:
        line_items = data["line_items"]
        if not isinstance(line_items, list):
            errors.append("line_items must be an array")
        else:
            for i, item in enumerate(line_items):
                if "description" not in item:
                    errors.append(f"LineItem {i+1} missing required field: description")
                if "quantity" not in item:
                    errors.append(f"LineItem {i+1} missing required field: quantity")
                if "unit_amount" not in item and "line_amount" not in item:
                    errors.append(
                        f"LineItem {i+1} missing required field: unit_amount or line_amount"
                    )

    # Check if there are any errors
    is_valid = len(errors) == 0

    return is_valid, errors
