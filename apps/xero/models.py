from django.db import models
from django.conf import settings
from django.core.exceptions import ValidationError
from django.utils import timezone
from datetime import timedelta


class XeroToken(models.Model):
    """Model to store Xero OAuth2 tokens"""

    # User relationship
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="xero_tokens",
        null=True,  # Allow null initially for backward compatibility
    )

    # Token data
    access_token = models.TextField()
    refresh_token = models.TextField()
    token_type = models.CharField(max_length=50)
    expires_in = models.IntegerField()
    scope = models.TextField()

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    expires_at = models.DateTimeField()

    class Meta:
        ordering = ["-created_at"]
        # Add unique constraint to ensure one token per user
        unique_together = ["user"]

    def save(self, *args, **kwargs):
        # Calculate expires_at based on expires_in using timezone-aware datetime
        if not self.expires_at and self.expires_in:
            self.expires_at = timezone.now() + timedelta(seconds=self.expires_in)
        super().save(*args, **kwargs)

    @property
    def is_expired(self):
        """Check if the token is expired"""
        return timezone.now() > self.expires_at if self.expires_at else True

    @classmethod
    def store_token(cls, token_data, user=None):
        """Store or update token data for a specific user"""
        if user:
            # Try to get existing token for this user
            token = cls.objects.filter(user=user).first() or cls(user=user)
        else:
            # Fallback for backward compatibility
            token = cls.objects.first() or cls()

        token.access_token = token_data["access_token"]
        token.refresh_token = token_data["refresh_token"]
        token.token_type = token_data["token_type"]
        token.expires_in = token_data["expires_in"]
        token.scope = token_data.get("scope", "")
        token.save()
        return token

    def __str__(self):
        user_str = f"for {self.user}" if self.user else "(no user)"
        return f"Xero Token {user_str} (expires: {self.expires_at})"
