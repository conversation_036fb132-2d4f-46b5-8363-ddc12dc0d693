from .models import Invoice
from django.contrib import admin


@admin.register(Invoice)
class InvoiceAdmin(admin.ModelAdmin):
    list_display = ("file_name", "uploaded_by", "uploaded_at", "status", "processed_at")
    list_filter = ("status", "uploaded_at", "processed_at")
    search_fields = ("file_name", "uploaded_by__username")
    readonly_fields = ("uploaded_at", "processed_at")

    fieldsets = (
        (
            "File Information",
            {"fields": ("file_name", "file_path", "uploaded_by", "uploaded_at")},
        ),
        ("Processing Status", {"fields": ("status", "processed_at", "error_message")}),
        (
            "Extracted Data",
            {
                "fields": ("invoice_data", "invoice_items", "extra_data"),
                "classes": ("collapse",),
            },
        ),
    )
