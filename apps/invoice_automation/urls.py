from . import views
from django.urls import path, include
from rest_framework.routers import DefaultRouter

app_name = "invoice_automation"

# Create a router and register our viewsets
router = DefaultRouter()
router.register(r"invoices", views.InvoiceViewSet, basename="invoice")

urlpatterns = [
    # API endpoints
    path("", include(router.urls)),
    path("process/", views.process_invoice_view, name="process_invoice"),
    path("verify/<int:invoice_id>/", views.mark_invoice_verified, name="mark_verified"),
]
