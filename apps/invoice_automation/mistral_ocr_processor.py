"""
Mistral OCR processor for invoice processing.
This module provides functions to process invoices using Mistral OCR.
"""

import os
import json
import base64
import tempfile
from pathlib import Path
from dotenv import load_dotenv
from mistralai import Mistral, DocumentURLChunk, ImageURLChunk, TextChunk

# Load environment variables
load_dotenv()

# Initialize Mistral client
api_key = os.getenv("MISTRAL_API_KEY", "no_mistral_api_key")
client = Mistral(api_key=api_key)


def process_pdf_with_ocr(client, pdf_path):
    """
    Process a PDF file with OCR and return the response dictionary.

    Args:
        client: Mistral AI client
        pdf_path (str): Path to the PDF file

    Returns:
        dict: OCR response dictionary
    """
    # Verify PDF file exists
    pdf_file = Path(pdf_path)
    assert pdf_file.is_file(), f"PDF file not found at {pdf_path}"

    # Upload PDF file to Mistral's OCR service
    uploaded_file = client.files.upload(
        file={
            "file_name": pdf_file.stem,
            "content": pdf_file.read_bytes(),
        },
        purpose="ocr",
    )

    # Get URL for the uploaded file
    signed_url = client.files.get_signed_url(file_id=uploaded_file.id, expiry=1)

    # Process PDF with OCR, including embedded images
    pdf_response = client.ocr.process(
        document=DocumentURLChunk(document_url=signed_url.url),
        model="mistral-ocr-latest",
        include_image_base64=False,
    )

    # Convert response to JSON format
    response_dict = json.loads(pdf_response.model_dump_json())

    return response_dict


def process_image_with_ocr(client, image_path):
    """
    Process an image with OCR and return the response dictionary.

    Args:
        client: The OCR client
        image_path (str): Path to the image file

    Returns:
        dict: The OCR response dictionary
    """
    # Verify image exists
    image_file = Path(image_path)
    assert image_file.is_file(), f"Image file not found: {image_path}"

    # Encode image as base64 for API
    encoded = base64.b64encode(image_file.read_bytes()).decode()
    base64_data_url = f"data:image/jpeg;base64,{encoded}"

    # Process image with OCR
    image_response = client.ocr.process(
        document=ImageURLChunk(image_url=base64_data_url), model="mistral-ocr-latest"
    )

    # Convert response to JSON
    response_dict = json.loads(image_response.model_dump_json())
    return response_dict


def parse_md(client, markdown_text, base64_data_url=None):
    """
    Convert markdown text to structured JSON data

    Args:
        client: The LLM client
        markdown_text: The markdown text to convert
        base64_data_url: Optional base64 image URL if image context is needed

    Returns:
        dict: Structured JSON data
    """
    # Prepare content based on whether image URL is provided
    content = []
    if base64_data_url:
        content.append(ImageURLChunk(image_url=base64_data_url))

    content.append(
        TextChunk(
            text=(
                f"This is image's OCR in markdown:\n\n{markdown_text}\n.\n"
                "Convert this into a sensible structured json response. "
                "The output should be strictly be json with no extra commentary"
            )
        )
    )

    # Get structured response from model
    chat_response = client.chat.complete(
        model="pixtral-12b-latest",
        messages=[
            {
                "role": "user",
                "content": content,
            }
        ],
        response_format={"type": "json_object"},
        temperature=0,
    )

    # Parse and return JSON response
    response_dict = json.loads(chat_response.choices[0].message.content)
    return response_dict


def process_invoice_with_mistral(file_path):
    """
    Process an invoice file with Mistral OCR and return structured data.

    Args:
        file_path (str): Path to the invoice file (PDF or image)

    Returns:
        dict: Structured invoice data
    """
    # Determine file type based on extension
    if file_path.lower().endswith(".pdf"):
        response_dict = process_pdf_with_ocr(client, file_path)
    else:  # Assume it's an image
        response_dict = process_image_with_ocr(client, file_path)

    # Process each page's markdown through parse_md
    for page in response_dict.get("pages", []):
        if "markdown" in page:
            # Parse the markdown and add the parsed JSON
            parsed_json = parse_md(client, page["markdown"])
            page["json"] = parsed_json

    return response_dict
