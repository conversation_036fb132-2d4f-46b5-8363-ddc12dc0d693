from .models import Invoice
from rest_framework import serializers


class InvoiceSerializer(serializers.ModelSerializer):
    """Serializer for the Invoice model"""

    status_display = serializers.SerializerMethodField()

    class Meta:
        model = Invoice
        fields = [
            "id",
            "file_name",
            "file_path",
            "uploaded_by",
            "uploaded_at",
            "processed_at",
            "status",
            "status_display",
            "invoice_data",
            "invoice_items",
            "extra_data",
            "error_message",
        ]
        read_only_fields = fields  # All fields are read-only

    def get_status_display(self, obj):
        """Return the display name for the status"""
        return dict(Invoice.STATUS_CHOICES).get(obj.status, obj.status)
