import os
import time
import json
import base64
import tempfile
from pathlib import Path
from dotenv import load_dotenv
from azure.core.credentials import AzureKeyCredential
from azure.ai.documentintelligence import DocumentIntelligenceClient

from utils.logger import get_logger

# Import the Mistral OCR processor
from .mistral_ocr_processor import process_invoice_with_mistral

# Set up logger
invoice_processor_logger = get_logger("invoice_automation.processor")

load_dotenv()

endpoint = os.getenv("AZURE_DOCUMENT_INTELLIGENCE_ENDPOINT", "no_azure_endpoint")
key = os.getenv("AZURE_DOCUMENT_INTELLIGENCE_KEY", "no_azure_key")
document_intelligence_client = DocumentIntelligenceClient(
    endpoint=endpoint, credential=AzureKeyCredential(key)
)

USE_AZURE_FORM_RECOGNIZER = (
    os.getenv("USE_AZURE_FORM_RECOGNIZER", "false").lower() == "true"
)


def clean_currency(amount_str):
    if amount_str:
        # remove currrecvy, symbols and commas, then convert to float
        return float(amount_str.replace("$", "").replace(",", ""))
    return None


def parse_azure_result(ocr_result):
    if not ocr_result:
        return [], [], []

    invoice_info_list = []
    invoice_items_list = []
    other_tables_raw = []
    final_extra_tables_list = []

    # Process documents
    for doc in ocr_result.documents:
        # Extract items
        if "Items" in doc.fields:
            items = doc.fields["Items"]["valueArray"]
            for item in items:
                item_dict = {}
                for key, field in item["valueObject"].items():
                    item_dict[key] = field["content"]
                invoice_items_list.append(item_dict)

        # Extract other fields
        info_dict = {}
        for field_name, field in doc.fields.items():
            if field_name != "Items":  # Skip items as they're handled separately
                if "content" in field:
                    info_dict[field_name] = field["content"]
                else:
                    other_tables_raw.append([field_name, field])
                    continue
        invoice_info_list.append(info_dict)

        # Process extra tables
        for table_entry in other_tables_raw:
            field_name, field = table_entry
            table_items = []

            if "valueArray" in field:
                items = field["valueArray"]
                for item in items:
                    item_dict = {}
                    if "valueObject" in item:
                        for key, value in item["valueObject"].items():
                            if "content" in value:
                                item_dict[key] = value["content"]
                    if item_dict:  # Only append if we extracted some content
                        table_items.append(item_dict)

            if table_items:  # Only append if we found items in this table
                final_extra_tables_list.append({field_name: table_items})

        return invoice_info_list, invoice_items_list, final_extra_tables_list


def procecess_mistral_result(ocr_result):
    # Extract invoice information from the OCR result
    invoice_info_list = []
    invoice_items_list = []

    # Process each page in the OCR result
    for page in ocr_result.get("pages", []):
        if "json" in page:
            # Extract the structured JSON data from the page
            json_data = page["json"]

            def extract_items(data):
                if isinstance(data, dict):
                    for key, value in data.items():
                        if key == "items" and isinstance(value, list):
                            invoice_items_list.extend(value)
                            del data[key]
                            return True
                        elif isinstance(value, (dict, list)):
                            if extract_items(value):
                                return True
                elif isinstance(data, list):
                    for item in data:
                        if extract_items(item):
                            return True
                return False

            extract_items(json_data)
            invoice_info_list.append(json_data)

        else:
            print("No structured JSON data found in page")

    print("Invoice Info: \n", invoice_info_list, "\n\n")
    print("Invoice Items: \n", invoice_items_list, "\n\n")
    return invoice_info_list, invoice_items_list


def process_invoice(uploaded_file):
    try:
        # Reset file position to beginning to ensure we can read the full file
        uploaded_file.seek(0)

        # Start tracking time for file processing

        with tempfile.NamedTemporaryFile(
            delete=False, suffix="." + uploaded_file.name.split(".")[-1]
        ) as tmp_file:
            tmp_file.write(uploaded_file.read())
            tmp_file_path = tmp_file.name
            invoice_processor_logger.info(f"Created temporary file: {tmp_file_path}")

        if USE_AZURE_FORM_RECOGNIZER:
            invoice_processor_logger.info("Using Azure Document Intelligence for OCR")

            # Process the invoice with Azure Form Recognizer
            with open(tmp_file_path, "rb") as file:
                document_content = file.read()

                # request body in base64
                body = {"base64Source": base64.b64encode(document_content).decode()}

                # Start API call timer
                api_call_start = time.time()

                # Make the API call
                poller = document_intelligence_client.begin_analyze_document(
                    model_id="prebuilt-invoice", body=body
                )

                # Wait for the result
                ocr_result = poller.result()

                # Measure API call time
                api_call_time = time.time() - api_call_start
                invoice_processor_logger.info(
                    f"Azure API call time: {api_call_time:.2f} seconds"
                )

            invoice_processor_logger.info("Successfully received results from Azure")

            # Parse the result
            result = parse_azure_result(ocr_result)
            if result:
                invoice_info_list, invoice_items_list, other_tables_list = result
            else:
                invoice_info_list, invoice_items_list, other_tables_list = [], [], []

            os.unlink(tmp_file_path)

            return invoice_info_list, invoice_items_list, other_tables_list
        else:
            invoice_processor_logger.info("Using Mistral OCR")

            # Start API call timer
            api_call_start = time.time()

            # Process the invoice with Mistral OCR
            ocr_result = process_invoice_with_mistral(tmp_file_path)

            # Measure API call time
            api_call_time = time.time() - api_call_start
            invoice_processor_logger.info(
                f"Mistral OCR API call time: {api_call_time:.2f} seconds"
            )

            invoice_processor_logger.info(
                "Successfully received results from Mistral OCR"
            )

            # Start parsing timer
            parsing_start = time.time()

            # Parse the result
            invoice_info_list, invoice_items_list = procecess_mistral_result(ocr_result)

            # Measure parsing time
            parsing_time = time.time() - parsing_start
            invoice_processor_logger.info(
                f"Time taken to parse results: {parsing_time:.2f} seconds"
            )

            os.unlink(tmp_file_path)

            # Calculate total processing time
            total_time = time.time() - start_time
            invoice_processor_logger.info(
                f"Total processing time for {uploaded_file.name}: {total_time:.2f} seconds"
            )

            return invoice_info_list, invoice_items_list, []
    except Exception as e:
        if "tmp_file_path" in locals() and os.path.exists(tmp_file_path):
            os.unlink(tmp_file_path)
        invoice_processor_logger.error(f"Error processing file: {str(e)}")
        return [], [], []
