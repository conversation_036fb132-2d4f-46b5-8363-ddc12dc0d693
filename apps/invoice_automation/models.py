from django.db import models
from django.utils import timezone
from django.contrib.auth import get_user_model

User = get_user_model()


class Invoice(models.Model):
    """Model for storing processed invoice information"""

    id = models.AutoField(primary_key=True)

    STATUS_CHOICES = (
        ("pending", "Pending"),
        ("processed", "Processed"),
        ("error", "Error"),
        ("verified", "Verified"),
    )

    file_name = models.CharField(max_length=255)
    file_path = models.CharField(max_length=512)
    uploaded_by = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True, related_name="invoices"
    )
    uploaded_at = models.DateTimeField(default=timezone.now)
    processed_at = models.DateTimeField(null=True, blank=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default="pending")

    # Store JSON data extracted from invoice
    invoice_data = models.JSO<PERSON>ield(null=True, blank=True)
    invoice_items = models.JSO<PERSON>ield(null=True, blank=True)
    extra_data = models.JSO<PERSON>ield(null=True, blank=True)

    # Error information if processing failed
    error_message = models.TextField(null=True, blank=True)

    def __str__(self):
        return f"Invoice {self.file_name} ({self.status})"

    def mark_as_processed(self, invoice_data=None, invoice_items=None, extra_data=None):
        """Mark invoice as processed with extracted data"""
        self.status = "processed"
        self.processed_at = timezone.now()

        if invoice_data:
            self.invoice_data = invoice_data
        if invoice_items:
            self.invoice_items = invoice_items
        if extra_data:
            self.extra_data = extra_data

        self.save()

    def mark_as_error(self, error_message):
        """Mark invoice as failed with error message"""
        self.status = "error"
        self.processed_at = timezone.now()
        self.error_message = error_message
        self.save()

    def mark_as_verified(self):
        """Mark invoice as verified"""
        self.status = "verified"
        self.save()
