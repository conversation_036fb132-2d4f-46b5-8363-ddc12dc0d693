from django.contrib import admin
from .models import ChatConversation


@admin.register(ChatConversation)
class ChatConversationAdmin(admin.ModelAdmin):
    list_display = ("user_id", "category", "original_question", "timestamp")
    list_filter = ("category", "timestamp")
    search_fields = ("user_id", "original_question", "response")
    readonly_fields = ("timestamp",)
    ordering = ("-timestamp",)
