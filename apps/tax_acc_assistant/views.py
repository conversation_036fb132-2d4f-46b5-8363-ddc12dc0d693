import os
import json
import pytz
import asyncio
import mimetypes
import traceback
from django.urls import reverse
from django.conf import settings
from django.db.models import Max
from django.utils import timezone
from django.shortcuts import render
from .models import ChatConversation
from rest_framework.decorators import api_view
from django.http import JsonResponse, HttpResponse
from utils.logger import get_logger, TAX_ACC_LOGGER
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_POST
from django.core.files.storage import default_storage

from .accounting_chatbot import get_user_email_folder
from .tax_acc_agent_openai import orchestrator_function, send_memo_email
from apps.credit_balance.services import (
    deduct_credit,
    get_or_create_user_credit_balance,
)

# Initialize logger for the views
logger = get_logger(TAX_ACC_LOGGER)


@csrf_exempt
@api_view(["POST"])
def accounting_chat_api(request):
    """API endpoint for the accounting and tax assistant chatbot."""
    try:
        # Check if user is authenticated
        if not request.user.is_authenticated:
            return JsonResponse({"error": "Authentication required"}, status=401)

        # Parse the request data
        data = json.loads(request.body)
        question = data.get("question", "")

        if not question:
            return JsonResponse({"error": "No question provided"}, status=400)

        # Get credit cost per request from environment variable (default: 10 credits)
        credits_per_request = int(os.getenv("CREDITS_PER_TAX_ACC_CHAT_REQUEST", "10"))

        # Check user's credit balance
        try:
            user_credit_balance = get_or_create_user_credit_balance(request.user)
            if user_credit_balance.balance < credits_per_request:
                return JsonResponse(
                    {
                        "error": f"Insufficient credit balance. You need {credits_per_request} credits but have {user_credit_balance.balance} credits.",
                        "credits_required": credits_per_request,
                        "current_balance": user_credit_balance.balance,
                    },
                    status=402,
                )  # 402 Payment Required
        except Exception as e:
            logger.error(
                f"Error checking credit balance for user {request.user.id}: {str(e)}"
            )
            return JsonResponse({"error": "Error checking credit balance"}, status=500)

        # Check if memo format is requested
        memo_format = data.get("memo_format", False)
        memo_details = data.get("memo_details", None)

        # Get chat session ID if provided
        chat_session_id = data.get("chat_session_id", None)

        # Use the authenticated user ID
        user_id = f"user_{request.user.id}"

        # Generate chat session ID if not provided
        if not chat_session_id:
            chat_session_id = str(__import__("uuid").uuid4())

        # Process the question asynchronously using the new orchestrator
        response = asyncio.run(
            orchestrator_function(
                query=question,
                memo_required=memo_format,
                email_required=bool(memo_details and memo_details.get("email")),
                memo_details=memo_details,
                user=request.user,
                chat_session_id=chat_session_id,
            )
        )

        # Deduct credits after successful processing
        try:
            deduct_credit(
                user=request.user,
                amount=credits_per_request,
                description=f"Tax & Accounting Assistant chat request - {question[:50]}...",
            )
            logger.info(
                f"Deducted {credits_per_request} credits from user {request.user.id}"
            )
        except Exception as e:
            logger.error(
                f"Error deducting credits from user {request.user.id}: {str(e)}"
            )
            # Continue processing even if credit deduction fails

        # Store conversation in database for history
        try:
            from .models import ChatConversation
            from asgiref.sync import sync_to_async

            conversation = ChatConversation(
                user_id=user_id,
                chat_session_id=chat_session_id,
                original_question=question,
                corrected_question=question,  # The orchestrator handles grammar correction internally
                category=response.get("category", "Unknown"),
                response=response.get("response", ""),
                memo_url=response.get("memo_url"),
            )
            asyncio.run(sync_to_async(conversation.save)())
        except Exception as e:
            logger.error(f"Error saving conversation: {str(e)}")

        # Transform response to match expected frontend format
        transformed_response = {
            "answer": response.get("response", ""),
            "chat_session_id": chat_session_id,
            "category": response.get("category", "Unknown"),
            "memo_url": response.get("memo_url"),
            "valid_inquiry": response.get("success", True),
            "email_sent": response.get("email_sent", False),
            "email_error": response.get("email_error"),
        }

        response = transformed_response

        # Add full URL for memo if available
        if "memo_url" in response and response["memo_url"]:
            memo_url = response["memo_url"]
            # Generate a URL to our download endpoint
            download_url = request.build_absolute_uri(
                reverse("download_memo", args=[memo_url])
            )
            response["memo_url"] = download_url

        return JsonResponse(response)

    except json.JSONDecodeError:
        return JsonResponse({"error": "Invalid JSON data"}, status=400)
    except Exception as e:
        logger.error(f"Error in accounting_chat_api: {str(e)}")
        return JsonResponse({"error": str(e)}, status=500)


@csrf_exempt
@api_view(["GET"])
def chat_history_api(request):
    """API endpoint to fetch user's chat history with the accounting assistant."""
    try:
        # Check if user is authenticated
        if not request.user.is_authenticated:
            return JsonResponse({"error": "Authentication required"}, status=401)

        # Use the authenticated user ID
        user_id = f"user_{request.user.id}"

        # Get the limit parameter (default 10)
        limit = int(request.GET.get("limit", 10))

        # Fetch all unique chat session IDs for this user
        # Use a subquery to get the latest timestamp for each chat session
        latest_timestamps = (
            ChatConversation.objects.filter(user_id=user_id)
            .exclude(chat_session_id__isnull=True)
            .exclude(chat_session_id="")
            .values("chat_session_id")
            .annotate(latest_timestamp=Max("timestamp"))
            .order_by("-latest_timestamp")[:limit]
        )

        # Format the conversations grouped by chat session
        history = []

        # Get the current timezone from settings
        current_tz = pytz.timezone(settings.TIME_ZONE)

        for session_data in latest_timestamps:
            chat_session_id = session_data["chat_session_id"]

            # Get all messages in this chat session
            conversations = ChatConversation.objects.filter(
                user_id=user_id, chat_session_id=chat_session_id
            ).order_by("timestamp")

            if conversations.exists():
                # Get the first message as the session starter
                first_convo = conversations.first()

                # Add null check for first_convo before accessing its attributes
                if first_convo:
                    # Convert timestamp to the correct timezone
                    localized_timestamp = timezone.localtime(
                        first_convo.timestamp, current_tz
                    )

                    session_data = {
                        "chat_session_id": chat_session_id,
                        "initial_question": first_convo.original_question,
                        "category": first_convo.category,
                        "timestamp": localized_timestamp.strftime("%Y-%m-%d %H:%M"),
                        "messages": [],
                    }

                    # Add all messages in the session
                    for convo in conversations:
                        # Convert timestamp to the correct timezone for each message
                        msg_localized_timestamp = timezone.localtime(
                            convo.timestamp, current_tz
                        )

                        # Create download URL for memos if available
                        memo_url = None
                        if convo.memo_url:
                            memo_url = request.build_absolute_uri(
                                reverse("download_memo", args=[convo.memo_url])
                            )

                        session_data["messages"].append(
                            {
                                "question": convo.original_question,
                                "response": convo.response,
                                "memo_url": memo_url,
                                "timestamp": msg_localized_timestamp.strftime(
                                    "%Y-%m-%d %H:%M"
                                ),
                            }
                        )

                    history.append(session_data)

        return JsonResponse({"history": history})
    except Exception as e:
        logger.error(f"Error in chat_history_api: {str(e)}")
        return JsonResponse({"error": str(e)}, status=500)


@api_view(["GET"])
def download_memo(request, filename):
    """
    Serve memo files with proper content-type headers for download.
    This fixes issues with the default file serving not setting proper headers.
    """
    try:
        # Check if user is authenticated
        if not request.user.is_authenticated:
            return JsonResponse({"error": "Authentication required"}, status=401)

        logger.info(f"Download request for filename: {filename}")

        # Full path to file - memos/{user_folder}/{chat_session_id}/memo_{random_id}.docx
        file_path = os.path.join("memos", filename)
        logger.info(f"Looking for file at path: {file_path}")

        # Check if file exists
        if not default_storage.exists(file_path):
            logger.error(f"File not found at path: {file_path}")
            return JsonResponse({"error": "File not found"}, status=404)

        # Security check - verify that this memo belongs to the current user or user is staff
        # Extract user email part from the path
        path_parts = filename.split("/")
        if len(path_parts) >= 2:
            user_folder = path_parts[0]
            current_user_folder = get_user_email_folder(request.user)

            # If the file doesn't belong to current user and user is not staff, deny access
            if user_folder != current_user_folder and not request.user.is_staff:
                logger.warning(
                    f"Unauthorized memo access attempt by {request.user} for {filename}"
                )
                return JsonResponse({"error": "Unauthorized access"}, status=403)

        # Get the file from storage
        file = default_storage.open(file_path, "rb")
        file_content = file.read()
        file.close()

        # Get the memo filename without the path
        output_filename = os.path.basename(filename)

        # Determine the content type
        content_type, _ = mimetypes.guess_type(output_filename)
        if not content_type:
            content_type = "application/octet-stream"

        # Create the response with the file content
        response = HttpResponse(file_content, content_type=content_type)

        # Set headers for file download
        response["Content-Disposition"] = f'attachment; filename="{output_filename}"'
        response["Content-Length"] = len(file_content)

        # Add security headers
        response["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"

        return response
    except Exception as e:
        logger.error(f"Error serving memo file: {str(e)}")
        logger.error(f"Stack trace: {traceback.format_exc()}")
        return JsonResponse({"error": str(e)}, status=500)


@csrf_exempt
@api_view(["POST"])
def send_memo_email_api(request):
    """API endpoint to send memo files via email."""
    try:
        # Check if user is authenticated
        if not request.user.is_authenticated:
            return JsonResponse({"error": "Authentication required"}, status=401)

        # Parse the request data
        data = json.loads(request.body)
        memo_filename = data.get("memo_filename", "")
        email_addresses = data.get("email_addresses", [])
        subject = data.get("subject", "Technical Memo")
        message = data.get("message", "Please find the attached memo.")

        if not memo_filename:
            return JsonResponse({"error": "No memo filename provided"}, status=400)

        if not email_addresses:
            return JsonResponse({"error": "No email addresses provided"}, status=400)

        # Full path to file
        file_path = os.path.join("memos", memo_filename)

        # Check if file exists
        if not default_storage.exists(file_path):
            return JsonResponse({"error": "Memo file not found"}, status=404)

        # Security check - verify that this memo belongs to the current user
        path_parts = memo_filename.split("/")
        if len(path_parts) >= 2:
            user_folder = path_parts[0]
            current_user_folder = get_user_email_folder(request.user)

            if user_folder != current_user_folder and not request.user.is_staff:
                logger.warning(
                    f"Unauthorized memo email attempt by {request.user} for {memo_filename}"
                )
                return JsonResponse({"error": "Unauthorized access"}, status=403)

        # Get the file from storage
        stored_file = default_storage.open(file_path, "rb")
        file_content = stored_file.read()
        stored_file.close()

        # Send emails to all addresses
        successful_emails = []
        failed_emails = []

        for email in email_addresses:
            try:
                email_sent = send_memo_email(
                    email,
                    subject,
                    message,
                    file_content,
                    os.path.basename(memo_filename),
                    memo_subject=subject,
                    user_query=None,  # Not available in this context
                )
                if email_sent:
                    successful_emails.append(email)
                else:
                    failed_emails.append(email)
            except Exception as e:
                logger.error(f"Error sending email to {email}: {str(e)}")
                failed_emails.append(email)

        return JsonResponse(
            {
                "success": True,
                "successful_emails": successful_emails,
                "failed_emails": failed_emails,
                "message": f"Email sent to {len(successful_emails)} recipients",
            }
        )

    except json.JSONDecodeError:
        return JsonResponse({"error": "Invalid JSON data"}, status=400)
    except Exception as e:
        logger.error(f"Error in send_memo_email_api: {str(e)}")
        return JsonResponse({"error": str(e)}, status=500)
