from django.db import models

# Create your models here


class ChatConversation(models.Model):
    """
    Model to store conversations between users and the accounting chatbot.
    Stores both original and corrected questions.
    """

    user_id = models.CharField(max_length=255, default="anonymous")
    chat_session_id = models.CharField(
        max_length=255, blank=True, null=True, db_index=True
    )  # To group related messages
    parent_id = models.IntegerField(
        null=True, blank=True
    )  # For threaded conversations, null means top level
    original_question = models.TextField()
    corrected_question = models.TextField()
    category = models.CharField(max_length=50, blank=True)  # Accounting or Tax
    response = models.TextField()
    timestamp = models.DateTimeField(auto_now_add=True)
    memo_url = models.CharField(max_length=255, blank=True, null=True)

    class Meta:
        indexes = [
            models.Index(fields=["user_id", "timestamp"]),
            models.Index(fields=["chat_session_id", "timestamp"]),
        ]
        ordering = ["-timestamp"]

    def __str__(self):
        return f"{self.user_id} - {self.timestamp.strftime('%Y-%m-%d %H:%M')}"
