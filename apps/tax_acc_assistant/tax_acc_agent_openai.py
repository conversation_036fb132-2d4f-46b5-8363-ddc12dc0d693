# Using OpenAI Agents SDK
import os
import re
import json
import time
import asyncio
import smtplib
import requests
from docx import Document
from datetime import datetime
from dotenv import load_dotenv
from openai import AsyncOpenAI
from dataclasses import dataclass
from typing import List, Optional
from pydantic import BaseModel, Field
from email.message import EmailMessage
from typing import Literal, Any, Optional, List
from openai.types.responses import ResponseTextDeltaEvent
from agents import set_tracing_disabled, Agent, function_tool, Runner
from utils.logger import get_logger, log_exception, TAX_ACC_LOGGER
import docx.opc.constants
from docx.oxml.parser import OxmlElement
from docx.oxml.ns import qn
from docx.shared import RGBColor

import tempfile
from django.core.files.base import ContentFile
from django.core.files.storage import default_storage
from tavily import TavilyClient
from agents import enable_verbose_stdout_logging
import logging


# Initialize logger
logger = get_logger(
    TAX_ACC_LOGGER, level=logging.DEBUG
)  # Set to DEBUG level to capture all logs
load_dotenv()
# enable_verbose_stdout_logging()

# Test mode flag - set to True to use static response instead of calling OpenAI API
USE_STATIC_RESPONSE_FOR_TEST = (
    os.getenv("USE_STATIC_RESPONSE_FOR_TEST", "True").lower() == "true"
)
NUM_RESULTS_FOR_SEARCH = os.getenv("NUM_RESULTS_FOR_SEARCH", 5)
llm = os.getenv("LLM_MODEL_FOR_TAX_ASSISTANT", "gpt-4.1-mini")
USE_TAVILY_SEARCH = os.getenv("USE_TAVILY_SEARCH", "True").lower() == "true"
logger.info(f"USE_TAVILY_SEARCH: {USE_TAVILY_SEARCH}")
if USE_TAVILY_SEARCH:
    TAVILY_API_KEY = os.getenv("TAVILY_API_KEY", "")
    tavily_client = TavilyClient(api_key=TAVILY_API_KEY)
else:
    raise ValueError("TAVILY_API_KEY is not set")

# Static response for testing
STATIC_RESPONSE = """
Background: The question pertains to the accounting treatment of interest rate swaps for cash flow hedges under International Financial Reporting Standards (IFRS). Interest rate swaps are commonly used to manage interest rate risk by exchanging a fixed interest rate for a floating interest rate or vice versa. Cash flow hedges are a type of hedge used to hedge the exposure to variability in cash flows that is attributable to a particular risk associated with a recognized asset or liability.

Analysis: Under IFRS, the accounting treatment for interest rate swaps used as cash flow hedges involves recognizing the effective portion of the gain or loss on the swap in other comprehensive income (OCI) and the ineffective portion in profit or loss. The effective portion is determined by assessing the hedge effectiveness, which is typically done using a hedge effectiveness test.

The specific guidance for accounting for cash flow hedges, including interest rate swaps, can be found in IFRS 9 Financial Instruments. According to IFRS 9, when an interest rate swap is designated as a cash flow hedge and meets the hedge accounting criteria, any changes in the fair value of the swap that are effective in offsetting the changes in cash flows of the hedged item are recognized in OCI.

It is important to note that any ineffective portion of the hedge, i.e., the portion that does not qualify for hedge accounting treatment, is recognized immediately in profit or loss. This ensures that the financial statements accurately reflect the economic substance of the hedge.

Conclusion: In conclusion, under IFRS, interest rate swaps used as cash flow hedges are accounted for by recognizing the effective portion of the gain or loss in OCI and the ineffective portion in profit or loss. This treatment ensures that the financial statements provide a true and fair view of the hedging relationship and its impact on the entity's financial performance.

Citations:

IFRS 9 Financial Instruments
IFRS 9 provides guidance on the accounting treatment of financial instruments, including cash flow hedges.
https://www.ifrs.org/issued-standards/list-of-standards/ifrs-9-financial-instruments/
"""


################################################################################
## Helper functions ############################################################
################################################################################


# function for Google search - not a function tool
def google_search(query: str, site_restrictions: List[str] = []) -> str:
    logger.info(
        f"Google Search: Parameters - query:{query}, sites:{site_restrictions}, num_results:{NUM_RESULTS_FOR_SEARCH}"
    )
    """
    Perform a Google search using the Custom Search JSON API.

    Args:
        query (str): The search query.
        site_restrictions (List[str], optional): List of domains to restrict the search to.

    Returns:
        str: Formatted search results.
    """

    api_key = os.getenv("GOOGLE_PSE_KEY")
    search_engine_id = os.getenv("GOOGLE_PSE_ID")

    if not api_key or not search_engine_id:
        logger.error("API key or Search Engine ID not found in environment variables.")
        return "API key or Search Engine ID not found in environment variables."

    # Construct the query with site restrictions if provided
    if site_restrictions:
        site_query = " OR ".join([f"site:{domain}" for domain in site_restrictions])
        query = f"{query} {site_query}"

    url = "https://www.googleapis.com/customsearch/v1"
    params = {
        "key": api_key,
        "cx": search_engine_id,
        "q": query,
        "num": NUM_RESULTS_FOR_SEARCH,
    }

    try:
        response = requests.get(url, params=params)
        response.raise_for_status()  # Raise exception for HTTP errors

        results = response.json().get("items", [])
        if not results:
            logger.warning("No results found for query: " + query)
            return "No results found."

        logger.info(f"Google Search: Retrieved {len(results)} results")

        # Format the results
        formatted_results = ""
        for item in results:
            title = item.get("title")
            link = item.get("link")
            snippet = item.get("snippet")
            formatted_results += f"Title: {title}\nLink: {link}\nSnippet: {snippet}\n\n"

        logger.info(f"Google Search results: {formatted_results}")

        return formatted_results

    except Exception as e:
        log_exception(logger, e, {"function": "google_search", "query": query})
        return f"Error performing search: {str(e)}"


def tavily_search(query: str, site_restrictions: List[str] = []) -> str:
    """
    Perform a search using the Tavily API.

    Args:
        query (str): The search query.
        site_restrictions (List[str], optional): List of domains to restrict the search to.

    Returns:
        str: Formatted search results.
    """
    logger.info(f"Tavily Search: Parameters - query:{query}, sites:{site_restrictions}")
    results = tavily_client.search(
        query=query,
        include_domains=site_restrictions,
    )
    # Format the results from Tavily search response
    formatted_results = ""

    # Add query information if available
    if isinstance(results, dict):
        query = results.get("query", "")
        if query:
            formatted_results += f"Search Query: {query}\n\n"

        # Get the results list
        search_results = results.get("results", [])
    else:
        search_results = results  # If results is already a list

    # Format each search result
    for item in search_results:
        title = item.get("title", "")
        link = item.get("url", "")
        content = item.get("content", "")
        score = str(item.get("score", ""))

        formatted_results += (
            f"Title: {title}\n"
            f"Link: {link}\n"
            f"Content: {content}\n"
            f"Relevance Score: {score}\n\n"
        )

    logger.info(f"Tavily Search results: {formatted_results}")

    return formatted_results


async def run_agent_with_streaming(
    agent: Agent, input_text: str, context: Optional[Any] = None, verbose: bool = True
) -> str:
    """
    Run an agent with streaming output.

    Args:
        agent: The agent to run
        input_text: The input text for the agent
        context: Optional context to pass to the agent
        verbose: Whether to print the streaming output

    Returns:
        The complete response text
    """
    logger.info(f"Running agent with streaming: {agent.name}")
    start_time = time.time()
    # Use run_streamed for the agent output
    stream_result = Runner.run_streamed(
        starting_agent=agent, input=input_text, context=context
    )

    response_parts = []

    # Process the streaming events
    async for event in stream_result.stream_events():
        # Check for text delta events which have the delta attribute
        if event.type == "raw_response_event" and isinstance(
            event.data, ResponseTextDeltaEvent
        ):
            if verbose:
                print(event.data.delta, end="", flush=True)
            response_parts.append(event.data.delta)

    # Return the complete response
    complete_response = "".join(response_parts)
    end_time = time.time()
    elapsed_time = end_time - start_time
    logger.info(f"Agent {agent.name} completed in {elapsed_time:.2f} seconds")
    logger.debug(f"Agent streaming response length: {len(complete_response)}")
    return complete_response


async def stream_text(text: str):
    """
    Simple utility function to stream text character by character.
    """
    for char in text:
        print(char, end="", flush=True)
        await asyncio.sleep(0.01)  # Small delay for streaming effect
    print()  # New line at the end


def adapt_tool_output_for_gemini(raw_output: str) -> str:
    try:
        data = json.loads(raw_output)
    except json.JSONDecodeError:
        # If already plain text, or not JSON
        return raw_output

    # Turn the JSON into readable text
    text_parts = []
    for key, value in data.items():
        if isinstance(value, list):
            value = ", ".join(str(v) for v in value)
        text_parts.append(f"{key}: {value}")

    return "\n".join(text_parts)


def generate_memo_docx(question, response_obj, meta, user=None, chat_session_id=None):
    """
    Generate a Word document memo from a structured response object.

    Args:
        question (str): The grammatically corrected query
        response_obj (dict): Structured response object with background, analysis, conclusion, and citations
        meta (dict): Metadata like company, author, date, and subject
        user: Django user object for organizing files
        chat_session_id: Chat session identifier for grouping conversations

    Returns:
        str: Path to the generated document in storage
    """
    logger.info("Generating memo document")

    # Ensure memos directory exists
    memos_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "memos")
    if not os.path.exists(memos_dir):
        logger.info(f"Creating memos directory: {memos_dir}")
        os.makedirs(memos_dir, exist_ok=True)

    doc = Document()
    doc.add_heading("Technical Memorandum", 0)
    doc.add_paragraph(f"Company: {meta['company']}")
    doc.add_paragraph(f"Prepared By: {meta['author']}")
    doc.add_paragraph(f"Date: {meta['date']}")
    doc.add_paragraph(f"Subject: {meta['subject']}")
    doc.add_paragraph("")  # Spacer

    # Section: Question
    bg_heading = doc.add_paragraph("Question")
    bg_heading.runs[0].bold = True

    # Add the original question first
    if question:
        question_para = doc.add_paragraph("")
        question_para.add_run(question).italic = True
        doc.add_paragraph("")  # Add space after question

    # Helper function to create hyperlink
    def create_hyperlink(paragraph, url, text, color=RGBColor(0, 0, 255)):
        """
        Create a hyperlink in a paragraph.

        Args:
            paragraph: The paragraph to add the hyperlink to
            url: The URL for the hyperlink
            text: The text to display for the hyperlink
            color: The color for the hyperlink (default is blue)

        Returns:
            The hyperlink run
        """
        # Create run with the hyperlink text
        hyperlink_run = paragraph.add_run(text)
        hyperlink_run.font.underline = True
        hyperlink_run.font.color.rgb = color  # Blue color

        try:
            # Get relationship ID for the hyperlink
            r_id = paragraph.part.relate_to(
                url,
                docx.opc.constants.RELATIONSHIP_TYPE.HYPERLINK,
                is_external=True,
            )

            # Create hyperlink XML element
            hyperlink_elem = OxmlElement("w:hyperlink")
            hyperlink_elem.set(qn("r:id"), r_id)

            # Add the w:r element of the newly created run to the hyperlink
            hyperlink_elem.append(hyperlink_run._element)

            # Replace the original run with the hyperlink
            paragraph._p.append(hyperlink_elem)

            return hyperlink_run
        except Exception as e:
            log_exception(logger, e, {"function": "create_hyperlink", "url": url})
            # If hyperlink creation fails, return the text run
            return hyperlink_run

    # Helper function to process text with URLs
    def process_text_with_urls(text, citations):
        """
        Replace URLs in text with '(source)' links.

        Args:
            text: The text to process
            citations: List of citation URLs

        Returns:
            Tuple of (processed_text, url_indices_map) where url_indices_map maps
            indices in the processed text to URLs
        """
        # First create a map of URL positions
        url_positions = []
        for url in citations:
            if isinstance(url, str) and url.startswith("http"):
                url_clean = url.strip()
                for match in re.finditer(re.escape(url_clean), text):
                    url_positions.append((match.start(), match.end(), url_clean))

        # Sort by start position (reverse order to avoid index shifting)
        url_positions.sort(key=lambda x: x[0], reverse=True)

        # Replace URLs with source placeholder format
        source_counter = 1
        url_indices_map = {}  # Maps source number to URL
        processed_text = text

        for start, end, url in url_positions:
            placeholder = f"(source)"
            url_indices_map[source_counter] = url
            processed_text = processed_text[:start] + placeholder + processed_text[end:]
            source_counter += 1

        return processed_text, url_indices_map

    # Extract all citation URLs
    citation_urls = []
    if response_obj.get("citations"):
        for citation in response_obj.get("citations", []):
            url_match = re.search(r"https?://\S+", citation)
            if url_match:
                citation_urls.append(url_match.group(0))

    # Section: Background
    bg_heading = doc.add_paragraph("Background")
    bg_heading.runs[0].bold = True

    # Add the background from the response with source links
    if response_obj.get("background"):
        background_text = response_obj.get("background")
        processed_bg, bg_url_map = process_text_with_urls(
            background_text, citation_urls
        )

        # Add the processed background text
        bg_para = doc.add_paragraph()
        parts = processed_bg.split(r"(source)")
        bg_para.add_run(parts[0])

        # Add hyperlinks for sources
        for i in range(1, len(parts)):
            # Find the source number for this occurrence
            source_num = i  # We'll use the position as the source number

            if source_num in bg_url_map:
                create_hyperlink(
                    bg_para,
                    bg_url_map[source_num],
                    f"(source)",
                    RGBColor(0, 0, 255),
                )
                bg_para.add_run(parts[i])
            else:
                bg_para.add_run(f"(source){parts[i]}")

    # Section: Analysis
    an_heading = doc.add_paragraph("Analysis")
    an_heading.runs[0].bold = True

    # Process the analysis text
    analysis_text = response_obj.get("analysis", "")
    if analysis_text:
        processed_analysis, analysis_url_map = process_text_with_urls(
            analysis_text, citation_urls
        )

        # Add the processed analysis text
        an_para = doc.add_paragraph()
        parts = processed_analysis.split(r"(source)")
        an_para.add_run(parts[0])

        # Add hyperlinks for sources
        for i in range(1, len(parts)):
            # Find the source number for this occurrence
            source_num = i  # We'll use the position as the source number

            if source_num in analysis_url_map:
                create_hyperlink(
                    an_para,
                    analysis_url_map[source_num],
                    f"(source)",
                    RGBColor(0, 0, 255),
                )
                an_para.add_run(parts[i])
            else:
                an_para.add_run(f"(source){parts[i]}")

    # Section: Conclusion
    if response_obj.get("conclusion"):
        co_heading = doc.add_paragraph("Conclusion")
        co_heading.runs[0].bold = True
        doc.add_paragraph(response_obj.get("conclusion"))

    # Section: Citations with hyperlinks
    if response_obj.get("citations"):
        ci_heading = doc.add_paragraph("Citations")
        ci_heading.runs[0].bold = True

        # Add each citation as a separate paragraph with hyperlink
        for i, citation in enumerate(response_obj.get("citations", [])):
            # Extract URL if present
            url_match = re.search(r"https?://\S+", citation)
            if url_match:
                url = url_match.group(0)
                # Create paragraph for the citation
                p = doc.add_paragraph()
                p.add_run(f"{i+1}. ")  # Add numbered citation

                # Add text before URL
                text_before_url = citation[: url_match.start()].strip()
                if text_before_url:
                    p.add_run(text_before_url + " ")

                # Add hyperlink
                try:
                    # Add a hyperlink run for the URL
                    hyperlink_run = p.add_run(url)
                    hyperlink_run.font.underline = True
                    hyperlink_run.font.color.rgb = RGBColor(0, 0, 255)  # Blue color

                    # Get relationship ID for the hyperlink
                    r_id = p.part.relate_to(
                        url,
                        docx.opc.constants.RELATIONSHIP_TYPE.HYPERLINK,
                        is_external=True,
                    )

                    # Create hyperlink XML element
                    hyperlink_elem = OxmlElement("w:hyperlink")
                    hyperlink_elem.set(qn("r:id"), r_id)

                    # Add the w:r element of the newly created run to the hyperlink
                    hyperlink_elem.append(hyperlink_run._element)

                    # Replace the original run with the hyperlink
                    p._p.append(hyperlink_elem)

                    logger.info(f"Added hyperlink to citation: {url}")
                except Exception as e:
                    log_exception(
                        logger, e, {"function": "generate_memo_docx", "url": url}
                    )
                    # If hyperlink creation fails, fall back to plain text
                    p.add_run(url)

                # Add any text after URL
                text_after_url = citation[url_match.end() :].strip()
                if text_after_url:
                    p.add_run(" " + text_after_url)
            else:
                # No URL found, add as plain text
                doc.add_paragraph(f"{i+1}. {citation}")

    # Import helper functions from accounting_chatbot
    from .accounting_chatbot import get_user_email_folder, generate_short_id

    # Get user folder and generate filename using the same structure as old implementation
    user_folder = get_user_email_folder(user)
    short_id = generate_short_id(6)
    filename = f"memo_{short_id}.docx"

    # Use the same file path structure as the old implementation
    file_path = f"memos/{user_folder}/{chat_session_id}/{filename}"

    # Create a temporary file to save the document
    with tempfile.NamedTemporaryFile(delete=False, suffix=".docx") as temp_file:
        doc.save(temp_file.name)
        temp_file_path = temp_file.name

    # Save to Django's storage
    with open(temp_file_path, "rb") as f:
        file_content = f.read()

    # Clean up the temporary file
    os.unlink(temp_file_path)

    # Save to Django's storage using the structured path
    path = default_storage.save(file_path, ContentFile(file_content))

    logger.info(f"Memo document saved to storage: {path}")
    logger.info(
        f"User folder: {user_folder}, Chat session: {chat_session_id}, Filename: {filename}"
    )
    return path


def sanitize_filename(filename):
    """
    Sanitize a filename by removing invalid characters and limiting length.

    Args:
        filename (str): The filename to sanitize

    Returns:
        str: Sanitized filename safe for file systems
    """
    # Remove invalid characters for file systems
    invalid_chars = '<>:"/\\|?*'
    for char in invalid_chars:
        filename = filename.replace(char, "-")

    # Replace multiple spaces with single space and strip
    filename = re.sub(r"\s+", " ", filename).strip()

    # Limit length to 100 characters (leaving room for extension)
    if len(filename) > 100:
        filename = filename[:100].strip()

    return filename


def generate_descriptive_filename(subject, query=None):
    """
    Generate a descriptive filename for memo attachments.

    Args:
        subject (str): The memo subject
        query (str, optional): The original user query

    Returns:
        str: Descriptive filename with .docx extension
    """
    # Start with the subject if available
    if subject and subject.strip():
        base_name = subject.strip()
        # Remove common prefixes
        prefixes_to_remove = [
            "Technical Memo:",
            "Tax Memo:",
            "Memo:",
            "Accounting Memo:",
        ]
        for prefix in prefixes_to_remove:
            if base_name.startswith(prefix):
                base_name = base_name[len(prefix) :].strip()
                break
    elif query and query.strip():
        # Fall back to truncated query
        base_name = query.strip()
        if len(base_name) > 50:
            base_name = base_name[:50].strip()
    else:
        # Default fallback
        base_name = "Tax-Accounting-Memo"

    # Sanitize the filename
    base_name = sanitize_filename(base_name)

    # Ensure we have a valid name
    if not base_name or base_name.isspace():
        base_name = "Tax-Accounting-Memo"

    # Add timestamp for uniqueness
    timestamp = datetime.now().strftime("%Y%m%d-%H%M")

    return f"memo-{base_name}-{timestamp}.docx"


def send_memo_email(
    to, subject, summary, file_content, filename, memo_subject=None, user_query=None
):
    """
    Send an email with the memo attached

    Args:
        to: Recipient email address (can be comma-separated for multiple recipients)
        subject: Email subject
        summary: Brief summary or response
        file_content: The binary content of the file to attach
        filename: The filename to use for the attachment
        memo_subject: The memo subject for generating descriptive filename
        user_query: The original user query for fallback filename generation

    Returns:
        bool: Whether the email was sent successfully
    """
    # Generate a descriptive filename for the attachment
    descriptive_filename = generate_descriptive_filename(
        memo_subject or subject, user_query
    )
    # Load email configuration from environment variables
    from_email = os.getenv("EMAIL_HOST_USER")
    app_password = os.getenv("EMAIL_HOST_PASSWORD")
    smtp_server = os.getenv("EMAIL_HOST", "smtp.hostinger.com")  # Default to hostinger
    smtp_port = int(os.getenv("EMAIL_PORT", "587"))  # Default to 587 for TLS
    email_enabled = (
        os.getenv("EMAIL_ENABLED", "True").lower() == "true"
    )  # Enable or disable email
    email_test_mode = (
        os.getenv("EMAIL_TEST_MODE", "False").lower() == "true"
    )  # Just log instead of sending

    # Check if email is enabled
    if not email_enabled:
        logger.info("Email sending is disabled by configuration")
        return False

    # Check if credentials are available
    if not from_email or not app_password:
        logger.error("Email credentials not available")
        return False

    # Handle multiple recipients
    if isinstance(to, str):
        recipients = [email.strip() for email in to.split(",") if email.strip()]
    else:
        recipients = [to] if to else []

    if not recipients:
        logger.error("No valid recipients provided")
        return False

    try:
        # Create the email message
        msg = EmailMessage()
        msg["Subject"] = subject
        msg["From"] = from_email
        msg["To"] = ", ".join(recipients)

        # Add the body
        msg.set_content(
            f"""
Here is the accounting/tax memo you requested.

{summary}

This is an automated message. Please do not reply.
"""
        )

        # Attach the file with descriptive filename
        msg.add_attachment(
            file_content,
            maintype="application",
            subtype="vnd.openxmlformats-officedocument.wordprocessingml.document",
            filename=descriptive_filename,
        )

        # Log SMTP configuration
        logger.info(
            f"Sending email to {', '.join(recipients)} using SMTP server {smtp_server}:{smtp_port}"
        )

        # If in test mode, just log the email instead of sending it
        if email_test_mode:
            logger.info(
                f"TEST MODE: Would send email to {', '.join(recipients)} with subject '{subject}'"
            )
            logger.info(
                f"TEST MODE: Email body contains summary and attachment: {descriptive_filename}"
            )
            return True

        # Send the email using SMTP
        try:
            # Use TLS directly with port 587
            with smtplib.SMTP(smtp_server, smtp_port) as smtp:
                smtp.starttls()
                smtp.login(from_email, app_password)
                smtp.send_message(msg)
                logger.info(f"Email sent successfully to {', '.join(recipients)}")
                return True
        except Exception as e:
            logger.error(f"Failed to send email: {str(e)}")
            raise

    except Exception as e:
        log_exception(
            logger,
            e,
            {
                "function": "send_memo_email",
                "to": to,
                "subject": subject,
                "filename": filename,
                "smtp_server": smtp_server,
                "smtp_port": smtp_port,
            },
        )
        return False


## END Helper functions #########################################################

################################################################################
## Pydantic BaseModel for outputs ###############################################
################################################################################


class ClassificationStructuredOutput(BaseModel):
    """
    Structured output for the classification agent.
    """

    is_valid: bool = Field(
        ...,
        description="true if the query relates to Accounting or Tax; false otherwise.",
    )
    category: Literal["Accounting", "Tax", "null"] = Field(
        ...,
        description='Classification label. MUST be one of: "Accounting", "Tax"; otherwise, "null"',
    )
    reasoning: str = Field(
        ...,
        description="Short explanation justifying the classification. Clear reasoning based on keywords, phrasing, or domain knowledge",
    )


class GrammarStructuredOutput(BaseModel):
    """
    Output model for the grammar correction agent.
    corrected_query: The grammatically corrected version of the input text.
    """

    corrected_query: str = Field(
        ...,
        description="The grammatically corrected version of the input text. Do not add any additional information.",
    )


# Not used right now - replaced by BAML client. See structured_outputs.baml
class MemoStructuredOutput(BaseModel):
    """
    Structured output model for generating professional tax and accounting memos.
    Organizes information into four key sections for clarity and comprehensiveness.
    """

    background: str = Field(
        ...,
        description="Summarize the question being asked and provide important contextual information needed to understand the inquiry. Identify the core issues and relevant circumstances.",
    )

    analysis: str = Field(
        ...,
        description="Provide a thorough technical explanation with references to authoritative sources. Include specific laws, standards, or regulations with section numbers. Present step-by-step reasoning, discuss exceptions or alternatives, include illustrative examples where helpful, and mention any audit or reporting implications.",
    )

    conclusion: str = Field(
        ...,
        description="Clearly restate the answer to the original question and summarize the key reasoning that supports this conclusion. Be concise but comprehensive.",
    )

    citations: List[str] = Field(
        ...,
        description="List of URLs to authoritative sources cited in the analysis. Do not include any additional information.",
    )


# Not used right now - replaced by BAML client. See structured_outputs.baml
class ConciseResponseOutput(BaseModel):
    """
    Structured output model for concise tax and accounting responses.
    Provides a direct answer with supporting citations.
    """

    summary: str = Field(
        ...,
        description="A clear, concise answer to the query that addresses the main points directly. Focus on what the user is asking for and provide answer strictly based on the tool call results. Do not add any additional information.",
    )

    citations: List[str] = Field(
        ...,
        description="List of URLs from the tool call results to authoritative sources that support the summary. Do not add any additional information.",
    )


## END Pydantic BaseModel for outputs ###########################################

################################################################################
## Function tools ##############################################################
################################################################################


# Specialized function tools that use google_search
@function_tool
async def search_federal_tax(query: str) -> str:
    """
    Search for Canadian federal tax information from official government sources.

    Args:
        query (str): The federal tax related search query.

    Returns:
        str: Formatted search results from authoritative Canadian federal tax sources.
    """
    logger.info(f"Searching for federal tax information: {query}")
    tax_sites = [
        "laws-lois.justice.gc.ca/eng/acts/E-15/",
        "canada.ca/en/revenue-agency/services/forms-publications/publications.html?q=GST%2FHST+Memoranda",
        "canada.ca/en/revenue-agency/services/tax/businesses/topics/interpretation-bulletins.html",
        "laws-lois.justice.gc.ca/eng/acts/I-3.3/",
    ]
    if USE_TAVILY_SEARCH:
        return tavily_search(query, site_restrictions=tax_sites)
    else:
        return google_search(query, site_restrictions=tax_sites)


@function_tool
async def search_provincial_sales_tax(query: str) -> str:
    """
    Search for Canadian provincial sales tax information from official provincial sources.

    Args:
        query (str): The provincial sales tax related search query.

    Returns:
        str: Formatted search results about Canadian provincial sales taxes (PST, RST, QST, HST).
    """
    logger.info(f"Searching for provincial sales tax information: {query}")
    pst_sites = [
        "gov.bc.ca",
        "gov.mb.ca",
        "saskatchewan.ca",
        "revenuquebec.ca",
        "ontario.ca",
        "gnb.ca",
        "novascotia.ca",
        "princeedwardisland.ca",
        "gov.nl.ca",
        "alberta.ca",
    ]
    if USE_TAVILY_SEARCH:
        return tavily_search(query, site_restrictions=pst_sites)
    else:
        return google_search(query, site_restrictions=pst_sites)


@function_tool
async def search_accounting_standards(query: str) -> str:
    """
    Search for Canadian accounting standards and guidelines.

    Args:
        query (str): The accounting standards related search query.

    Returns:
        str: Formatted search results for Canadian accounting standards (IFRS, ASPE, PSAS).
    """
    logger.info(f"Searching for accounting standards: {query}")
    accounting_sites = [
        "ifrs.org",
        "cpacanada.ca/en/business-and-accounting-resources/financial-and-non-financial-reporting/aspe",
        "frascanada.ca/en/public-sector-accounting-board",
        "fasb.org/standards",
    ]
    if USE_TAVILY_SEARCH:
        return tavily_search(query, site_restrictions=accounting_sites)
    else:
        return google_search(query, site_restrictions=accounting_sites)


## END Function tools ##########################################################

################################################################################
## Agents Definitions ##################################################
################################################################################

grammar_agent = Agent(
    name="Grammar_Agent",
    model=llm,
    instructions="""You are a grammar correction agent that:
    1. Fixes grammar and spelling issues in user queries
    2. Ensures the query is clear and well-formed
    Do not change the meaning or intent of the original query and do not add any additional information.
    """,
    output_type=GrammarStructuredOutput,
)

classification_agent = Agent(
    name="Classification_Agent",
    model=llm,
    instructions="""You are a user question category classification agent. The classification MUST be one of the following: "Accounting" or "Tax" or "null". Ensure the output strictly adheres to the ClassificationStructuredOutput schema. Use the field descriptions to guide your output. Do not include anything outside the schema.
    """,
    output_type=ClassificationStructuredOutput,
)

retriever_tax = Agent(
    name="Retriever_Tax",
    model=llm,
    instructions="""You are a specialized tax assistant that processes user queries by selecting the most appropriate tool from your available tools based on the query content. Your task is to:

    1. Analyze the user's query to determine which tool is most relevant
    2. Call that tool to retrieve information
    3. Use the tool's output to formulate the response in the ConciseResponseOutput format
    4. Include at least 3 most relevant URLs from the tool's output in the citations field.

    Important guidelines:
    - Make only ONE tool call - choose the most relevant tool based on the query
    - Do not make multiple tool calls - use the results from your single tool call
    - Do not add any text outside this structured format
    - Always include URLs from the retrieved content in your citations field.
    - Focus only on information relevant to the query.
    - If the retrieved information doesn't address the query, always acknowledge this in your response.
    - Be precise, accurate, and professional in your analysis.
    - Do not add any additional information.
    """,
    tools=[
        search_federal_tax,
        search_provincial_sales_tax,
    ],
    output_type=ConciseResponseOutput,  # output_type does not work while function calling is active with Gemini
)

retriever_accounting = Agent(
    name="Retriever_Accounting",
    model=llm,
    instructions="""You are a specialized accounting assistant that processes user queries by selecting the most appropriate tool from your available tools based on the query content. Your task is to:

    1. Analyze the user's query to determine which tool is most relevant
    2. Call that tool to retrieve information
    3. Use the tool's output to formulate the response in the ConciseResponseOutput format
    4. Include at least 3 most relevant URLs from the tool's output in the citations field.

    Important guidelines:
    - Make only ONE tool call - choose the most relevant tool based on the query
    - Do not make multiple tool calls - use the results from your single tool call
    - Do not add any text outside this structured format
    - Always include URLs from the retrieved content in your citations field.
    - Focus only on information relevant to the query.
    - If the retrieved information doesn't address the query, always acknowledge this in your response.
    - Be precise, accurate, and professional in your analysis.
    - Do not add any additional information.
    """,
    tools=[search_accounting_standards],
    output_type=ConciseResponseOutput,  # output_type does not work while function calling is active with Gemini
)

retriever_tax_as_memo = Agent(
    name="Retriever_Tax_as_Memo",
    model=llm,
    instructions="""You are a specialized tax assistant that processes user queries by selecting the most appropriate tool from your available tools based on the query content. Your task is to:

    1. Analyze the user's query to determine which tool is most relevant
    2. Call that tool to retrieve information
    3. Use the tool's output to formulate the response in the MemoStructuredOutput format
    4. Include at least 3 most relevant URLs from the tool's output in the citations field.

    Important guidelines:
    - Make only ONE tool call - choose the most relevant tool based on the query
    - Do not make multiple tool calls - use the results from your single tool call
    - Do not add any text outside this structured format
    - Always include URLs from the retrieved content in your citations field.
    - Focus only on information relevant to the query.
    - If the retrieved information doesn't address the query, always acknowledge this in your response.
    - Be precise, accurate, and professional in your analysis.
    - Do not add any additional information.
    """,
    tools=[
        search_federal_tax,
        search_provincial_sales_tax,
    ],
    output_type=MemoStructuredOutput,  # output_type does not work while function calling is active with Gemini
)

retriever_accounting_as_memo = Agent(
    name="Retriever_Accounting_as_Memo",
    model=llm,
    instructions="""You are a specialized accounting assistant that processes user queries by selecting the most appropriate tool from your available tools based on the query content. Your task is to:

    1. Analyze the user's query to determine which tool is most relevant
    2. Call that tool to retrieve information
    3. Use the tool's output to formulate the response in the MemoStructuredOutput format
    4. Include at least 3 most relevant URLs from the tool's output in the citations field.
    Important guidelines:
    - Make only ONE tool call - choose the most relevant tool based on the query
    - Do not make multiple tool calls - use the results from your single tool call
    - Do not add any text outside this structured format
    - Always include URLs from the retrieved content in your citations field.
    - Focus only on information relevant to the query.
    - If the retrieved information doesn't address the query, always acknowledge this in your response.
    - Be precise, accurate, and professional in your analysis.
    - Do not add any additional information.
    """,
    tools=[search_accounting_standards],
    output_type=MemoStructuredOutput,  # output_type does not work while function calling is active with Gemini
)


## END Agents Definitions #######################################################

################################################################################
## Main Workflow/Orchestration #################################################
################################################################################


# Instead of relying to LLMs (Triage Agent) to orchestrate the workflow, we will use a simple function to orchestrate the workflow. Calling LLMs to orchestrate the workflow will be slow and expensive. Too many back and forths. Red Flag! Why spend tokens on orchestration if we can do it with a simple function programmatically?


def format_response(response_obj, category):
    """
    Format the structured response for better readability in chat.

    Args:
        response_obj (dict): The structured response object
        category (str): The category of the response (Tax or Accounting)

    Returns:
        str: Formatted response string
    """
    logger.debug(f"Formatting {category} response")

    if not response_obj:
        logger.warning("No structured response object available")
        return "No structured response available."

    formatted_text = ""

    # Format for memo responses (more detailed)
    if (
        "background" in response_obj
        and "analysis" in response_obj
        and "conclusion" in response_obj
    ):
        logger.debug("Formatting detailed memo response")
        formatted_text += f"## {category} Memo\n\n"
        formatted_text += f"### Background\n{response_obj['background']}\n\n"
        formatted_text += f"### Analysis\n{response_obj['analysis']}\n\n"
        formatted_text += f"### Conclusion\n{response_obj['conclusion']}\n\n"

        if "citations" in response_obj and response_obj["citations"]:
            formatted_text += "### Sources\n"
            for citation in response_obj["citations"]:
                formatted_text += f"- {citation}\n"

    # Format for concise responses
    elif "summary" in response_obj:
        logger.debug("Formatting concise response")
        formatted_text += f"{response_obj['summary']}\n\n"

        if "citations" in response_obj and response_obj["citations"]:
            formatted_text += "### Sources\n"
            for citation in response_obj["citations"]:
                formatted_text += f"- {citation}\n"

    # Default case if structure doesn't match expected format
    else:
        logger.warning("Response object has unexpected structure")
        formatted_text = str(response_obj)

    logger.debug(f"Formatted response length: {len(formatted_text)}")
    return formatted_text


# Orchestrator function
async def orchestrator_function(
    query: str,
    memo_required: bool = False,
    email_required: bool = False,
    memo_details: Optional[dict] = None,
    user=None,
    chat_session_id=None,
):
    """
    Orchestrate the workflow for a user query.
    Args:
        query (str): The user's input query
        memo_required (bool, optional): Whether a memo is required. Defaults to False.
        email_required (bool, optional): Whether an email is required. Defaults to False.
        memo_details (Optional[dict], optional): Dictionary containing memo details including recipients. Defaults to None.

    Returns:
        dict: A dictionary containing the response and additional metadata
    """
    total_start_time = time.time()
    logger.info(f"Starting orchestration with query: {query}")
    logger.info(
        f"Parameters - memo_required: {memo_required}, email_required: {email_required}"
    )

    # Validate email format if email is required
    if email_required and memo_details:
        logger.info(f"Email recipients: {memo_details['recipients']}")

        email_pattern = r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"
        recipients = memo_details["recipients"].split(",")

        for email in recipients:
            email = email.strip()
            if not re.match(email_pattern, email):
                logger.error(f"Invalid email format: {email}")
                return {
                    "response": f"Invalid email format: {email}. Please provide valid comma-separated email addresses.",
                    "success": False,
                    "email_error": "Invalid email format",
                }

    # Check if we should use static response for testing
    if USE_STATIC_RESPONSE_FOR_TEST:
        logger.info("Using static response for test mode")

        # Prepare response with static text
        response_text = STATIC_RESPONSE
        memo_url = None
        email_sent = False
        email_error = None

        # Still create memo if requested
        if memo_required:
            # Create mock response object with the static text
            response_obj = {
                "background": "The question pertains to the accounting treatment of interest rate swaps for cash flow hedges under International Financial Reporting Standards (IFRS).",
                "analysis": "Under IFRS, the accounting treatment for interest rate swaps used as cash flow hedges involves recognizing the effective portion of the gain or loss on the swap in other comprehensive income (OCI) and the ineffective portion in profit or loss.",
                "conclusion": "In conclusion, under IFRS, interest rate swaps used as cash flow hedges are accounted for by recognizing the effective portion of the gain or loss in OCI and the ineffective portion in profit or loss.",
                "citations": [
                    "https://www.ifrs.org/issued-standards/list-of-standards/ifrs-9-financial-instruments/"
                ],
            }

            # Create metadata for the memo
            meta = {
                "company": "Accounting Advisory Services",
                "author": "MizuFlow Tax & Accounting AI Assistant",
                "date": datetime.now().strftime("%Y-%m-%d"),
                "subject": (query[:60] + "..." if len(query) > 60 else query),
            }

            # Generate the memo
            memo_file = generate_memo_docx(
                query, response_obj, meta, user, chat_session_id
            )
            logger.info(f"Memo generated and saved as: {memo_file}")

            # Set memo_url for return value - use same format as old implementation
            memo_url = memo_file.replace("memos/", "")
            logger.info(f"Generated memo URL: {memo_url} (from file path: {memo_file})")

            # Send email if required
            if email_required and memo_details and "recipients" in memo_details:
                # Get the file content from storage for email attachment
                try:
                    stored_file = default_storage.open(memo_file, "rb")
                    file_content = stored_file.read()
                    stored_file.close()

                    email_sent = send_memo_email(
                        to=memo_details["recipients"],
                        subject=f"Memo: {meta['subject']}",
                        summary=f"Please find attached a memo regarding: {query}",
                        file_content=file_content,
                        filename=os.path.basename(memo_file),
                        memo_subject=meta["subject"],
                        user_query=query,
                    )
                except Exception as e:
                    logger.error(f"Error reading memo file for email: {str(e)}")
                    email_sent = False
                logger.info(f"Email sending result: {email_sent}")
                if not email_sent:
                    email_error = "Failed to send email"
                    logger.error(f"Email sending failed: {email_error}")

        total_time = time.time() - total_start_time
        logger.info(
            f"Orchestration completed with static response in {total_time:.2f} seconds"
        )
        return {
            "response": response_text,
            "success": True,
            "category": "Accounting",
            "memo_url": memo_url,
            "email_sent": email_sent,
            "email_error": email_error,
        }

    original_query = query
    # Step 1: Grammar agent fixes grammar
    logger.info("Processing through Grammar Agent")
    grammar_start_time = time.time()
    grammar_result = await Runner.run(starting_agent=grammar_agent, input=query)
    grammar_time = time.time() - grammar_start_time
    corrected_query = grammar_result.final_output.corrected_query
    logger.info(f"Grammar Agent Result: {corrected_query}")
    logger.info(f"Grammar Agent processing time: {grammar_time:.2f} seconds")

    # Step 2: Classification agent classifies the query
    logger.info("Processing through Classification Agent")
    classification_start_time = time.time()
    classification_result = await Runner.run(
        starting_agent=classification_agent,
        input=corrected_query,
    )
    classification_time = time.time() - classification_start_time

    # Extract classification information
    is_valid = classification_result.final_output.is_valid
    category = classification_result.final_output.category
    reasoning = classification_result.final_output.reasoning

    logger.info(f"Classification results - is_valid: {is_valid}, category: {category}")
    logger.info(
        f"Classification Agent processing time: {classification_time:.2f} seconds"
    )
    logger.debug(f"Classification reasoning: {reasoning}")

    # Only proceed if the query is valid
    if not is_valid:
        error_msg = f"Sorry, I can only answer questions related to Accounting or Tax. This query is not related to Accounting or Tax. {reasoning}"
        logger.info(f"Query not valid for processing: {reasoning}")
        total_time = time.time() - total_start_time
        logger.info(
            f"Orchestration completed (invalid query) in {total_time:.2f} seconds"
        )
        return {
            "response": error_msg,
            "memo_url": None,
            "email_sent": False,
        }

    # Step 3: Check if category is Tax or Accounting
    response_text = ""
    response_obj = {}
    memo_url = None
    email_sent = False
    email_error = None

    if category == "Tax":
        if memo_required:
            # Step 3.1: Retrieve tax information
            logger.info("Processing Tax query in Memo format")
            retrieval_start_time = time.time()
            agent_result = await Runner.run(
                starting_agent=retriever_tax_as_memo, input=corrected_query
            )
            retrieval_time = time.time() - retrieval_start_time
            logger.info(
                f"Tax retrieval processing time in Memo format: {retrieval_time:.2f} seconds"
            )
            response_obj: dict = agent_result.final_output.model_dump()
            # Format the response text
            response_text = format_response(response_obj, category)

            # Create metadata for the memo
            # TODO: Add metadata from the memo_details
            meta = {
                "company": "Tax Advisory Services",
                "author": "MizuFlow Tax & Accounting AI Assistant",
                "date": datetime.now().strftime("%Y-%m-%d"),
                "subject": (
                    corrected_query[:60] + "..."
                    if len(corrected_query) > 60
                    else corrected_query
                ),
            }
            memo_start_time = time.time()
            memo_path = generate_memo_docx(
                corrected_query, response_obj, meta, user, chat_session_id
            )
            memo_time = time.time() - memo_start_time
            logger.info(f"Tax memo generation time: {memo_time:.2f} seconds")
            logger.info(f"Tax memo generated: {memo_path}")

            # Set memo_url for return value - use same format as old implementation
            memo_url = memo_path.replace("memos/", "")
            logger.info(
                f"Generated Tax memo URL: {memo_url} (from file path: {memo_path})"
            )

            # Send email if required
            if email_required and memo_details and "recipients" in memo_details:
                logger.info(f"Sending Tax memo email to: {memo_details['recipients']}")
                email_start_time = time.time()
                # Get the file content from storage for email attachment
                try:
                    stored_file = default_storage.open(memo_path, "rb")
                    file_content = stored_file.read()
                    stored_file.close()

                    email_sent = send_memo_email(
                        to=memo_details["recipients"],
                        subject=f"Tax Memo: {meta['subject']}",
                        summary=f"Please find attached a tax memo regarding: {corrected_query}",
                        file_content=file_content,
                        filename=os.path.basename(memo_path),
                        memo_subject=meta["subject"],
                        user_query=corrected_query,
                    )
                except Exception as e:
                    logger.error(f"Error reading tax memo file for email: {str(e)}")
                    email_sent = False
                email_time = time.time() - email_start_time
                logger.info(f"Email sending time: {email_time:.2f} seconds")
                if not email_sent:
                    email_error = "Failed to send email"
                    logger.error(f"Failed to send Tax memo email: {email_error}")
        else:
            logger.info("Processing Tax query in normal response format")
            retrieval_start_time = time.time()
            agent_result = await Runner.run(
                starting_agent=retriever_tax, input=corrected_query
            )
            retrieval_time = time.time() - retrieval_start_time
            logger.info(
                f"Tax retrieval processing time in normal response format: {retrieval_time:.2f} seconds"
            )
            response_obj: dict = agent_result.final_output.model_dump()
            # Format the response text
            response_text = format_response(response_obj, category)

    elif category == "Accounting":
        if memo_required:
            # Step 3.2: Retrieve accounting information
            logger.info("Processing Accounting query in Memo format")
            retrieval_start_time = time.time()
            agent_result = await Runner.run(
                starting_agent=retriever_accounting_as_memo, input=corrected_query
            )
            retrieval_time = time.time() - retrieval_start_time
            logger.info(
                f"Accounting retrieval processing time in Memo format: {retrieval_time:.2f} seconds"
            )
            response_obj: dict = agent_result.final_output.model_dump()
            # Format the response text
            response_text = format_response(response_obj, category)

            # Create metadata for the memo
            meta = {
                "company": "Accounting Advisory Services",
                "author": "MizuFlow Tax & Accounting AI Assistant",
                "date": datetime.now().strftime("%Y-%m-%d"),
                "subject": (
                    original_query[:60] + "..."
                    if len(original_query) > 60
                    else original_query
                ),
            }
            memo_start_time = time.time()
            memo_path = generate_memo_docx(
                corrected_query, response_obj, meta, user, chat_session_id
            )
            memo_time = time.time() - memo_start_time
            logger.info(f"Accounting memo generation time: {memo_time:.2f} seconds")
            logger.info(f"Accounting memo generated: {memo_path}")

            # Set memo_url for return value - use same format as old implementation
            memo_url = memo_path.replace("memos/", "")
            logger.info(
                f"Generated Accounting memo URL: {memo_url} (from file path: {memo_path})"
            )

            # Send email if required
            if email_required and memo_details and "recipients" in memo_details:
                logger.info(
                    f"Sending Accounting memo email to: {memo_details['recipients']}"
                )
                email_start_time = time.time()
                # Get the file content from storage for email attachment
                try:
                    stored_file = default_storage.open(memo_path, "rb")
                    file_content = stored_file.read()
                    stored_file.close()

                    email_sent = send_memo_email(
                        to=memo_details["recipients"],
                        subject=f"Memo: {meta['subject']}",
                        summary=f"Please find attached a memo regarding: {original_query}",
                        file_content=file_content,
                        filename=os.path.basename(memo_path),
                        memo_subject=meta["subject"],
                        user_query=original_query,
                    )
                except Exception as e:
                    logger.error(
                        f"Error reading accounting memo file for email: {str(e)}"
                    )
                    email_sent = False
                email_time = time.time() - email_start_time
                logger.info(f"Email sending time: {email_time:.2f} seconds")
                if not email_sent:
                    email_error = "Failed to send email"
                    logger.error(f"Failed to send Accounting memo email: {email_error}")
        else:
            logger.info("Processing Accounting query in normal response format")
            retrieval_start_time = time.time()
            agent_result = await Runner.run(
                starting_agent=retriever_accounting, input=corrected_query
            )
            retrieval_time = time.time() - retrieval_start_time
            logger.info(
                f"Accounting retrieval processing time in normal response format: {retrieval_time:.2f} seconds"
            )
            response_obj: dict = agent_result.final_output.model_dump()
            # Format the response text
            response_text = format_response(response_obj, category)

    # Return a response dictionary in the format expected by the view
    result = {
        "response": response_text,
        "success": True,
        "category": category,
        "memo_url": memo_url,
        "email_sent": email_sent,
        "email_error": email_error if not email_sent and email_required else None,
    }

    total_time = time.time() - total_start_time
    logger.info(
        f"Orchestration completed for {category} query in {total_time:.2f} seconds"
    )
    return result


## END Main Workflow ###########################################################
