# Generated by Django 5.2 on 2025-05-02 20:49

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='ChatConversation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('user_id', models.Char<PERSON><PERSON>(default='anonymous', max_length=255)),
                ('chat_session_id', models.CharField(blank=True, db_index=True, max_length=255, null=True)),
                ('parent_id', models.IntegerField(blank=True, null=True)),
                ('original_question', models.TextField()),
                ('corrected_question', models.TextField()),
                ('category', models.CharField(blank=True, max_length=50)),
                ('response', models.TextField()),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('memo_url', models.Cha<PERSON><PERSON><PERSON>(blank=True, max_length=255, null=True)),
            ],
            options={
                'ordering': ['-timestamp'],
                'indexes': [models.Index(fields=['user_id', 'timestamp'], name='tax_acc_ass_user_id_93c4d5_idx'), models.Index(fields=['chat_session_id', 'timestamp'], name='tax_acc_ass_chat_se_65b4d6_idx')],
            },
        ),
    ]
