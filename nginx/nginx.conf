upstream django_app {
    server mizu_web:8004;
}

server {
    listen 8005;
    server_name _;
    client_max_body_size 100M;

    # Force HTTPS for all requests
    if ($http_x_forwarded_proto = "http") {
        return 301 https://$host$request_uri;
    }

    # Better proxy settings
    location / {
        proxy_pass http://django_app;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header Host $http_host;
        proxy_set_header X-CSRFToken $http_x_csrftoken;

        # Ensure cookies are passed properly
        proxy_cookie_path / /;
        # proxy_cookie_domain django_app $host;

        proxy_redirect off;
        proxy_buffering off;

        # Increased timeouts for this specific endpoint
        proxy_connect_timeout 600s;
        proxy_send_timeout 600s;
        proxy_read_timeout 600s;
    }

    location /static/ {
        alias /app/staticfiles/;
        access_log off;
        expires 30d;
        add_header Cache-Control "public, no-transform";
    }

    location /media/ {
        alias /app/media/;
        access_log off;
        expires 30d;
        add_header Cache-Control "public, no-transform";
        add_header Content-Disposition "attachment";
        add_header X-Content-Type-Options "nosniff";
        add_header Access-Control-Allow-Origin "*";
        add_header Access-Control-Allow-Methods "GET, OPTIONS";
        add_header Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept";
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    }
} 