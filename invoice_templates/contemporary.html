<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice - {{ invoice_number }}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.7;
            color: #1f2937;
            background: #ffffff;
            font-size: 14px;
        }
        
        .invoice-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 40px;
            background: white;
            position: relative;
        }
        
        /* Accent Bar */
        .accent-bar {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 6px;
            background: linear-gradient(90deg, #7c3aed 0%, #a855f7 100%);
        }
        
        /* Header Section */
        .header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 50px;
            margin-top: 20px;
        }
        
        .company-logo-section {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .company-logo {
            max-width: 180px;
            max-height: 70px;
        }
        
        .company-brand {
            background: linear-gradient(135deg, #7c3aed, #a855f7);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 24px;
            font-weight: 700;
        }
        
        .company-info {
            text-align: right;
            color: #6b7280;
            max-width: 250px;
        }
        
        .company-details {
            font-size: 13px;
            line-height: 1.6;
        }
        
        /* Invoice Title */
        .invoice-title {
            font-size: 42px;
            font-weight: 300;
            color: #1f2937;
            margin-bottom: 50px;
            position: relative;
        }
        
        .invoice-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 0;
            width: 80px;
            height: 3px;
            background: linear-gradient(90deg, #7c3aed, #a855f7);
        }
        
        /* Client and Invoice Info Section */
        .info-section {
            display: flex;
            justify-content: space-between;
            margin-bottom: 50px;
            gap: 30px;
        }
        
        .client-info, .invoice-meta {
            width: 48%;
            position: relative;
        }
        
        .info-card {
            background: #f3f4f6;
            padding: 25px;
            border-radius: 12px;
            border-left: 4px solid #7c3aed;
            position: relative;
            overflow: hidden;
        }
        
        .info-card::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #7c3aed20, #a855f720);
            border-radius: 0 12px 0 60px;
        }
        
        .section-title {
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            color: #7c3aed;
            margin-bottom: 15px;
        }
        
        .client-name {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 10px;
        }
        
        .client-details, .invoice-details {
            color: #4b5563;
            font-size: 13px;
            line-height: 1.6;
        }
        
        .invoice-number {
            font-size: 20px;
            font-weight: 600;
            color: #7c3aed;
            margin-bottom: 10px;
        }
        
        /* Line Items Table */
        .line-items {
            margin-bottom: 40px;
        }
        
        .items-table {
            width: 100%;
            border-collapse: collapse;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        
        .items-table th {
            background: linear-gradient(135deg, #7c3aed, #a855f7);
            color: white;
            padding: 18px 15px;
            text-align: left;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .items-table td {
            padding: 16px 15px;
            border-bottom: 1px solid #f3f4f6;
            color: #1f2937;
            background: white;
        }
        
        .items-table tr:nth-child(even) td {
            background: #fafafa;
        }
        
        .items-table tr:last-child td {
            border-bottom: none;
        }
        
        .text-right {
            text-align: right;
        }
        
        .item-description {
            font-weight: 500;
        }
        
        /* Totals Section */
        .totals-section {
            display: flex;
            justify-content: flex-end;
            margin-bottom: 50px;
        }
        
        .totals {
            width: 320px;
            background: #f9fafb;
            border-radius: 12px;
            padding: 20px;
            border: 1px solid #e5e7eb;
        }
        
        .total-row {
            display: flex;
            justify-content: space-between;
            padding: 10px 0;
            font-size: 14px;
        }
        
        .total-row.subtotal {
            color: #6b7280;
            border-bottom: 1px solid #e5e7eb;
            padding-bottom: 15px;
            margin-bottom: 10px;
        }
        
        .total-row.final {
            font-size: 20px;
            font-weight: 600;
            color: white;
            background: linear-gradient(135deg, #7c3aed, #a855f7);
            margin: 15px -20px -20px;
            padding: 20px;
            border-radius: 0 0 12px 12px;
        }
        
        /* Footer */
        .footer {
            text-align: center;
            color: #6b7280;
            font-size: 12px;
            line-height: 1.6;
            background: #f9fafb;
            padding: 25px;
            border-radius: 12px;
            border-top: 3px solid #7c3aed;
        }
        
        .payment-terms {
            margin-bottom: 15px;
            font-weight: 600;
            color: #1f2937;
        }
        
        .footer-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        
        .footer-item {
            text-align: center;
        }
        
        /* Print Styles */
        @media print {
            body {
                font-size: 12px;
            }
            
            .invoice-container {
                padding: 20px;
            }
            
            .accent-bar {
                height: 4px;
            }
            
            .header {
                margin-bottom: 30px;
            }
            
            .invoice-title {
                font-size: 32px;
                margin-bottom: 30px;
            }
            
            .info-section {
                margin-bottom: 30px;
            }
            
            .items-table {
                box-shadow: none;
            }
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .invoice-container {
                padding: 20px;
            }
            
            .header {
                flex-direction: column;
                gap: 20px;
            }
            
            .company-info {
                text-align: left;
                max-width: none;
            }
            
            .invoice-title {
                font-size: 32px;
            }
            
            .info-section {
                flex-direction: column;
                gap: 20px;
            }
            
            .client-info, .invoice-meta {
                width: 100%;
            }
            
            .totals {
                width: 100%;
            }
            
            .footer-grid {
                grid-template-columns: 1fr;
                gap: 10px;
            }
            
            .items-table th,
            .items-table td {
                padding: 12px 8px;
                font-size: 12px;
            }
        }
    </style>
</head>
<body>
    <div class="invoice-container">
        <!-- Accent Bar -->
        <div class="accent-bar"></div>
        
        <!-- Header -->
        <div class="header">
            <div class="company-logo-section">
                {% if company.logo %}
                    <img src="{{ company.logo }}" alt="{{ company.company_name }}" class="company-logo">
                {% else %}
                    <div class="company-brand">{{ company.company_name }}</div>
                {% endif %}
            </div>
            <div class="company-info">
                {% if not company.logo %}
                    <div class="company-brand">{{ company.company_name }}</div>
                {% endif %}
                <div class="company-details">
                    {% if company.address_line_1 %}{{ company.address_line_1 }}<br>{% endif %}
                    {% if company.address_line_2 %}{{ company.address_line_2 }}<br>{% endif %}
                    {% if company.city %}{{ company.city }}{% endif %}{% if company.state_province %}, {{ company.state_province }}{% endif %}{% if company.postal_code %} {{ company.postal_code }}{% endif %}<br>
                    {% if company.country %}{{ company.country }}<br>{% endif %}
                    {% if company.phone %}{{ company.phone }}<br>{% endif %}
                    {% if company.email %}{{ company.email }}<br>{% endif %}
                    {% if company.website %}{{ company.website }}{% endif %}
                </div>
            </div>
        </div>
        
        <!-- Invoice Title -->
        <h1 class="invoice-title">Invoice</h1>
        
        <!-- Client and Invoice Info -->
        <div class="info-section">
            <div class="client-info">
                <div class="info-card">
                    <div class="section-title">Bill To</div>
                    <div class="client-name">{{ client.name }}</div>
                    <div class="client-details">
                        {% if client.address %}{{ client.address }}<br>{% endif %}
                        {% if client.email %}{{ client.email }}<br>{% endif %}
                        {% if client.phone %}{{ client.phone }}{% endif %}
                    </div>
                </div>
            </div>
            <div class="invoice-meta">
                <div class="info-card">
                    <div class="section-title">Invoice Details</div>
                    <div class="invoice-number">#{{ invoice.number }}</div>
                    <div class="invoice-details">
                        <strong>Date:</strong> {{ invoice.date }}<br>
                        <strong>Due Date:</strong> {{ invoice.due_date }}<br>
                        {% if invoice.po_number %}<strong>PO Number:</strong> {{ invoice.po_number }}{% endif %}
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Line Items -->
        <div class="line-items">
            <table class="items-table">
                <thead>
                    <tr>
                        <th>Description</th>
                        <th class="text-right">Qty</th>
                        <th class="text-right">Rate</th>
                        <th class="text-right">Amount</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in invoice.items %}
                    <tr>
                        <td class="item-description">{{ item.description }}</td>
                        <td class="text-right">{{ item.quantity }}</td>
                        <td class="text-right">${{ item.rate|floatformat:2 }}</td>
                        <td class="text-right">${{ item.amount|floatformat:2 }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- Totals -->
        <div class="totals-section">
            <div class="totals">
                <div class="total-row subtotal">
                    <span>Subtotal:</span>
                    <span>${{ invoice.subtotal|floatformat:2 }}</span>
                </div>
                {% if invoice.tax_amount %}
                <div class="total-row">
                    <span>Tax ({{ invoice.tax_rate }}%):</span>
                    <span>${{ invoice.tax_amount|floatformat:2 }}</span>
                </div>
                {% endif %}
                {% if invoice.discount_amount %}
                <div class="total-row">
                    <span>Discount:</span>
                    <span>-${{ invoice.discount_amount|floatformat:2 }}</span>
                </div>
                {% endif %}
                <div class="total-row final">
                    <span>Total:</span>
                    <span>${{ invoice.total|floatformat:2 }}</span>
                </div>
            </div>
        </div>
        
        <!-- Footer -->
        <div class="footer">
            <div class="payment-terms">
                Payment Terms: {{ company.default_payment_terms|default:"Net 30 days" }}
            </div>
            <div class="footer-grid">
                {% if company.bank_name %}
                <div class="footer-item">
                    <strong>Banking</strong><br>
                    {{ company.bank_name }}<br>
                    {% if company.account_number %}Acc: {{ company.account_number }}{% endif %}
                </div>
                {% endif %}
                {% if company.tax_id %}
                <div class="footer-item">
                    <strong>Tax ID</strong><br>
                    {{ company.tax_id }}
                </div>
                {% endif %}
                {% if company.website %}
                <div class="footer-item">
                    <strong>Web</strong><br>
                    {{ company.website }}
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</body>
</html>
