<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice - {{ invoice_number }}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Times New Roman', Times, serif;
            line-height: 1.5;
            color: #111827;
            background: #ffffff;
            font-size: 14px;
        }
        
        .invoice-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 40px;
            background: white;
            border: 2px solid #1f2937;
        }
        
        /* Header Section */
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 30px 0;
            border-bottom: 3px solid #1f2937;
        }
        
        .company-logo {
            max-width: 180px;
            max-height: 70px;
            margin-bottom: 15px;
        }
        
        .company-name {
            font-size: 28px;
            font-weight: bold;
            color: #1f2937;
            margin-bottom: 10px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .company-details {
            font-size: 13px;
            color: #6b7280;
            line-height: 1.4;
        }
        
        /* Invoice Title */
        .invoice-title {
            font-size: 36px;
            font-weight: bold;
            color: #1f2937;
            text-align: center;
            margin-bottom: 30px;
            text-transform: uppercase;
            letter-spacing: 2px;
            border-bottom: 1px solid #d1d5db;
            padding-bottom: 15px;
        }
        
        /* Client and Invoice Info Section */
        .info-section {
            display: flex;
            justify-content: space-between;
            margin-bottom: 40px;
        }
        
        .client-info, .invoice-meta {
            width: 45%;
            padding: 20px;
            border: 1px solid #d1d5db;
            background: #f9fafb;
        }
        
        .section-title {
            font-size: 14px;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            color: #1f2937;
            margin-bottom: 15px;
            border-bottom: 2px solid #1f2937;
            padding-bottom: 5px;
        }
        
        .client-name {
            font-size: 16px;
            font-weight: bold;
            color: #1f2937;
            margin-bottom: 10px;
        }
        
        .client-details, .invoice-details {
            color: #374151;
            font-size: 13px;
            line-height: 1.6;
        }
        
        .invoice-number {
            font-size: 18px;
            font-weight: bold;
            color: #1f2937;
            margin-bottom: 10px;
        }
        
        /* Line Items Table */
        .line-items {
            margin-bottom: 30px;
        }
        
        .items-table {
            width: 100%;
            border-collapse: collapse;
            border: 2px solid #1f2937;
        }
        
        .items-table th {
            background: #1f2937;
            color: white;
            padding: 15px 12px;
            text-align: left;
            font-size: 13px;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            border-right: 1px solid #374151;
        }
        
        .items-table th:last-child {
            border-right: none;
        }
        
        .items-table td {
            padding: 12px;
            border-bottom: 1px solid #d1d5db;
            border-right: 1px solid #d1d5db;
            color: #1f2937;
        }
        
        .items-table td:last-child {
            border-right: none;
        }
        
        .items-table tr:nth-child(even) {
            background: #f9fafb;
        }
        
        .text-right {
            text-align: right;
        }
        
        .item-description {
            font-weight: 500;
        }
        
        /* Totals Section */
        .totals-section {
            display: flex;
            justify-content: flex-end;
            margin-bottom: 40px;
        }
        
        .totals {
            width: 350px;
            border: 2px solid #1f2937;
            background: #f9fafb;
        }
        
        .total-row {
            display: flex;
            justify-content: space-between;
            padding: 12px 20px;
            font-size: 14px;
            border-bottom: 1px solid #d1d5db;
        }
        
        .total-row:last-child {
            border-bottom: none;
        }
        
        .total-row.subtotal {
            color: #6b7280;
            font-weight: 500;
        }
        
        .total-row.final {
            font-size: 18px;
            font-weight: bold;
            color: white;
            background: #1f2937;
        }
        
        /* Footer */
        .footer {
            text-align: left;
            color: #374151;
            font-size: 12px;
            line-height: 1.6;
            border-top: 2px solid #1f2937;
            padding-top: 20px;
        }
        
        .payment-terms {
            margin-bottom: 15px;
            font-weight: bold;
            color: #1f2937;
        }
        
        .footer-section {
            margin-bottom: 10px;
        }
        
        /* Print Styles */
        @media print {
            body {
                font-size: 12px;
            }
            
            .invoice-container {
                padding: 20px;
                border: 1px solid #1f2937;
            }
            
            .header {
                margin-bottom: 30px;
                padding: 20px 0;
            }
            
            .invoice-title {
                font-size: 30px;
                margin-bottom: 20px;
            }
            
            .info-section {
                margin-bottom: 30px;
            }
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .invoice-container {
                padding: 20px;
                border: 1px solid #1f2937;
            }
            
            .header {
                padding: 20px 0;
            }
            
            .company-name {
                font-size: 22px;
            }
            
            .invoice-title {
                font-size: 28px;
            }
            
            .info-section {
                flex-direction: column;
                gap: 20px;
            }
            
            .client-info, .invoice-meta {
                width: 100%;
            }
            
            .totals {
                width: 100%;
            }
            
            .items-table th,
            .items-table td {
                padding: 8px 6px;
                font-size: 12px;
            }
        }
    </style>
</head>
<body>
    <div class="invoice-container">
        <!-- Header -->
        <div class="header">
            {% if company.logo %}
                <img src="{{ company.logo }}" alt="{{ company.company_name }}" class="company-logo">
            {% endif %}
            <div class="company-name">{{ company.company_name }}</div>
            <div class="company-details">
                {% if company.address_line_1 %}{{ company.address_line_1 }}{% endif %}{% if company.address_line_2 %}, {{ company.address_line_2 }}{% endif %}<br>
                {% if company.city %}{{ company.city }}{% endif %}{% if company.state_province %}, {{ company.state_province }}{% endif %}{% if company.postal_code %} {{ company.postal_code }}{% endif %}{% if company.country %}, {{ company.country }}{% endif %}<br>
                {% if company.phone %}Tel: {{ company.phone }}{% endif %}{% if company.email %} | Email: {{ company.email }}{% endif %}<br>
                {% if company.website %}{{ company.website }}{% endif %}
            </div>
        </div>
        
        <!-- Invoice Title -->
        <h1 class="invoice-title">Invoice</h1>
        
        <!-- Client and Invoice Info -->
        <div class="info-section">
            <div class="client-info">
                <div class="section-title">Bill To</div>
                <div class="client-name">{{ client.name }}</div>
                <div class="client-details">
                    {% if client.address %}{{ client.address }}<br>{% endif %}
                    {% if client.email %}Email: {{ client.email }}<br>{% endif %}
                    {% if client.phone %}Phone: {{ client.phone }}{% endif %}
                </div>
            </div>
            <div class="invoice-meta">
                <div class="section-title">Invoice Information</div>
                <div class="invoice-number">Invoice #{{ invoice.number }}</div>
                <div class="invoice-details">
                    <strong>Invoice Date:</strong> {{ invoice.date }}<br>
                    <strong>Due Date:</strong> {{ invoice.due_date }}<br>
                    {% if invoice.po_number %}<strong>Purchase Order:</strong> {{ invoice.po_number }}<br>{% endif %}
                    <strong>Terms:</strong> {{ company.default_payment_terms|default:"Net 30 days" }}
                </div>
            </div>
        </div>
        
        <!-- Line Items -->
        <div class="line-items">
            <table class="items-table">
                <thead>
                    <tr>
                        <th>Description of Services/Products</th>
                        <th class="text-right">Quantity</th>
                        <th class="text-right">Unit Price</th>
                        <th class="text-right">Total Amount</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in invoice.items %}
                    <tr>
                        <td class="item-description">{{ item.description }}</td>
                        <td class="text-right">{{ item.quantity }}</td>
                        <td class="text-right">${{ item.rate|floatformat:2 }}</td>
                        <td class="text-right">${{ item.amount|floatformat:2 }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- Totals -->
        <div class="totals-section">
            <div class="totals">
                <div class="total-row subtotal">
                    <span>Subtotal:</span>
                    <span>${{ invoice.subtotal|floatformat:2 }}</span>
                </div>
                {% if invoice.tax_amount %}
                <div class="total-row">
                    <span>Tax ({{ invoice.tax_rate }}%):</span>
                    <span>${{ invoice.tax_amount|floatformat:2 }}</span>
                </div>
                {% endif %}
                {% if invoice.discount_amount %}
                <div class="total-row">
                    <span>Discount:</span>
                    <span>-${{ invoice.discount_amount|floatformat:2 }}</span>
                </div>
                {% endif %}
                <div class="total-row final">
                    <span><strong>TOTAL DUE:</strong></span>
                    <span><strong>${{ invoice.total|floatformat:2 }}</strong></span>
                </div>
            </div>
        </div>
        
        <!-- Footer -->
        <div class="footer">
            <div class="payment-terms">
                PAYMENT TERMS: {{ company.default_payment_terms|default:"Net 30 days" }}
            </div>
            {% if company.bank_name %}
            <div class="footer-section">
                <strong>REMIT PAYMENT TO:</strong><br>
                Bank: {{ company.bank_name }}<br>
                {% if company.account_number %}Account Number: {{ company.account_number }}<br>{% endif %}
                {% if company.routing_number %}Routing Number: {{ company.routing_number }}<br>{% endif %}
                {% if company.swift_code %}SWIFT Code: {{ company.swift_code }}{% endif %}
            </div>
            {% endif %}
            {% if company.tax_id %}
            <div class="footer-section">
                <strong>TAX IDENTIFICATION:</strong> {{ company.tax_id }}
            </div>
            {% endif %}
            {% if company.business_registration %}
            <div class="footer-section">
                <strong>BUSINESS REGISTRATION:</strong> {{ company.business_registration }}
            </div>
            {% endif %}
        </div>
    </div>
</body>
</html>
