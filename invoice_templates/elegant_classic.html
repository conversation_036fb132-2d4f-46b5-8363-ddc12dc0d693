<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice - {{ invoice_number }}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Playfair Display', Georgia, serif;
            line-height: 1.7;
            color: #111827;
            background: #ffffff;
            font-size: 14px;
        }
        
        .invoice-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 50px;
            background: white;
            border: 1px solid #d1d5db;
            position: relative;
        }
        
        /* Decorative Elements */
        .decorative-line {
            height: 3px;
            background: linear-gradient(90deg, #d97706, #f59e0b, #d97706);
            margin-bottom: 30px;
        }
        
        .corner-decoration {
            position: absolute;
            width: 40px;
            height: 40px;
            border: 2px solid #d97706;
        }
        
        .corner-decoration.top-left {
            top: 20px;
            left: 20px;
            border-right: none;
            border-bottom: none;
        }
        
        .corner-decoration.top-right {
            top: 20px;
            right: 20px;
            border-left: none;
            border-bottom: none;
        }
        
        .corner-decoration.bottom-left {
            bottom: 20px;
            left: 20px;
            border-right: none;
            border-top: none;
        }
        
        .corner-decoration.bottom-right {
            bottom: 20px;
            right: 20px;
            border-left: none;
            border-top: none;
        }
        
        /* Header Section */
        .header {
            text-align: center;
            margin-bottom: 45px;
            padding: 30px 0;
            border-bottom: 1px solid #d1d5db;
            position: relative;
        }
        
        .company-logo {
            max-width: 200px;
            max-height: 80px;
            margin-bottom: 20px;
        }
        
        .company-name {
            font-size: 32px;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 15px;
            letter-spacing: 1px;
        }
        
        .company-tagline {
            font-style: italic;
            color: #d97706;
            font-size: 16px;
            margin-bottom: 15px;
        }
        
        .company-details {
            font-family: 'Source Sans Pro', sans-serif;
            font-size: 13px;
            color: #374151;
            line-height: 1.5;
        }
        
        /* Invoice Title */
        .invoice-title {
            font-size: 48px;
            font-weight: 400;
            color: #1f2937;
            text-align: center;
            margin-bottom: 45px;
            position: relative;
            font-style: italic;
        }
        
        .invoice-title::before,
        .invoice-title::after {
            content: '';
            position: absolute;
            top: 50%;
            width: 80px;
            height: 1px;
            background: #d97706;
        }
        
        .invoice-title::before {
            left: -100px;
        }
        
        .invoice-title::after {
            right: -100px;
        }
        
        /* Client and Invoice Info Section */
        .info-section {
            display: flex;
            justify-content: space-between;
            margin-bottom: 55px;
            gap: 40px;
        }
        
        .client-info, .invoice-meta {
            width: 46%;
            padding: 25px;
            background: #fafafa;
            border: 1px solid #e5e7eb;
            position: relative;
        }
        
        .info-accent {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #d97706, #f59e0b);
        }
        
        .section-title {
            font-size: 14px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            color: #1f2937;
            margin-bottom: 15px;
            border-bottom: 1px solid #d97706;
            padding-bottom: 8px;
            font-family: 'Source Sans Pro', sans-serif;
        }
        
        .client-name {
            font-size: 20px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 12px;
        }
        
        .client-details, .invoice-details {
            font-family: 'Source Sans Pro', sans-serif;
            color: #4b5563;
            font-size: 13px;
            line-height: 1.6;
        }
        
        .invoice-number {
            font-size: 22px;
            font-weight: 600;
            color: #d97706;
            margin-bottom: 12px;
        }
        
        /* Line Items Table */
        .line-items {
            margin-bottom: 40px;
        }
        
        .items-table {
            width: 100%;
            border-collapse: collapse;
            border: 2px solid #1f2937;
        }
        
        .items-table th {
            background: #1f2937;
            color: white;
            padding: 18px 15px;
            text-align: left;
            font-size: 13px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-family: 'Source Sans Pro', sans-serif;
            border-right: 1px solid #374151;
        }
        
        .items-table th:last-child {
            border-right: none;
        }
        
        .items-table td {
            padding: 16px 15px;
            border-bottom: 1px solid #e5e7eb;
            border-right: 1px solid #e5e7eb;
            color: #1f2937;
            font-family: 'Source Sans Pro', sans-serif;
        }
        
        .items-table td:last-child {
            border-right: none;
        }
        
        .items-table tr:nth-child(even) {
            background: #f9fafb;
        }
        
        .text-right {
            text-align: right;
        }
        
        .item-description {
            font-weight: 500;
            font-family: 'Playfair Display', serif;
        }
        
        /* Totals Section */
        .totals-section {
            display: flex;
            justify-content: flex-end;
            margin-bottom: 55px;
        }
        
        .totals {
            width: 350px;
            border: 2px solid #1f2937;
            background: white;
        }
        
        .total-row {
            display: flex;
            justify-content: space-between;
            padding: 15px 25px;
            font-size: 14px;
            font-family: 'Source Sans Pro', sans-serif;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .total-row:last-child {
            border-bottom: none;
        }
        
        .total-row.subtotal {
            color: #6b7280;
            background: #f9fafb;
        }
        
        .total-row.final {
            font-size: 20px;
            font-weight: 700;
            color: white;
            background: linear-gradient(135deg, #1f2937, #374151);
            font-family: 'Playfair Display', serif;
        }
        
        /* Footer */
        .footer {
            text-align: center;
            color: #4b5563;
            font-size: 12px;
            line-height: 1.6;
            font-family: 'Source Sans Pro', sans-serif;
            border-top: 1px solid #d1d5db;
            padding-top: 25px;
            position: relative;
        }
        
        .footer::before {
            content: '';
            position: absolute;
            top: -2px;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 3px;
            background: linear-gradient(90deg, #d97706, #f59e0b, #d97706);
        }
        
        .payment-terms {
            margin-bottom: 20px;
            font-weight: 600;
            color: #1f2937;
            font-size: 14px;
        }
        
        .footer-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .footer-item {
            text-align: center;
            padding: 15px;
            background: #fafafa;
            border: 1px solid #e5e7eb;
        }
        
        .footer-label {
            font-weight: 600;
            color: #d97706;
            display: block;
            margin-bottom: 5px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        /* Print Styles */
        @media print {
            body {
                font-size: 12px;
            }
            
            .invoice-container {
                padding: 30px;
                border: 1px solid #1f2937;
            }
            
            .corner-decoration {
                display: none;
            }
            
            .header {
                margin-bottom: 30px;
                padding: 20px 0;
            }
            
            .invoice-title {
                font-size: 36px;
                margin-bottom: 30px;
            }
            
            .invoice-title::before,
            .invoice-title::after {
                display: none;
            }
            
            .info-section {
                margin-bottom: 35px;
            }
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .invoice-container {
                padding: 30px 20px;
            }
            
            .corner-decoration {
                display: none;
            }
            
            .header {
                padding: 20px 0;
            }
            
            .company-name {
                font-size: 24px;
            }
            
            .invoice-title {
                font-size: 32px;
            }
            
            .invoice-title::before,
            .invoice-title::after {
                display: none;
            }
            
            .info-section {
                flex-direction: column;
                gap: 20px;
            }
            
            .client-info, .invoice-meta {
                width: 100%;
            }
            
            .totals {
                width: 100%;
            }
            
            .footer-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }
            
            .items-table th,
            .items-table td {
                padding: 12px 8px;
                font-size: 12px;
            }
        }
    </style>
</head>
<body>
    <div class="invoice-container">
        <!-- Corner Decorations -->
        <div class="corner-decoration top-left"></div>
        <div class="corner-decoration top-right"></div>
        <div class="corner-decoration bottom-left"></div>
        <div class="corner-decoration bottom-right"></div>
        
        <!-- Decorative Line -->
        <div class="decorative-line"></div>
        
        <!-- Header -->
        <div class="header">
            {% if company.logo %}
                <img src="{{ company.logo }}" alt="{{ company.company_name }}" class="company-logo">
            {% endif %}
            <div class="company-name">{{ company.company_name }}</div>
            <div class="company-tagline">Excellence in Service</div>
            <div class="company-details">
                {% if company.address_line_1 %}{{ company.address_line_1 }}{% endif %}{% if company.address_line_2 %}, {{ company.address_line_2 }}{% endif %}<br>
                {% if company.city %}{{ company.city }}{% endif %}{% if company.state_province %}, {{ company.state_province }}{% endif %}{% if company.postal_code %} {{ company.postal_code }}{% endif %}{% if company.country %}, {{ company.country }}{% endif %}<br>
                {% if company.phone %}{{ company.phone }}{% endif %}{% if company.email %} • {{ company.email }}{% endif %}<br>
                {% if company.website %}{{ company.website }}{% endif %}
            </div>
        </div>
        
        <!-- Invoice Title -->
        <h1 class="invoice-title">Invoice</h1>
        
        <!-- Client and Invoice Info -->
        <div class="info-section">
            <div class="client-info">
                <div class="info-accent"></div>
                <div class="section-title">Bill To</div>
                <div class="client-name">{{ client.name }}</div>
                <div class="client-details">
                    {% if client.address %}{{ client.address }}<br>{% endif %}
                    {% if client.email %}{{ client.email }}<br>{% endif %}
                    {% if client.phone %}{{ client.phone }}{% endif %}
                </div>
            </div>
            <div class="invoice-meta">
                <div class="info-accent"></div>
                <div class="section-title">Invoice Details</div>
                <div class="invoice-number">#{{ invoice.number }}</div>
                <div class="invoice-details">
                    <strong>Invoice Date:</strong> {{ invoice.date }}<br>
                    <strong>Due Date:</strong> {{ invoice.due_date }}<br>
                    {% if invoice.po_number %}<strong>Purchase Order:</strong> {{ invoice.po_number }}<br>{% endif %}
                    <strong>Payment Terms:</strong> {{ company.default_payment_terms|default:"Net 30 days" }}
                </div>
            </div>
        </div>
        
        <!-- Line Items -->
        <div class="line-items">
            <table class="items-table">
                <thead>
                    <tr>
                        <th>Description</th>
                        <th class="text-right">Quantity</th>
                        <th class="text-right">Rate</th>
                        <th class="text-right">Amount</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in invoice.items %}
                    <tr>
                        <td class="item-description">{{ item.description }}</td>
                        <td class="text-right">{{ item.quantity }}</td>
                        <td class="text-right">${{ item.rate|floatformat:2 }}</td>
                        <td class="text-right">${{ item.amount|floatformat:2 }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- Totals -->
        <div class="totals-section">
            <div class="totals">
                <div class="total-row subtotal">
                    <span>Subtotal:</span>
                    <span>${{ invoice.subtotal|floatformat:2 }}</span>
                </div>
                {% if invoice.tax_amount %}
                <div class="total-row">
                    <span>Tax ({{ invoice.tax_rate }}%):</span>
                    <span>${{ invoice.tax_amount|floatformat:2 }}</span>
                </div>
                {% endif %}
                {% if invoice.discount_amount %}
                <div class="total-row">
                    <span>Discount:</span>
                    <span>-${{ invoice.discount_amount|floatformat:2 }}</span>
                </div>
                {% endif %}
                <div class="total-row final">
                    <span>Total Due:</span>
                    <span>${{ invoice.total|floatformat:2 }}</span>
                </div>
            </div>
        </div>
        
        <!-- Footer -->
        <div class="footer">
            <div class="payment-terms">
                Payment Terms: {{ company.default_payment_terms|default:"Net 30 days" }}
            </div>
            <div class="footer-grid">
                {% if company.bank_name %}
                <div class="footer-item">
                    <span class="footer-label">Banking Information</span>
                    {{ company.bank_name }}<br>
                    {% if company.account_number %}Account: {{ company.account_number }}<br>{% endif %}
                    {% if company.routing_number %}Routing: {{ company.routing_number }}<br>{% endif %}
                    {% if company.swift_code %}SWIFT: {{ company.swift_code }}{% endif %}
                </div>
                {% endif %}
                {% if company.tax_id %}
                <div class="footer-item">
                    <span class="footer-label">Tax Information</span>
                    Tax ID: {{ company.tax_id }}
                </div>
                {% endif %}
                {% if company.business_registration %}
                <div class="footer-item">
                    <span class="footer-label">Business Registration</span>
                    {{ company.business_registration }}
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</body>
</html>
