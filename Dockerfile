# Dockerfile
FROM node:18-alpine AS builder
WORKDIR /app

# Install deps with dev dependencies for build
COPY package*.json ./
RUN npm install

# Copy config files first
COPY jsconfig.json ./
COPY next.config.mjs ./
COPY postcss.config.mjs ./
COPY tailwind.config.js ./

# Copy all source files
COPY . .

# Build the application
RUN npm run build

# Runtime image
FROM node:18-alpine AS runner
WORKDIR /app

# Copy necessary files from builder
COPY --from=builder /app/package*.json ./
COPY --from=builder /app/.next ./.next
COPY --from=builder /app/public ./public
COPY --from=builder /app/next.config.mjs ./
COPY --from=builder /app/jsconfig.json ./
COPY --from=builder /app/src ./src

# Install only production dependencies
RUN npm install --only=production

EXPOSE 3000
ENV PORT 3000
ENV HOSTNAME "0.0.0.0"
ENV NODE_ENV "production"

CMD ["npm", "start"]
