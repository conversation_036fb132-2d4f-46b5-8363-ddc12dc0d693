#!/bin/bash

# Function to display usage information
show_usage() {
    echo "Usage: $0 [start|stop|restart|logs|build|build_no_cache|clean]"
    echo "  start   - Start the Docker containers"
    echo "  stop    - Stop the Docker containers"
    echo "  restart - Restart the Docker containers"
    echo "  logs    - Show logs from the containers"
    echo "  build   - Rebuild the Docker containers"
    echo "  build_no_cache   - Rebuild the Docker containers with no cache"
    echo "  clean   - Remove all Docker containers, volumes, and images associated with this project"
    exit 1
}

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "Docker is not installed. Please install Docker first."
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    echo "Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Check if an argument was provided
if [ $# -eq 0 ]; then
    show_usage
fi

# Process the command
case "$1" in
    start)
        echo "Starting Docker containers..."
        docker-compose -p mizu up -d
        echo "Containers started. The application is available at http://localhost:8005"
        ;;
    stop)
        echo "Stopping Docker containers..."
        docker-compose -p mizu down
        echo "Containers stopped."
        ;;
    restart)
        echo "Restarting Docker containers..."
        docker-compose -p mizu down
        docker-compose -p mizu up -d
        echo "Containers restarted. The application is available at http://localhost:8005"
        ;;
    logs)
        echo "Showing logs from Docker containers..."
        docker-compose -p mizu logs -f
        ;;
    build)
        echo "Rebuilding Docker containers..."
        docker-compose -p mizu down
        docker-compose -p mizu build
        docker-compose -p mizu up -d
        echo "Containers rebuilt and started. The application is available at http://localhost:8005"
        ;;
    build_no_cache)
        echo "Rebuilding Docker containers with no cache..."
        docker-compose -p mizu down
        docker-compose -p mizu build --no-cache
        docker-compose -p mizu up -d
        echo "Containers rebuilt and started. The application is available at http://localhost:8005"
        ;;
    clean)
        echo "Removing all Docker containers, volumes, and images associated with this project..."
        # Stop and remove containers
        docker-compose -p mizu down --volumes
        
        # Remove all containers with mizu_ prefix
        echo "Removing any remaining containers with mizu_ prefix..."
        containers=$(docker ps -a --filter "name=mizu_" -q)
        if [ -n "$containers" ]; then
            docker rm -f $containers
        fi
        
        # Remove all volumes with mizu_ prefix
        echo "Removing volumes with mizu_ prefix..."
        volumes=$(docker volume ls --filter "name=mizu_" -q)
        if [ -n "$volumes" ]; then
            docker volume rm $volumes
        fi
        
        # Remove all images built for this project
        echo "Removing images built for this project..."
        images=$(docker images --filter "reference=mizu_*" -q)
        if [ -n "$images" ]; then
            docker rmi $images
        fi
        
        echo "Clean up completed. All Docker resources associated with this project have been removed."
        ;;
    *)
        show_usage
        ;;
esac 