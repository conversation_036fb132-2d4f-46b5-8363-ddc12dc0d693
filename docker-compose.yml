services:
  db:
    image: mysql:8.0
    container_name: mizu_mysql
    restart: always
    environment:
      - MYSQL_DATABASE=${DB_NAME}
      - MYSQL_USER=${DB_USER}
      - MYSQL_PASSWORD=${DB_PASSWORD}
      - MYSQL_ROOT_PASSWORD=${DB_PASSWORD}
    ports:
      - "3307:3306"
    volumes:
      - ./data/mysql/db:/var/lib/mysql
    command: --default-authentication-plugin=mysql_native_password --wait_timeout=28800 --interactive_timeout=28800 --max_allowed_packet=128M
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 5s
      retries: 10

  mizu_web:
    build: .
    container_name: mizu_web
    volumes:
      - ./static:/app/static
      - static_volume:/app/staticfiles
      - media_volume:/app/media
      - ./invoices_data:/app/invoices_data
      - ./logs:/app/logs
    environment:
      - DJANGO_SETTINGS_MODULE=config.settings
      - DB_HOST=db
      - DB_NAME=${DB_NAME}
      - DB_USER=${DB_USER}
      - DB_PASSWORD=${DB_PASSWORD}
      - DB_PORT=3306
    command: >
      bash -c "rm -rf /app/staticfiles/* &&
              python manage.py collectstatic --noinput --clear &&
              python manage.py migrate &&
              python manage.py createcachetable &&
              gunicorn --config gunicorn_config.py config.wsgi:application"
    restart: always
    depends_on:
      db:
        condition: service_healthy

  mizu_nginx:
    image: nginx:alpine
    container_name: mizu_nginx
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/conf.d/default.conf:ro
      - static_volume:/app/staticfiles
      - media_volume:/app/media
    ports:
      - "8005:8005"
    depends_on:
      - mizu_web
    restart: always

volumes:
  static_volume:
  media_volume: 