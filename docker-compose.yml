version: "3.8"

services:
  nextjs:
    build:
      context: .
      dockerfile: Dockerfile
    environment:
      - NODE_ENV=production
      - PORT=3000
      - HOSTNAME=0.0.0.0
    ports:
      - "3000:3000"
    healthcheck:
      test: ["CMD", "wget", "--spider", "-q", "http://localhost:3000"]
      interval: 10s
      timeout: 5s
      retries: 3
    image: mizu/demo-nextjs:latest
    container_name: mizu-demo-nextjs
    restart: always
    networks:
      - app-network

  nginx:
    build:
      context: ./nginx
      dockerfile: Dockerfile
    image: mizu/demo-next-nginx:latest
    container_name: mizu-demo-nextjs-nginx
    ports:
      - "3005:80"
    depends_on:
      nextjs:
        condition: service_healthy
    restart: always
    networks:
      - app-network

networks:
  app-network:
    driver: bridge
