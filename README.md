# MizuFlow Backend

A Django REST Framework project with a modular structure for accounting and financial management services.

## Project Structure

```
project_root/
├── apps/
│   ├── users/              # User management and authentication
│   ├── invoice_automation/ # Automated invoice processing
│   ├── invoice_generator/  # Invoice generation and management
│   └── tax_acc_assistant/  # Tax and accounting assistance
├── config/                 # Project configuration
│   ├── settings.py
│   ├── urls.py
│   └── ...
├── manage.py
└── requirements.txt
```

## Setup and Installation

1. Clone the repository
2. Create a virtual environment:
   ```bash
   python -m venv env
   source env/bin/activate  # On Windows: env\Scripts\activate
   ```
3. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```
4. Apply migrations:
   ```bash
   python manage.py makemigrations
   python manage.py migrate
   ```
5. Create a superuser:
   ```bash
   python manage.py createsuperuser
   ```
6. Run the development server:
   ```bash
   python manage.py runserver
   ```

## API Endpoints

### Authentication
- `POST /api/v1/users/token/`: Obtain JWT token pair by providing valid credentials
- `POST /api/v1/users/token/refresh/`: Refresh access token using refresh token
- `POST /api/v1/users/token/verify/`: Verify token validity

### User Management
- `POST /api/v1/users/register/`: Register a new user
- `GET /api/v1/users/`: List all users (requires authentication)
- `GET /api/v1/users/me/`: Get current user details (requires authentication)
- `GET/PUT /api/v1/users/profile/`: Get or update user details (requires authentication)

## Authentication

The API uses JWT (JSON Web Token) authentication. 

### JWT Authentication Flow:

1. Register a new user with the `/api/v1/users/register/` endpoint
2. Obtain an access token and refresh token by posting valid credentials to `/api/v1/users/token/`
3. Include the access token in the Authorization header for protected endpoints:
   ```
   Authorization: Bearer <your_access_token>
   ```
4. When the access token expires, use the refresh token to get a new access token:
   ```
   POST /api/v1/users/token/refresh/
   {
     "refresh": "<your_refresh_token>"
   }
   ```

## Running Tests

To run the test suite:
```bash
python manage.py test
```

## Technologies Used

- Django 5.2
- Django REST Framework 3.16.0
- djangorestframework-simplejwt 5.5.0
- SQLite (default database) 

## Docker Setup

### Prerequisites
- Docker
- Docker Compose

### Environment Setup
Create a `.env` file in the project root with the following variables:
```
# Database settings
DB_NAME=mizu_db
DB_USER=mizu_user
DB_PASSWORD=mizu_password

# Django settings
DEBUG=False
SECRET_KEY=your_secret_key_here
ALLOWED_HOSTS=localhost,127.0.0.1
```

### Running with Docker
Use the `docker-run.sh` script to manage Docker containers:

```bash
# Make the script executable
chmod +x docker-run.sh

# Build and start containers
./docker-run.sh build

# Start containers
./docker-run.sh start

# Stop containers
./docker-run.sh stop

# Restart containers
./docker-run.sh restart

# View logs
./docker-run.sh logs

# Clean up all Docker resources
./docker-run.sh clean
```

The application will be available at http://localhost:8005

### Architecture
- MySQL database running on port 3307
- Nginx container serving the application on port 8005
- Gunicorn running the Django application

### Volumes
- MySQL data: `./data/mysql/db:/var/lib/mysql`
- Static files: `static_volume:/app/staticfiles`
- Media files: `media_volume:/app/media`

### VPS Deployment
For VPS deployment, the host's Nginx can be configured to reverse proxy to the Docker Nginx container:

```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://localhost:8005;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
``` 