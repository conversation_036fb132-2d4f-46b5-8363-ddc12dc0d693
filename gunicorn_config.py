"""
Gunicorn configuration file
"""

import multiprocessing

# Bind to 0.0.0.0:8004
bind = "0.0.0.0:8004"

# Number of worker processes
workers = multiprocessing.cpu_count() * 2 + 1

# Worker class
worker_class = "gthread"
threads = 2
worker_connections = 1000

# Timeout (seconds)
timeout = 120

# Log level
loglevel = "info"

# Access log format
accesslog = "-"

# Error log
errorlog = "-"

# Reload when code changes (not recommended for production)
reload = False
