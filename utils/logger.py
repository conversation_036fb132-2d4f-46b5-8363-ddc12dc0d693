import os
import logging
import traceback
from pathlib import Path
from datetime import datetime
from logging.handlers import RotatingFileHandler

try:
    from django.conf import settings

    BASE_DIR = settings.BASE_DIR
except (ImportError, AttributeError):
    # Fallback if Django settings are not available
    BASE_DIR = Path(__file__).resolve().parent.parent

# Define logger name constants
INVOICE_LOGGER = "invoice_automation"
TAX_ACC_LOGGER = "tax_acc_assistant"


def get_logger(name, level=logging.INFO):
    """
    Create a logger for the given name with proper formatting.
    Logs will be stored in logs/{app_name}/{app_name}.log with rotation

    Args:
        name: The name of the logger, usually in format 'app_name.module'

    Returns:
        A configured logger instance
    """
    logger = logging.getLogger(name)

    # If the logger has handlers already, it's configured
    # Return it without adding new handlers to prevent duplication
    if logger.handlers:
        return logger

    # Set logger level
    logger.setLevel(level)

    # Set propagate to False to prevent duplicate messages
    logger.propagate = False

    # Extract app name from the logger name
    app_name = name.split(".")[0] if "." in name else name

    # Create logs directory if it doesn't exist
    logs_dir = Path(BASE_DIR) / "logs" / app_name
    logs_dir.mkdir(parents=True, exist_ok=True)

    # Create a rotating file handler (max 10MB per file, keep 10 backup files)
    log_file = logs_dir / f"{app_name}.log"
    file_handler = RotatingFileHandler(
        log_file, maxBytes=10 * 1024 * 1024, backupCount=10  # 10 MB
    )

    # Create console handler
    console_handler = logging.StreamHandler()

    # Set formatter
    formatter = logging.Formatter(
        "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)

    # Set log level for handlers
    file_handler.setLevel(level)
    console_handler.setLevel(level)

    # Add handlers to logger
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)

    return logger


def log_exception(logger, exception, context=None):
    """
    Log an exception with a stack trace and optional context

    Args:
        logger: The logger instance to use
        exception: The exception to log
        context: Optional dictionary with context data
    """
    # Get the stack trace
    stack_trace = traceback.format_exc()

    # Create the error message
    error_message = f"Exception: {str(exception)}\n"

    # Add context if provided
    if context:
        error_message += "Context:\n"
        for key, value in context.items():
            error_message += f"  {key}: {value}\n"

    # Add stack trace
    error_message += f"\nStack Trace:\n{stack_trace}"

    # Log the error
    logger.error(error_message)
